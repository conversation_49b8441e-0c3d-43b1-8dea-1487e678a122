package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetNotEmptyDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.Map;

public class NotEmptyHandler extends ExcelHandleRuler {
    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetNotEmptyDto condition = (SheetNotEmptyDto) param;
        Sheet sheet = condition.getSheet();
        int notEmptyColumn = condition.getColumn();

        boolean res = true;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);
            Cell oneRowTargetCell = oneRow.getCell(notEmptyColumn);
            setCellStr(oneRowTargetCell);
            String cellStrVal = oneRowTargetCell.getStringCellValue();
            if (cellStrVal.isEmpty() || cellStrVal.trim().isEmpty()) {
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }
        }
        return res ? success() : fail();
    }
}
