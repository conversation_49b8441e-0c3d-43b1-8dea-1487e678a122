package com.suke.czx.modules.aitrain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 根据sql生成文件接收到的前端参数信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GenerateFileFromSqlVO {

    /**
     * 动态数据源的id
     */
    private String runDatasourceId;

    /**
     * 要执行的sql语句
     */
    private String sqlText;

    /**
     * 文件的类型，是正常训练数据文件or异常训练数据文件
     * 文件类型。0代表正常，1代表异常
     */
    private String fileType;
}
