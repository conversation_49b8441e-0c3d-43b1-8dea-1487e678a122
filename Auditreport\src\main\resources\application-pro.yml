spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: oracle.jdbc.driver.OracleDriver
        dbType: oracle
        druid:

            first:  #数据源1
                #                云库
                url: *********************************************
                username: <PERSON><PERSON>(yk2wcJR8ebovVIoq3OoXhFxPJFne7hBh)
                password: E<PERSON>(40meTkZ4ggNJ6nAJucnyPYneaOtRP/nN)
                second: #数据源2
                    #                云库
                    url: *********************************************
                    username: <PERSON><PERSON>(yk2wcJR8ebovVIoq3OoXhFxPJFne7hBh)
                    password: <PERSON><PERSON>(40meTkZ4ggNJ6nAJucnyPYneaOtRP/nN)

            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true

# 文件上传的服务器信息配置
ftp:
    host: *************
    port: 22
    user: ENC(+1d5Ta9Ckr1NvINC4YP7zUuSldd81h/X)
    password: ENC(t2wjs+WAdqOwqSBG2vi+DTti5hiGiDbA)
    basePath: /home/<USER>/audit_report_tools/
    comPath: /home/<USER>/audit_report_tools/comparison
    rulePath: /home/<USER>/audit_report_tools/rule
    aiPath: /home/<USER>/audit_report_tools/rule
    pythonbasePath: /home/<USER>/audit_report_tools/Audit_AI/pythonProject1
    #    输出文件的位置
    logPath: /home/<USER>/audit_report_tools/
    logName: nohup.out
    containerName: audit_project