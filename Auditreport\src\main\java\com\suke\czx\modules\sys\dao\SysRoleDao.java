package com.suke.czx.modules.sys.dao;

import com.suke.czx.modules.sys.entity.SysRoleEntity;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * 角色管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:33:33
 */
@Mapper
public interface SysRoleDao extends BaseDao<SysRoleEntity> {
	
	/**
	 * 查询用户创建的角色ID列表
	 */
	List<Long> queryRoleIdList(Long createUserId);

	/**
	 * 根据角色名称查询是否已经存在解决名称
	 * @param roleName
	 * @return
	 */
    Integer queryTotalByRoleName(String roleName);

	List<String> qryRoleNameByRoleIds(Set<String> roleIds);
}
