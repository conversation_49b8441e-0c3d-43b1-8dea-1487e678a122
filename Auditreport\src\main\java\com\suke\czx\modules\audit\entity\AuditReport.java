package com.suke.czx.modules.audit.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报表信息类
 */
@Data
public class AuditReport {

    /**
     * id
     */
    private Long id;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 报表大小
     */
    private Long reportSize;

    /**
     * 报表类型
     */
    private String type;

    /**
     * 报表文件后缀
     */
    private String suffix;

    /**
     * 报表下载url
     */
    private String url;

    /**
     * 上传人id
     */
    private String uploadUserId;

    /**
     * 报表是否删除，默认0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;
}
