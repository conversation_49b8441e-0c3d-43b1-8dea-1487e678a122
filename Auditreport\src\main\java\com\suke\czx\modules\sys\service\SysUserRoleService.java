package com.suke.czx.modules.sys.service;

import java.util.List;



/**
 * 用户与角色对应关系
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:43:24
 */
public interface SysUserRoleService {
	
	void saveOrUpdate(Long userId, List<Long> roleIdList);
	
	/**
	 * 根据用户ID，获取角色ID列表
	 */
	List<Long> queryRoleIdList(String userId);
	
	void delete(String userId);

	//根据角色id查询用户角色关联表，查询是否存在数据，若存在则不允许删除
    Integer queryRoleById(Long id);

	/**
	 * 保存用户的角色信息
	 * @param userId
	 * @param roleIds
	 */
	void saveUserRole(String userId, Long[] roleIds);
}
