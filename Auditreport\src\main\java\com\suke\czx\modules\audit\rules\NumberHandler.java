package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetNumberDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.Map;

public class NumberHandler extends ExcelHandleRuler {
    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetNumberDto condition = (SheetNumberDto) param;
        Sheet sheet = condition.getSheet();
        String rule = condition.getRule();
        String[] ruleArr = rule.split(",");
        int intLengthRule = Integer.parseInt(ruleArr[0]);
        int floatLengthRule = Integer.parseInt(ruleArr[1]);
        int numberColumn = condition.getColumn();

        boolean res = true;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);
            Cell oneRowTargetCell = oneRow.getCell(numberColumn);
            if (!isCellNumeric(oneRowTargetCell)) {
                setCellComment(sheet, oneRowTargetCell, "该单元格内容非数字类型");
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
                continue;
            }
            setCellStr(oneRowTargetCell);
            String cellStrVal = oneRowTargetCell.getStringCellValue();
            if (!isCellStrMatchRule(cellStrVal, intLengthRule, floatLengthRule)) {
                res = false;
                setCellRed(oneRowTargetCell, condition.getCellStyle());
            }
        }
        return res ? success() : fail();
    }


    private boolean isCellStrMatchRule(String cellStr, int intLengthRule, int floatLengthRule) {
        if (cellStr == null || cellStr.isEmpty()) {
            return false;
        }
        int index = cellStr.indexOf('.');

        if (index != -1) {
            String intStr = cellStr.substring(0, index);
            String floatStr = cellStr.substring(index + 1);
            return intStr.length() <= intLengthRule && floatStr.length() <= floatLengthRule;
        } else if (cellStr.length() > intLengthRule) {
            return false;
        }
        return true;
    }

}
