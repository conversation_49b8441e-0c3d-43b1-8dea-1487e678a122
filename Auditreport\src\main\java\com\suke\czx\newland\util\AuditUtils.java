package com.suke.czx.newland.util;

import com.suke.czx.newland.bpm.camunda.tasks.service.AuditThread;

public class AuditUtils {

    private static int stauts = -1; //稽核状态  -1:没有稽核 0:正在稽核  1:稽核成功  2:终止稽核

    private static AuditThread execThread = null;


    public static int getStauts() {
        return stauts;
    }

    public static void setStauts(int stauts) {
        AuditUtils.stauts = stauts;
    }

    public static AuditThread getExecThread() {
        return execThread;
    }

    public static void setExecThread(AuditThread execThread) {
        AuditUtils.execThread = execThread;
    }
}
