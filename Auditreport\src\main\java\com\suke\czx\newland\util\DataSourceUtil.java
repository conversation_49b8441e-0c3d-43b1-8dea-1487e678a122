package com.suke.czx.newland.util;

import com.alibaba.fastjson.JSONObject;
import com.suke.czx.common.utils.CDESCrypt;
import com.suke.czx.datasources.DynamicDataSource;
import com.suke.czx.datasources.GenConfig;
import com.suke.czx.newland.dao.mgrdbmapper.DatasourceDefMapper;
import com.suke.czx.newland.dao.mgrdbmapper.SystemMapper;
import com.suke.czx.newland.dto.DatasourceInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.suke.czx.common.utils.AppBaseResult.KEY;

@Slf4j
@Component
public class DataSourceUtil {

    private final DatasourceDefMapper datasourceDefMapper;

    private final SystemMapper systemMapper;

    private final DynamicDataSource dynamicDataSource;

    @Value("${spring.datasource.druid.first.url}")
    private String url;
    @Value("${spring.datasource.druid.first.username}")
    private String username;
    @Value("${spring.datasource.druid.first.password}")
    private String password;
    @Value("${spring.datasource.driverClassName}")
    private String driverClassName;
    @Value("${spring.datasource.dbType}")
    private String dbType;


    public DataSourceUtil(DatasourceDefMapper datasourceDefMapper, SystemMapper systemMapper, DynamicDataSource dynamicDataSource) {
        this.datasourceDefMapper = datasourceDefMapper;
        this.systemMapper = systemMapper;
        this.dynamicDataSource = dynamicDataSource;
    }

    private static final Map<String, String> DATA_SOURCE_MAP = new HashMap<>();

    static {
        DATA_SOURCE_MAP.put("mysql", "com.mysql.cj.jdbc.Driver");
        DATA_SOURCE_MAP.put("oracle", "oracle.jdbc.driver.OracleDriver");
        DATA_SOURCE_MAP.put("postgresql", "org.postgresql.Driver");
    }

    /**
     * 根据请求数据源名称以及sql进行指定数据库的查询
     *
     * @param dataSourceId 数据源名称
     * @param sql          执行sql
     * @param flag         是否切换回默认数据源 true 切换默认数据源 false 不切换
     * @return
     */
    public JSONObject executeSql(String dataSourceId, String sql, Boolean flag) {
        //根据数据库名称查询数据库信息
        List<JSONObject> jsonObjects = doExecuteSql(dataSourceId, sql, flag);
        return jsonObjects == null ? new JSONObject() : jsonObjects.get(0);
    }

    public List<JSONObject> executeSqlGetAll(String dataSourceId, String sql, Boolean flag) {
        //根据数据库名称查询数据库信息
        return doExecuteSql(dataSourceId, sql, flag);
    }

    private List<JSONObject> doExecuteSql(String dataSourceId, String sql, Boolean flag) {
        DatasourceInfoDto dataBaseInfoBean = datasourceDefMapper.qryDatasourceInfoByDatasourceId(dataSourceId);
        //自行捕获异常进行处理
        if (dataBaseInfoBean == null) {
            throw new RuntimeException("根据数据库名称查询不到数据库配置，请新增数据库配置信息");
        }
        List<JSONObject> jsonObjects = null;

        String dbInfo = url.substring(url.indexOf("@") + 1);

        if (dataBaseInfoBean.getUrl().contains(dbInfo)) {
            jsonObjects = systemMapper.executeSqlForJSONObj(sql);
        } else {
            GenConfig genConfig = new GenConfig();
            try {
                genConfig.setUrl(dataBaseInfoBean.getUrl());
                genConfig.setUsername(CDESCrypt.decryptString(dataBaseInfoBean.getDatasourceUsername(), KEY));
                genConfig.setDbtype(dataBaseInfoBean.getDatasourceType());
                genConfig.setPassword(CryptoUtil.decrypt(dataBaseInfoBean.getDatasourcePassword()));
                genConfig.setDriverClassName(getDriver(dataBaseInfoBean.getDatasourceType()));
                dynamicDataSource.changeDataSource(genConfig);
                log.info("当前数据源:{}", dynamicDataSource.getConnection());

                //执行sql
                jsonObjects = systemMapper.executeSqlForJSONObj(sql);

            } catch (Exception e) {
                throw new RuntimeException(e);
            }finally {
                if (flag) {
                    //切换至默认数据源
                    genConfig.setUrl(url);
                    genConfig.setUsername(username);
                    genConfig.setDbtype(dbType);
                    genConfig.setPassword(password);
                    genConfig.setDriverClassName(driverClassName);
                    dynamicDataSource.changeDataSource(genConfig);
                }
            }

        }
        return jsonObjects;
    }

    /**
     * 根据请求数据源名称以及sql进行指定数据库的查询
     *
     * @param dataSourceId 数据源名称
     * @param sql          执行sql
     * @param flag         是否切换回默认数据源 true 切换默认数据源 false 不切换
     * @return
     */
    public List<JSONObject> executeSqlReturnAll(String dataSourceId, String sql, Boolean flag) {
        //根据数据库名称查询数据库信息
        return doExecuteSql(dataSourceId, sql, flag);
    }

    public void changeToTargetDataSource(String dataSourceId) {
        //如果出行数据源id 则到指定环境执行查询
        if (dataSourceId != null &&!dataSourceId.isEmpty()){
            //根据数据库名称查询数据库信息
            DatasourceInfoDto dataBaseInfoBean = datasourceDefMapper.qryDatasourceInfoByDatasourceId(dataSourceId);
            //自行捕获异常进行处理
            if (dataBaseInfoBean == null) {
                throw new RuntimeException("根据数据库名称查询不到数据库配置，请新增数据库配置信息");
            }
            GenConfig genConfig = new GenConfig();
            try {
                genConfig.setUrl(dataBaseInfoBean.getUrl());
                genConfig.setUsername(CDESCrypt.decryptString(dataBaseInfoBean.getDatasourceUsername(), KEY));
                genConfig.setDbtype(dataBaseInfoBean.getDatasourceType());
                genConfig.setPassword(CryptoUtil.decrypt(dataBaseInfoBean.getDatasourcePassword()));
                genConfig.setDriverClassName(getDriver(dataBaseInfoBean.getDatasourceType()));
                dynamicDataSource.changeDataSource(genConfig);
            }catch (Exception e){
                log.error("数据库连接失败，请检查数据库配置信息,异常信息：{}",e.getMessage());
                throw new RuntimeException("数据库连接失败，请检查数据库配置信息");
            }
        }
    }

    public void changeDefaultDataSource() {
        //切换至默认数据源
        GenConfig genConfig = new GenConfig();
        genConfig.setUrl(url);
        genConfig.setUsername(username);
        genConfig.setDbtype(dbType);
        genConfig.setPassword(password);
        genConfig.setDriverClassName(driverClassName);
        dynamicDataSource.changeDataSource(genConfig);
    }


    public static String getDriver(String type) {
        return String.valueOf(DATA_SOURCE_MAP.get(type.toLowerCase()));
    }
}
