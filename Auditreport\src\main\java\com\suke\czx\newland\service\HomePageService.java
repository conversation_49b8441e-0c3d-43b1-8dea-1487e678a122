package com.suke.czx.newland.service;


import com.suke.czx.newland.dto.AuditItemDto;
import com.suke.czx.newland.vo.echartstable.AuditItemCheckLogTableVo;


import java.time.LocalDate;
import java.util.List;

/*
主页面 图表接口
 */
public interface HomePageService {
    /*
    查询具体那个时间点的前x日内的数据
     */
    List<AuditItemDto> qryAuditItemCreateInfoWithDuration(int duration);


    List<AuditItemDto> qryAuditItemUpdateInfoWithDuration(int duration);


    /*
    查询time时间的check log
     */
    List<AuditItemCheckLogTableVo> qryAuditItemCheckLogInfo(LocalDate time);
}
