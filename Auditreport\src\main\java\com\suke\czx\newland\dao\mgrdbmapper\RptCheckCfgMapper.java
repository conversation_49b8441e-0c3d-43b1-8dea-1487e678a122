package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.AuditItemDto;
import com.suke.czx.newland.po.RptCheckCfgPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface RptCheckCfgMapper {

    List<RptCheckCfgPo> qryAuditCfgWithCondition(RptCheckCfgPo condition);

    List<AuditItemDto> qryRptCheckCfgCreateTimeWithDuration(int duration);
    List<AuditItemDto> qryRptCheckCfUpdateTimeWithDuration(int duration);
}
