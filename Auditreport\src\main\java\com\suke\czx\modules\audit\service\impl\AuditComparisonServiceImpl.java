package com.suke.czx.modules.audit.service.impl;

import com.alibaba.druid.util.Utils;
import com.suke.czx.common.utils.R;
import com.suke.czx.common.utils.ShiroUtils;
import com.suke.czx.modules.audit.dao.AuditComparisonDao;
import com.suke.czx.modules.audit.entity.AuditComparison;
import com.suke.czx.modules.audit.service.AuditComparisonService;
import com.suke.czx.modules.audit.util.FTPUtil;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import com.suke.czx.modules.rule.service.AuditRuleConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
@Slf4j
@Service
public class AuditComparisonServiceImpl implements AuditComparisonService {

    @Autowired
    private AuditComparisonDao auditComparisonDao;

    @Autowired
    private AuditRuleConfigService auditRuleConfigService;

    @Autowired
    private ComparisonService comparisonService;

    /**
     * 比对报表
     *
     * @param fileName
     * @param userId
     * @return
     */
    @Override
    public R comparison(String fileName, String userId, String auditName, HttpServletRequest request, HttpServletResponse response) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = FTPUtil.getFileInputStream(fileName);
            if (byteArrayOutputStream == null) {
                return R.error().put("result", "获取文件流失败:{" + fileName + "}");
            }
            //由于sftp关闭管道时，inputStream流也会随之关闭，所以获取到的InputStream流是空的，因此使用ByteArrayOutputStream获取
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(byteArrayOutputStream.toByteArray());
            //根据传过来的规则父id查询到全部的子规则
            List<AuditRuleConfig> ruleConfigList = auditRuleConfigService.queryRuleConfigList(auditName, null);
            //有可能查询到的规则是空的
            if (ruleConfigList.size() == 0) {
                return R.error().put("result", "规则为空，无法比对!");
            }



            String urlStr = "";
            //比对信息对象
            AuditComparison auditComparison = new AuditComparison();
            //比对状态 0：未比对，1：比对中，2：比对完成，3：出现异常，4：比对异常
            auditComparison.setStatus(0);
            auditComparison.setComparisonUserId(userId);
            auditComparison.setName(fileName);
            if (fileName.contains("%")) {
                urlStr = fileName.replaceAll("%", "%25");
            } else {
                urlStr = fileName;
            }

            //下载地址
            String downloadUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/audit/report/result/downloadFile?fileName=" + urlStr;
            auditComparison.setUrl(downloadUrl);
            log.info("解析前的比对信息：{}", auditComparison);
            //在插入之前先查询表中是否已经存在此名称的比对信息，如果存在的话，先删除再插入
            AuditComparison comparisonInfo = auditComparisonDao.queryByFileName(fileName);
            if (comparisonInfo != null) {
                //进行删除
                Integer finalResult = auditComparisonDao.deleteByFileName(fileName);
                if (finalResult > 0) {
                    log.info("删除成功!");
                } else {
                    log.info("删除失败!");
                }
            }
            //保存对比信息到比对表中
            Integer res = auditComparisonDao.saveComparison(auditComparison);
            //使用CompletableFuture回去异步调用的最终结果
            String finalFileName = fileName;
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    comparisonService.resolveReport(byteArrayInputStream, ruleConfigList, finalFileName, request, response);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                return "落库完成!";
            });
            String s = future.get();
            log.info("正在比对:{}", s);
            return R.ok("落库完成");
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return R.error("获取的报表流为空!");
        }
    }

    /**
     * 获取全部比对信息
     *
     * @return
     */
    @Override
    public List<AuditComparison> queryComparisonList(String name, Integer status, String userId) {
        Map<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("status", status);
        map.put("userId", userId);
        List<AuditComparison> auditComparisonList = auditComparisonDao.queryList(map);
        return auditComparisonList;
    }

    /**
     * 批量导出比对结果
     *
     * @param ids
     */
    @Override
    @Transactional
    public R batchExport(Long[] ids) {
        //获取选中的比对信息
        List<AuditComparison> auditComparisonList = auditComparisonDao.queryListByIds(ids);
        //获取名称
        List<String> urlList = auditComparisonList.stream().map(AuditComparison::getUrl).collect(Collectors.toList());
        if (urlList.size() > 0) {
            return R.ok().put("urlList", urlList);
        } else {
            return R.error("未获取到比对结果");
        }
    }


    /**
     * 删除对比报表
     *
     * @param id
     */
    @Override
    public int deleteBatch(Long id) throws Exception {

        //根据id查询出文件名
        List<AuditComparison> auditComparisonList = auditComparisonDao.queryAuditComparisonById(id);
        List<String> fileNameList = auditComparisonList.stream().map(AuditComparison::getName).collect(Collectors.toList());
        //删除数据中的报表(先删除数据库中的数据，因为遇到异常可以回滚)
        int res = auditComparisonDao.delete(id);
        if (res != 0) {
            for (String item : fileNameList) {
                //删除服务器中的报表
                FTPUtil.delFile(item, 2);
            }
        }
        return res;
    }

    /**
     * 批量删除
     *
     * @param ids
     */
    @Override
    public int delComparisonBatch(Long[] ids) throws Exception {
        //根据id查询出文件名
        List<AuditComparison> auditComparisonList = auditComparisonDao.queryListByIds(ids);
        List<String> fileNameList = auditComparisonList.stream().map(AuditComparison::getName).collect(Collectors.toList());
        //删除数据中的报表(先删除数据库中的数据，因为遇到异常可以回滚)
        int res = auditComparisonDao.deleteBatch(ids);
        if (res != 0) {
            for (String item : fileNameList) {
                //删除服务器中的报表
                FTPUtil.delFile(item, 2);
            }
        }
        return res;
    }

}
