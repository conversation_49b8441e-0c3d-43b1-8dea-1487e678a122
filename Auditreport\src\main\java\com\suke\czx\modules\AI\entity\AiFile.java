package com.suke.czx.modules.AI.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import jnr.constants.platform.PRIO;
import lombok.Data;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AiFile {
    /**
     * id
     */
    private String id;

    /**
     * 报表名称
     */
    private String name;

    /**
     * 报表大小
     */
    private Long reportSize;


    /**
     * ai稽核文件类型（0：原始文件；1：已经被AI稽核过的文件 2:AI稽核结果文件）
     */
    private String type;

    /**
     * 报表下载url
     */
    private String url;

    /**
     * 上传人id
     */
    private String uploadUserId;

    private String sourceFileId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;
}
