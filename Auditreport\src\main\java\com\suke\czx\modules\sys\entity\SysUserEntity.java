package com.suke.czx.modules.sys.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.suke.czx.common.validator.group.AddGroup;
import com.suke.czx.common.validator.group.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 系统用户
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:28:55
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class SysUserEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	
	/**
	 * 用户ID
	 */
	private String userId;

	/**
	 * 用户名
	 */
	@NotBlank(message="用户名不能为空", groups = {AddGroup.class, UpdateGroup.class})
	private String userName;

	/**
	 * 密码
	 */
	@NotBlank(message="密码不能为空", groups = AddGroup.class)
	private String password;

	/**
	 * 盐
	 */
	// private String salt;

	private String pwStr;

	private String platformName;

	private String platformCreateId;

	private String orgId;

	private String orgName;

	private String parentOrgId;

	private String systemId;

	/**
	 * 角色ID列表
	 */
	// private List<Long> roleIdList;

	private List<SysRoleEntity> role;


	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	private String mobile;

}
