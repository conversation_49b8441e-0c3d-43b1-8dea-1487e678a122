package com.suke.czx.thread;

import com.suke.czx.newland.bpm.camunda.tasks.service.AuditThread;
import com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper;
import com.suke.czx.newland.util.ThreadPoolUtil;
import com.suke.czx.newland.vo.audit.AuditConfigVo;
import com.suke.czx.newland.vo.audit.AuditDictVo;
import com.suke.czx.newland.vo.audit.AuditExecVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.stream.Collectors;

public class SchedulingRunnable implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(SchedulingRunnable.class);

    private int dictId;
    private final IAuditCfgMapper iAuditCfgMapper;

    public SchedulingRunnable(int dictId, IAuditCfgMapper iAuditCfgMapper) {
        this.dictId = dictId;
        this.iAuditCfgMapper = iAuditCfgMapper;
    }

    @Override
    public void run() {
        logger.info("定时任务开始执行 - 字典ID：{}", dictId);
        long startTime = System.currentTimeMillis();

        try {
            AuditDictVo auditDictVo = iAuditCfgMapper.qryAllAuditDictByDictId(dictId);
            if (auditDictVo == null){
                return;
            }
            logger.info("定时任务开始执行 - 字典NAME：{}", auditDictVo.getDictName());
            List<AuditConfigVo> auditConfigVos = iAuditCfgMapper.queryRptCheckCfgByRuleIdWithActived(auditDictVo.getDictValue());
            List<AuditExecVo> execVos = auditConfigVos.stream()
                    .map(e -> {
                        AuditExecVo temp = new AuditExecVo();
                        temp.setId((int) e.getRuleId());
                        temp.setName(e.getRuleName());
                        temp.setDatasourceId(String.valueOf(e.getEnvType()));
                        return temp;
                    })
                    .collect(Collectors.toList());
            if (execVos.isEmpty()){
                logger.info("定时任务不存在.");
                return;
            }
            ThreadPoolUtil.getInstance().getProcessPool().execute(new AuditThread(execVos, iAuditCfgMapper, "TimedTaskProgram"));
        } catch (Exception ex) {
            logger.error(String.format("定时任务执行异常 - bean：%s", dictId), ex);
        }

        long times = System.currentTimeMillis() - startTime;
        logger.info("定时任务执行结束 - 字典ID：{}，耗时：{} 毫秒", dictId, times);
    }

    public int getDictId() {
        return dictId;
    }

    public void setDictId(int dictId) {
        this.dictId = dictId;
    }
}
