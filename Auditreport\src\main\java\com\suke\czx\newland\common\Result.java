package com.suke.czx.newland.common;

/**
 * Copyright: Copyright (c) 2020 Newland
 *
 * @ClassName: com.newland.common.Result
 * @Description: 封装返回的对象信息
 * @version: v1.0.0
 * @author: linruibo
 * @date: 2020/6/30 15:47
 * Modification History:
 * Date         Author          Version            Description
 * ------------------------------------------------------------
 * 2020/6/30      linruibo         v1.0.0               修改原因
 */
public class Result
{
    private int code;
    private String msg;
    private Object data;
    private String[] names;

    public int getCode() {
        return code;
    }

    public Result setCode(int code) {
        this.code = code;
        return this;
    }

    public String getMsg() {
        return msg;
    }

    public Result setMsg(String msg) {
        this.msg = msg;
        return this;
    }

    public Object getData() {
        return data;
    }

    public Result setData(Object data) {
        this.data = data;
        return this;
    }

    public String[] getNames() {
        return names;
    }

    public Result setNames(String[] names) {
        this.names = names;
        return this;
    }
}
