package com.suke.czx.modules.audit.dao;

import com.suke.czx.modules.audit.entity.AuditComparison;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AuditComparisonDao extends BaseDao<AuditComparison>{
    /**
     * 保存比对信息
     * @param auditComparison
     * @return
     */
    Integer saveComparison(AuditComparison auditComparison);

    /**
     * 根据文件名查询表中是否存在比对信息
     * @param fileName
     * @return
     */
    AuditComparison queryByFileName(String fileName);

    AuditComparison queryByFileNameAndUserId(@Param("fileName") String fileName,@Param("userId") String userId);

    /**
     * 根据文件名称删除比对信息
     * @param fileName
     * @return
     */
    Integer deleteByFileName(String fileName);

    /**
     * 根据id查询比对结果
     * @param ids
     * @return
     */
    List<AuditComparison> queryListByIds(Long[] ids);


    List<AuditComparison> queryAuditComparisonById(Long id);

}
