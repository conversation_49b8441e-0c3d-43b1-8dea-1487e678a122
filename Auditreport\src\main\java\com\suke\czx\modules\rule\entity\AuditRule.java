package com.suke.czx.modules.rule.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 规则父表
 */
@Data
public class AuditRule {

    /**
     * id
     */
    private String id;
    /**
     * 规则名称
     */
    private String name;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 是否删除，默认0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    /*
      设置报表标题行
    */
    private Integer titleRow;
}
