package com.suke.czx.modules.log.controller;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.audit.util.FTPUtil;
import org.apache.commons.io.IOUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/audit/system/log")
public class SystemLogController {

    @RequestMapping(value = "/list")
    public R getSystemLogs(@RequestParam(defaultValue = "1") Integer current,
                           @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            ByteArrayOutputStream byteArrayOutputStream = FTPUtil.getLogFileInputStream();

            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(Objects.requireNonNull(byteArrayOutputStream,"日志文件流获取失败!").toByteArray());
            String logContent = IOUtils.toString(byteArrayInputStream);
            // System.out.println("字符串长度：" + logContent.length());
            //需要截取的字符串位置
            int index = logContent.length() / 10;
            String content = logContent.substring(logContent.length() - index);
            // System.out.println("最初获取到的数据：" + content);
            String[] splitContent = content.split("\\r?\\n");
            //总页数
            // int sumPage = (splitContent.length / pageSize) + 1;
            //开始的条数
            int startIndex = (current - 1) * pageSize;
            //结束的条数
            int endIndex = startIndex + pageSize;
            //将指定条数的数据添加到List中
            List<String> systemLog = new ArrayList<>(Arrays.asList(splitContent).subList(startIndex, endIndex));
            //总条数
            int total = splitContent.length;

            return Objects.requireNonNull(R.ok().put("systemLog", systemLog)).put("total",total);
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("异常原因:" + e.getMessage());
            return R.error("日志文件流获取失败!");
        }
    }
}
