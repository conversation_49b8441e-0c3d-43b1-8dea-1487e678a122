package com.suke.czx.modules.rule.service;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.rule.entity.AuditRule;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

public interface AuditRuleService {
    /**
     * 将规则文件存入父表
     * @param file
     * @param userId
     * @return
     */
    R saveAuditRule(MultipartFile file, String userId);

    /**
     * 查询全部规则父表
     * @return
     */
    List<AuditRule> queryList(String id);

    /**
     * 根据id更新规则名称
     * @param id
     * @param name
     * @return
     */
    Integer updateRuleById(String id, String name,String updateUserId);


    /**
     * 根据id删除规则父表
     * @param id
     * @return
     */
    Integer deleteRuleById(String id);

    /**
     * 保存规则父表
     * @param auditRule
     * @return
     */
    void add(AuditRule auditRule);

    /**
     * 保存规则父表
     * @param name
     * @return
     */
    List<AuditRule> queryAuditRule(String name);

    Map<String, Object> queryAuditRuleUrl(Integer url_id);

    List<AuditRule> qryAuditRuleWithKeyword(String keyword);
}
