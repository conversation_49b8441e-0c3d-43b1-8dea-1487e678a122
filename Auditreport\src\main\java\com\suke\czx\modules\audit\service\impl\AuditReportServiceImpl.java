package com.suke.czx.modules.audit.service.impl;

import com.alibaba.druid.util.Utils;
import com.suke.czx.common.utils.R;
import com.suke.czx.common.utils.SpringContextUtils;
import com.suke.czx.config.FtpProperties;
import com.suke.czx.modules.audit.controller.AbstractController;
import com.suke.czx.modules.audit.dao.AuditReportDao;
import com.suke.czx.modules.audit.entity.AuditReport;
import com.suke.czx.modules.audit.service.AuditReportService;
import com.suke.czx.modules.audit.util.FTPUtil;
import com.suke.czx.modules.audit.util.Ftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Name;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 报表信息
 */
@Slf4j
@Service
public class AuditReportServiceImpl implements AuditReportService {


    @Autowired
    private AuditReportDao auditReportDao;

    @Autowired
    private FtpProperties ftpProperties;

    @Override
    public AuditReport queryObject(Long id) {
        return null;
    }

    @Override
    public List<AuditReport> queryList(Map<String, Object> map) {
        return auditReportDao.queryList(map);
    }

    @Override
    public int queryTotal(Map<String, Object> map) {
        return 0;
    }

    @Override
    public void save(AuditReport auditReport) {
        auditReportDao.save(auditReport);
    }

    @Override
    public void update(AuditReport auditReport) {

    }

    /**
     * 指定抛出Exception异常时，进行事务回滚
     * @param Ids
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(Long[] Ids) throws Exception {
        //非空判断
        if (Ids.length > 0) {
            //根据id查询出文件名
            List<AuditReport> auditReportList = auditReportDao.queryReportsById(Ids);
            List<String> fileNameList = auditReportList.stream().map(AuditReport::getName).collect(Collectors.toList());
            //删除数据中的报表(先删除数据库中的数据，因为遇到异常可以回滚)
            auditReportDao.deleteBatch(Ids);
            for (String item : fileNameList) {
                //删除服务器中的报表
                FTPUtil.delFile(item,1);
                System.out.println("文件名：" + item);
            }
        }
    }

    /**
     * 批量保存报表
     * @param file
     * @return
     */
    @Override
    @Transactional
    public R saveBatch(MultipartFile[] file, HttpServletRequest request,String userId) throws Exception {
        List<AuditReport> reportList = new ArrayList<>();
        //获取上传的报表数量
        int uploadReportSize = file.length;
        Ftp ftp = null;
        try {
            ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            for (MultipartFile item : file) {
                byte[] bytes = item.getBytes();
                String fileSuffix = item.getOriginalFilename().substring(item.getOriginalFilename().indexOf(".") + 1);
                //将文件信息存入数据库
                AuditReport auditReport = new AuditReport();
                auditReport.setName(item.getOriginalFilename());
                auditReport.setReportSize(item.getSize()/1024);
                //默认设置为0，o：基础报表
                auditReport.setType("0");
                auditReport.setSuffix(fileSuffix);
                String url = item.getOriginalFilename().replaceAll("%","%25");
                String downloadUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/audit/report/download?fileName=" + url;
                auditReport.setUrl(downloadUrl);
                auditReport.setUploadUserId(userId);
                reportList.add(auditReport);
                //将文件上传到服务器
                ftp.sshSftp(bytes, item.getOriginalFilename(), ftpProperties.getBasePath());
            }
            //根据list进行批量插入
            if (reportList.size() > 0) {
                auditReportDao.save(reportList);
                if (reportList.size() < uploadReportSize) {
                    //说明报表全部设置了标题行名称
                    return R.error("报表部分上传成功，但存在部分报表未设置标题行名称!");
                }
            }else {
                return R.error("报表未设置标题行名称!");
            }
        }catch (Exception e){
            log.error(Utils.getStackTrace(e));
        }finally {
            //关闭fpt连接
            if (ftp != null){
                ftp.closeChannel();
            }
        }
        return R.ok("文件全部上传成功!");
    }

    @Override
    @Transactional
    public void updateById(Long reportId, String reportType) {
        auditReportDao.updateById(reportId,reportType);
    }
}
