package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetSumDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ExcelSumHandler extends ExcelHandleRuler {


    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetSumDto condition = ((SheetSumDto) param);
        //求和比对 先获取 比对的值
        Sheet sheet = condition.getSheet();

        Row targetRow = sheet.getRow(condition.getTargetRow());

        Cell targetCell = targetRow.getCell(condition.getTargetColumn());
        setCellStr(targetCell);

        String targetValStr = targetCell.getStringCellValue();
        if (targetValStr == null || targetValStr.isEmpty()) {
            throw new IllegalArgumentException("获取目标比对值为空.");
        }
        log.info("target val : {}", targetValStr);

        BigDecimal targetVal = new BigDecimal(targetValStr);

        BigDecimal sumRes = BigDecimal.ZERO;

        //根据行号 列号 开始求和
        int sumColumn = condition.getColumn();
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);

            Cell oneRowTargetCell = oneRow.getCell(sumColumn);
            setCellStr(oneRowTargetCell);

            BigDecimal oneRowTargetVal = new BigDecimal(oneRowTargetCell.getStringCellValue());

            sumRes = sumRes.add(oneRowTargetVal);
        }
        log.info("第{}列，求和结果为{}，目标结果为{}", sumColumn, sumRes, targetVal);

        HashMap<String, Object> res = new HashMap<>();
        if (sumRes.compareTo(targetVal) != 0) {
            //比对结果不一致 则设置target cell 红色
            setCellRed(targetCell, condition.getCellStyle());
            res.put("status", 3);
            return res;
        }

        res.put("status", 2);
        return res;
    }
}
