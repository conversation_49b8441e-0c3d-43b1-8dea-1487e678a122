package com.suke.czx;



import org.jasypt.util.text.BasicTextEncryptor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;


@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("dev")
public class DynamicDataSourceTest {

    @Test
    public void testJasypt() {
        BasicTextEncryptor encryptor = new BasicTextEncryptor();
        encryptor.setPassword("Nlp1US8sk9nTL0jh8HSYisi64jgoi569");
        String dbName = encryptor.encrypt("bossmain");
        String dbPwd = encryptor.encrypt("NCsmpdba2021");
        String sshAccount = encryptor.encrypt("bossapp");
        String sshPwd = encryptor.encrypt("Q9#4ykWqFi");

//        System.out.println("dbName:" + dbName);
//        System.out.println("dbPwd:" + dbPwd);
//        System.out.println("sshAccount:" + sshAccount);
//        System.out.println("sshPwd:" + sshPwd);
    }

    @Test
    public void testJasypt1() {
        BasicTextEncryptor encryptor = new BasicTextEncryptor();
        encryptor.setPassword("Nlp1US8sk9nTL0jh8HSYisi64jgoi569");
        String dbName = encryptor.decrypt("3IAhPnpsgzdLV0PE34CuI+erjDhtcSLt");

//        System.out.println("dbName:" + dbName);

    }

}
