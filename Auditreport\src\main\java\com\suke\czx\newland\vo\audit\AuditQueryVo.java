package com.suke.czx.newland.vo.audit;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AuditQueryVo {

    private String modle;
    private String center;
    private String value;
    private String key;
    private Date modifyDate;
    private short status;
    private String runDatasourceId;
}
