package com.suke.czx.modules.aitrain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * AI模型训练信息记录
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelTrain implements Serializable {

    /**
     * id
     */
    private int id;

    /**
     * 本次训练名称
     */
    private String trainName;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 训练数据集分数
     */
    private double scoreTest;

    /**
     * 评估标准
     */
    private String evalMetric;

    /**
     * 训练时间
     */
    private double fitTime;

    /**
     * 是否可以推理
     */
    private String canInfer;

    /**
     * 训练顺序
     */
    private int fitOrder;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 备注
     */
    private String remark;
}
