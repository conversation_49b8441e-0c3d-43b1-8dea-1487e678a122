package com.suke.czx.schedule.impl;

import com.suke.czx.config.CronTaskRegistrar;
import com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper;
import com.suke.czx.newland.vo.audit.AuditConfigVo;
import com.suke.czx.newland.vo.audit.AuditDictVo;
import com.suke.czx.schedule.CronTaskMgmt;
import com.suke.czx.thread.SchedulingRunnable;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CronTaskMgmtImpl implements CronTaskMgmt {

    @Autowired
    @Qualifier("taskScheduler")
    private TaskScheduler taskScheduler;

    @Autowired
    private CronTaskRegistrar cronTaskRegistrar;

    @Autowired
    private IAuditCfgMapper auditCfgMapper;

    @PostConstruct
    private void init() {
        cronTaskRegistrar.setTaskScheduler(taskScheduler);
    }

    @Override
    public void refreshTaskByDictId(int dictId) {
        AuditDictVo auditDictVo = auditCfgMapper.qryAllAuditDictByDictId(dictId);
        if (auditDictVo == null) {
            log.info("refresh dict id : {} is not exits.", dictId);
            //字典配置 不存在则移除一下任务
            cronTaskRegistrar.removeCronTaskByTaskDictId(dictId);
            return;
        }
        if (auditDictVo.getCronExpression() == null || auditDictVo.getCronExpression().equals("")){
            log.info("refresh dictId matched cron expression is null");
            //字典配置 为空则移除一下任务
            cronTaskRegistrar.removeCronTaskByTaskDictId(dictId);
            return;
        }
        List<AuditConfigVo> res = auditCfgMapper.queryRptCheckCfgByRuleIdWithActived(auditDictVo.getDictValue());

        if (res == null || res.isEmpty()) {
            log.info("refresh dict id : {} is not exits matched task.", dictId);
            //字典配置存在 没有对应的任务匹配则移除一下任务
            cronTaskRegistrar.removeCronTaskByTaskDictId(dictId);
            return;
        }
        //字典配置存在 且 有任务进行绑定，则进行任务新增
        SchedulingRunnable schedulingRunnable = new SchedulingRunnable(dictId, auditCfgMapper);
        cronTaskRegistrar.addCronTaskWithDictId(schedulingRunnable, auditDictVo.getCronExpression());
    }

    /*
    获取有效的任务配置
     */
    private List<AuditDictVo> getValidTasks(List<AuditDictVo> param) {
        if (param == null || param.isEmpty()) {
            return Lists.emptyList();
        }
        return param.stream()
                //不是正规cron  表达式不添加
                .filter(e -> {
                    if (e.getCronExpression() == null) {
                        return false;
                    } else {
                        return CronExpression.isValidExpression(e.getCronExpression());
                    }
                })
                //对应字典不存在匹配的任务 不添加
                .filter(e -> {
                    List<AuditConfigVo> res = auditCfgMapper.queryRptCheckCfgByRuleIdWithActived(e.getDictValue());
                    return !res.isEmpty();
                }).collect(Collectors.toList());
    }
}
