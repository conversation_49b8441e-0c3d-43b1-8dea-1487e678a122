package com.suke.czx.modules.audit.component;

import java.util.Objects;

public enum RuleType {

    /**
     * 匹配内容
     */
    TOGETHER("TOGETHER",1),
    /**
     *
     */
    SUM("SUM",2),
    /**
     *
     */
    LT("LT",3),
    /**
     *
     */
    GT("GT",4),
    /**
     *
     */
    EQUAL("EQUAL",5),
    /**
     *
     */
    SORT("SORT",6),
    /**
     *
     */
    IN("IN",7),
    /**
     *
     */
    NOTEMPTY("NOTEMPTY",8),
    /**
     *
     */
    NOTLIKE("NOTLIKE",9),
    /**
     *
     */
    NUMBER("NUMBER",10),
    /**
     *
     */
    UNIQUE("UNIQUE",11);

    private String name;

    private Integer index;

    RuleType(String name,Integer index) {
        this.name = name;
        this.index = index;
    }

    /**
     * 获取类型名称
     * @return
     */
    public String getName() {
        return name;
    }

    /**
     * 获取索引
     * @return
     */
    public Integer getIndex() {
        return index;
    }

    public static String getNameByIndex(int index) {
        for (RuleType item : RuleType.values()) {
            if (item.index == index) {
                return item.name;
            }
        }
        return null;
    }

    public static String getNameByName(String type) {
        for (RuleType item : RuleType.values()) {
            if (Objects.equals(item.name, type)) {
                return item.name;
            }
        }
        return null;
    }


}
