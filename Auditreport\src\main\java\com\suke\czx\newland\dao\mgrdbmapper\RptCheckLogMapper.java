package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.AuditLogDownloadRecordDto;
import com.suke.czx.newland.dto.RptCheckLogDto;
import com.suke.czx.newland.po.RptCheckCfgPo;
import com.suke.czx.newland.vo.AuditLogDownloadVo;
import com.suke.czx.newland.vo.LogIdsVo;
import com.suke.czx.newland.vo.audit.AuditLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface RptCheckLogMapper {


    @Select({"select t.MODLE_NAME modleName,t.CONCLUSION conclusion,count(*) countVal from RPT_CHECK_LOG t where trunc(EXEC_DATE) = to_date(#{execTime},'yyyyMMdd')   GROUP BY t.MODLE_NAME ,t.conclusion"})
    List<RptCheckLogDto> qryRptCheckLogGroupByConclusionWithTime(@Param("execTime") String execTime);

    List<AuditLogVo> qryRptCheckLogWithCondition(AuditLogVo condition);


    List<AuditLogVo> qryRptCheckLogWithLogIds(String[] logIds);


    void insertRptCheckLog(AuditLogVo record);

    @Update({"update RPT_CHECK_LOG set ANALYSIS_RES = #{analysisRes} where LOG_ID = #{logId}"})
    void updateRptCheckLogAnalysisResWithLogId(@Param("analysisRes") String analysisRes, @Param("logId") String logId);

    List<AuditLogDownloadRecordDto> qryAuditLogWithLogIds(AuditLogDownloadVo param);
}
