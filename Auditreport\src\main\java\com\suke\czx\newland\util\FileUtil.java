package com.suke.czx.newland.util;

import java.io.*;
import java.math.BigInteger;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

public class FileUtil {
    /**
     * 写入文件
     */
    public static void write(String target, InputStream src) throws IOException {
        OutputStream os = Files.newOutputStream(Paths.get(target));
        byte[] buf = new byte[1024];
        int len;
        while (-1 != (len = src.read(buf))) {
            os.write(buf, 0, len);
        }
        os.flush();
        os.close();
    }

    /**
     * 分块写入文件
     */
    public static void writeWithBlok(String target, Long targetSize, InputStream src, Long srcSize, Integer chunks, Integer chunk) throws IOException {
        RandomAccessFile randomAccessFile = new RandomAccessFile(target, "rw");
        randomAccessFile.setLength(targetSize);
        if (chunk == chunks - 1) {
            randomAccessFile.seek(targetSize - srcSize);
        } else {
            randomAccessFile.seek(chunk * srcSize);
        }
        byte[] buf = new byte[1024];
        int len;
        while (-1 != (len = src.read(buf))) {
            randomAccessFile.write(buf, 0, len);
        }
        randomAccessFile.close();
    }

    /**
     * 生成随机文件名
     */
    public static String generateFileName() {
        return UUID.randomUUID().toString();
    }


    public static String calculateMD5(String filePath) throws IOException, NoSuchAlgorithmException {
        // 创建MD5算法实例
        MessageDigest md = MessageDigest.getInstance("MD5");

        // 读取文件并更新摘要
        try (InputStream is = Files.newInputStream(Paths.get(filePath))) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
        }

        // 计算最终的哈希值
        byte[] digest = md.digest();

        // 转换为16进制字符串
        BigInteger bigInt = new BigInteger(1, digest);
        return bigInt.toString(16);
    }

    public static String calculateMD5(ByteArrayInputStream inputStream) {
        // 创建 MessageDigest 实例，使用 MD5 算法
        StringBuilder sb = new StringBuilder();
        try {
            MessageDigest digest = MessageDigest.getInstance("MD5");

            // 缓冲区大小
            byte[] buffer = new byte[1024];
            int bytesRead;

            // 读取输入流数据并更新到 MessageDigest 中
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }

            // 计算哈希值
            byte[] md5Bytes = digest.digest();

            // 将字节数组转换为十六进制字符串
            for (byte b : md5Bytes) {
                sb.append(String.format("%02x", b));
            }
        } catch (NoSuchAlgorithmException | IOException e) {
            throw new RuntimeException(e);
        }
        return sb.toString();
    }
}
