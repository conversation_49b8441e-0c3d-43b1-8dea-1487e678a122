package com.suke.czx.modules.audit.controller;


import com.alibaba.druid.util.Utils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.audit.component.RuleType;
import com.suke.czx.modules.audit.entity.AuditComparison;
import com.suke.czx.modules.audit.service.AuditComparisonService;
import com.suke.czx.modules.audit.util.FTPUtil;
import jnr.ffi.annotations.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 报表比对结果表
 */
@Slf4j
@RestController
@RequestMapping("/audit/report/result")
public class AuditComparisonController extends AbstractController{

    @Autowired
    private AuditComparisonService auditComparisonService;

    @SysLog("比对报表")
    @RequestMapping(value = "/comparison",method = RequestMethod.POST)
    public R comparisonReport(@RequestParam("auditName") String auditName, @RequestParam(value = "fileName",defaultValue = "") String fileName, HttpServletRequest request, HttpServletResponse response) {
        log.info("需要比对的文件名：{} 使用的规则id：{}", fileName, auditName);
        try {
            return auditComparisonService.comparison(fileName, getUserId(), auditName, request, response);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return R.error("获取的报表流为空!");
        }
    }

    @SysLog("下载比对结果")
    @RequestMapping(value = "/downloadFile",method = RequestMethod.GET)
    public void downloadComparisonFile(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        log.info("文件名：{}", fileName);
        try {
            FTPUtil.getComparisonFile(fileName,response);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
        }
    }


    /**
     * 获取所有的比对信息
     * @return
     */
    @RequestMapping(value = "/getComparisonList",method = RequestMethod.GET)
    public R getComparisonList(@RequestParam(defaultValue = "1") Integer current,
                               @RequestParam(defaultValue = "10") Integer pageSize,
                               @RequestParam(defaultValue = "") String name,
                               @RequestParam("status") Integer status,
                               @RequestParam(defaultValue = "")String userId) {
        //开启分页
        PageHelper.startPage(current,pageSize);
        List<AuditComparison> auditComparisonList = auditComparisonService.queryComparisonList(name,status,userId);
        //封装
        PageInfo<AuditComparison> comparisonPageInfo = new PageInfo<>(auditComparisonList);
        long total = comparisonPageInfo.getTotal();
        //使用Objects.requireNonNull判断一个对象是否为空（发生异常可以直接抛出空指针异常，直接定位问题位置）
        //Objects.requireNonNull(R.ok().put("comparisonList", auditComparisonList),"比对结果集为空").put("total",total);
        return R.ok().put("comparisonList", auditComparisonList).put("total",total);
    }

    @SysLog("批量导出比对结果")
    @RequestMapping(value = "/batchExport",method = RequestMethod.POST)
    public R batchExport(@RequestBody Long[] ids) {
        return auditComparisonService.batchExport(ids);
    }

    /**
     * 删除对比报表(删除服务器和数据库中的报表)
     * @param ids
     * @return
     * @throws Exception
     */
    @SysLog("批量删除对比报表")
    @RequestMapping(value = "/deleteBatch",method = RequestMethod.DELETE)
    public R delComparisonBatch(@RequestBody Long[] ids) throws Exception {
        //非空判断
        int res = auditComparisonService.delComparisonBatch(ids);
        if (res > 0) {
            return R.ok().put("result","删除成功!");
        }else if (res < 0) {
            return R.error().put("result","该规则下还存在子规则，请先删除子规则!");
        }else {
            return R.error().put("result","删除失败!");
        }
    }

    /**
     * 批量删除对比报表(删除服务器和数据库中的报表)
     * @param id
     * @return
     * @throws Exception
     */
    @SysLog("删除对比报表")
    @RequestMapping(value = "/delete",method = RequestMethod.GET)
    public R deleteAuditComparison(@RequestParam Long id) throws Exception {
        // FTPUtil.delFile(fileName);
        //非空判断
        int res = auditComparisonService.deleteBatch(id);
        if (res > 0) {
            return R.ok().put("result","删除成功!");
        }else if (res < 0) {
            return R.error().put("result","该规则下还存在子规则，请先删除子规则!");
        }else {
            return R.error().put("result","删除失败!");
        }
    }

    @RequestMapping(value = "/preview",method = RequestMethod.GET)
    public void previewExcel(@RequestParam("fileName") String fileName, HttpServletResponse response) throws IOException {
        log.debug("文件名path：{}",fileName);
        try {
            FTPUtil.downloadFile(fileName,"",response);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
        }

    }
}
