<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.AuditUploadFileMapper">
    <insert id="insert">
        insert into AUDIT_UPLOAD_FILE
        (ID,FILE_NAME,FILE_SIZE,MD5,FILE_PATH,UPLOAD_TIME,UPLOAD_USER,FILE_TYPE)
        values (AUDIT_UPLOAD_FILE_SEQ.NEXTVAL, #{fileName},#{fileSize}, #{md5}, #{filePath}, #{uploadTime}, #{uploadUser},#{fileType})
    </insert>
    <insert id="insertReturnId" useGeneratedKeys="false">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">
            select AUDIT_UPLOAD_FILE_SEQ.NEXTVAL from dual
        </selectKey>
        insert into AUDIT_UPLOAD_FILE
        (ID,FILE_NAME,FILE_SIZE,MD5,FILE_PATH,UPLOAD_TIME,UPLOAD_USER,FILE_TYPE)
        values (#{id}, #{fileName},#{fileSize}, #{md5}, #{filePath}, #{uploadTime}, #{uploadUser},#{fileType})
    </insert>
    <delete id="delAuditFileWithAuditFileId">
        update AUDIT_UPLOAD_FILE set DELETED = '1' where id = ${auditFileId}
    </delete>
    <delete id="delBatchAuditFileWithAuditFileIds">
        update AUDIT_UPLOAD_FILE set DELETED = '1' where id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <select id="qryAuditWithCondition" resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,FILE_NAME,FILE_SIZE,UPLOAD_TIME,FILE_TYPE from AUDIT_UPLOAD_FILE
        where 1 = 1 and DELETED = '0' and FILE_TYPE = '0'
        <if test="fileName != null and fileName != ''">
            and FILE_NAME like concat(concat('%',#{fileName}),'%')
        </if>
        <if test="uploadBeginTime != null and uploadEndTime != null">
            and to_char(UPLOAD_TIME,'yyyyMMddhh24miss') between to_char(#{uploadBeginTime},'yyyyMMddhh24miss') and
            to_char(#{uploadEndTime},'yyyyMMddhh24miss')
        </if>
        order by ID asc
    </select>
    <select id="qryAuditFileWithAuditFileId" resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,FILE_NAME,MD5,FILE_PATH,FILE_SIZE,UPLOAD_TIME,UPLOAD_USER,FILE_TYPE
        from AUDIT_UPLOAD_FILE where id = ${auditFileId} and DELETED = '0'
    </select>
    <select id="qryAuditFileWithAuditFileIdWithoutStatus"
            resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,FILE_NAME,MD5,FILE_PATH,FILE_SIZE,UPLOAD_TIME,UPLOAD_USER,FILE_TYPE
        from AUDIT_UPLOAD_FILE where id = ${auditFileId}
    </select>
    <select id="qryAuditFileWithAuditFileIds" resultType="com.suke.czx.newland.po.AuditUploadFilePo">
        select ID,FILE_NAME,FILE_SIZE,FILE_PATH,UPLOAD_TIME,FILE_TYPE from AUDIT_UPLOAD_FILE
        where 1 = 1 and DELETED = '0' and id in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>