package com.suke.czx.newland.service;

import com.github.pagehelper.PageInfo;
import com.suke.czx.newland.dto.DatasourceInfoDto;
import com.suke.czx.newland.vo.DatasourceInfoQryVo;

import java.util.List;

public interface DatasourceService {

    void addDatasource(DatasourceInfoDto dataBaseInfoBean);

    void updateDatasource(DatasourceInfoDto dataBaseInfoBean);

    Object qryDatasourceInfoByPage(DatasourceInfoDto condition, int pageIndex, int pageSize);

    void testDatasourceConnection(String datasourceId);

    PageInfo<DatasourceInfoDto> qryDatasourceWithPageInfo(DatasourceInfoQryVo qryCondition, int pageIndex, int pageSize);

    void deleteDatasourceByDatasourceId(String datasourceId);

    void testConnection(String driveClass, String url, String username, String password);

    List<DatasourceInfoDto> qryAllDatasourceActived();
}
