package com.suke.czx.modules.audit.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.audit.entity.AuditReport;
import com.suke.czx.modules.audit.service.AuditReportService;
import com.suke.czx.modules.audit.util.FTPUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.Path;
import java.util.*;

/**
 * 报表信息
 */
@RestController
@RequestMapping("/audit/report")
public class AuditReportController extends AbstractController{

    private static String host;
    @Value("${ftp.host}")
    public void setHost(String host) {
        AuditReportController.host = host;
    }

    private static String port;
    @Value("${server.port}")
    public void setPort(String port) {
        AuditReportController.port = port;
    }

    private static String basicPath;
    @Value("${server.context-path}")
    public void setBasicPath(String basicPath) {
        AuditReportController.basicPath = basicPath;
    }

    @Autowired
    private AuditReportService auditReportService;

    /**
     * 查询全部报表
     * @return
     */
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public R getAllReports(@RequestParam(defaultValue = "1") Integer current,
                           @RequestParam(defaultValue = "10") Integer pageSize,
                           @RequestParam(defaultValue = "") String name,
                           @RequestParam(defaultValue = "") String type) {
        //开启分页
        PageHelper.startPage(current,pageSize);
        Map<String,Object> map = new HashMap<>();
        if (!Objects.equals(name, "")) {
            map.put("name",name);
        }else {
            map.put("name",null);
        }
        if (!Objects.equals(type,"")) {
            map.put("type",type);
        }else {
            map.put("type",null);
        }

        List<AuditReport> auditReports = auditReportService.queryList(map);
        //封装
        PageInfo<AuditReport> reportPageInfo = new PageInfo<>(auditReports);
        long total = reportPageInfo.getTotal();
        return R.ok().put("reportList",auditReports).put("total",total);
    }

    /**
     * 将报表上传到服务器
     * @param file
     * @return
     */
    @SysLog("上传报表")
    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    public R uploadReport(@RequestParam("file") MultipartFile[] file, HttpServletRequest request) {
        R res = null;
        try {
            res = auditReportService.saveBatch(file,request,getUserId());
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("发生异常，文件上传失败!");
        }
        return res;
    }

    /**
     * 根据url下载报表
     */
    @SysLog("下载报表")
    @RequestMapping(value = "/download",method = RequestMethod.GET)
    public void downloadReport(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        try {
            FTPUtil.getFile(fileName,response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改报表信息
     * @param reportId
     * @param reportType
     * @return
     */
    @SysLog("更新报表信息")
    @RequestMapping(value = "/update",method = RequestMethod.PUT)
    public R uploadReport(@RequestParam("reportId") Long reportId,@RequestParam("reportType") String reportType) {
        auditReportService.updateById(reportId,reportType);
        return R.ok();
    }

    /**
     * 删除报表(删除服务器和数据库中的报表)
     * @param ids
     * @return
     * @throws Exception
     */
    @SysLog("删除报表")
    @RequestMapping(value = "/delete",method = RequestMethod.DELETE)
    public R deleteReport(@RequestBody Long[] ids) throws Exception {
        // FTPUtil.delFile(fileName);
        //非空判断
        if (ids.length > 0) {
            auditReportService.deleteBatch(ids);
        }
        return R.ok();
    }
}
