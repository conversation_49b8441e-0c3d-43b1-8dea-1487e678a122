package com.suke.czx.modules.AI.service.impl;

import com.alibaba.druid.util.Utils;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.config.FtpProperties;
import com.suke.czx.modules.AI.dao.AiFileDao;
import com.suke.czx.modules.AI.entity.AIFileVo;
import com.suke.czx.modules.AI.entity.AiFile;
import com.suke.czx.modules.AI.entity.AuditAiFileQryCondition;
import com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo;
import com.suke.czx.modules.AI.service.AIAuditService;
import com.suke.czx.modules.AI.util.ConvertUtil;
import com.suke.czx.modules.AI.util.FTPAiUtils;
import com.suke.czx.modules.audit.util.Ftp;
import com.suke.czx.newland.util.DataSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.poi.hssf.record.DVALRecord;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class AIAuditServiceImpl implements AIAuditService {

    private final AiFileDao aiFileDao;

    private final DataSourceUtil dataSourceUtil;

    private final FtpProperties ftpProperties;

    public AIAuditServiceImpl(AiFileDao aiFileDao, DataSourceUtil dataSourceUtil, FtpProperties ftpProperties) {
        this.aiFileDao = aiFileDao;
        this.dataSourceUtil = dataSourceUtil;
        this.ftpProperties = ftpProperties;
    }


    @Override
    public PageInfo<AIFileVo> qryAuditAiFile(AuditAiFileQryCondition condition) {
        PageHelper.startPage(condition.getCurPage(), condition.getPageSize());
        List<AiFile> res = aiFileDao.qyrAuditAiFileWithCondition(condition);
        List<AIFileVo> returnRes = new ArrayList<>();
        try {
            for (AiFile item : res) {
                AIFileVo temp = new AIFileVo();
                BeanUtils.copyProperties(temp, item);
                temp.setReportSize(getFileSizeStr(item.getReportSize()));
                returnRes.add(temp);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        PageInfo<AIFileVo> finalRes = new PageInfo<>(returnRes);
        PageInfo<AiFile> pageInfo = new PageInfo<>(res);
        finalRes.setTotal(pageInfo.getTotal());
        return finalRes;
    }

    public static String getFileSizeStr(long fileSize) {
        double fileSizeKb = fileSize / 1024.0;
        if (fileSizeKb > 1024) {
            Double fileSizeMB = fileSizeKb / 1024.0;
            return String.format("%.2f MB", fileSizeMB);
        } else {
            return String.format("%.2f KB", fileSizeKb);
        }

    }

    @Override
    @Transactional
    public void generateAIAuditFile(GenerateAIFileConditionVo condition, HttpServletRequest request, String userId) {
        try {
            List<JSONObject> res = dataSourceUtil.executeSqlReturnAll(condition.getRunDatasourceId(), condition.getSqlText(), true);
            Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            String fileName = "AI稽核SQL生成文件_" + dateStr + ".csv";
            String downloadUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/aiTools/AITool/download?fileName=" + fileName;
            int fileSize = ConvertUtil.convertToCsv(res, ftp, fileName, ftpProperties.getAiPath());
            AiFile aiFile = new AiFile();
            aiFile.setName(fileName);
            aiFile.setType("0");
            aiFile.setUrl(downloadUrl);
            aiFile.setReportSize((long) (fileSize / 1024));
            aiFile.setUploadUserId(userId);
            aiFileDao.save(Lists.newArrayList(aiFile));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateAuditFileDeleted(List<String> ids) {
        aiFileDao.delByIds(ids);
    }

    @Override
    public void downloadAuditedFile(String sourceFileId, HttpServletResponse response) {
        Objects.requireNonNull(sourceFileId);
        AiFile aiFile = aiFileDao.qryAuditedAiFileBySourceFileId(sourceFileId);
        if (Objects.isNull(aiFile)) {
            throw new IllegalArgumentException("文件还未执行AI稽核.");
        }
        FTPAiUtils.getAIAuditedFile(aiFile.getName(), response);
    }

    @Override
    @Transactional
    public void auditFileByFileId(String fileId,String modelName) {
        Objects.requireNonNull(fileId);
        AiFile aiFile = aiFileDao.qyrAuditAiFileWithId(fileId);
        if (Objects.isNull(aiFile)) {
            throw new IllegalArgumentException("文件在数据库不存在.");
        }
        if ("1".equals(aiFile.getType())) {
            throw new IllegalArgumentException("请勿重复AI稽核.");
        }
        //do ai audit
        doAiAudit(aiFile.getName(),modelName);
        aiFileDao.updateAiFileTypesByFileId(fileId);
        AiFile record = new AiFile();
        record.setName(aiFile.getName());
        record.setUrl(aiFile.getUrl());
        record.setReportSize(aiFile.getReportSize());
        record.setUploadUserId(aiFile.getUploadUserId());
        record.setSourceFileId(aiFile.getId());
        record.setType("2");
        aiFileDao.insertAuditedAIFileInfo(record);
    }

    private void doAiAudit(String fileName,String modelName) {
        try {
            //最终的调用命令：/xxx/python3.8/bin/python3.8 /app/pythonProject1/model/autoglu_pre.py /app/pythonProject1/data/训练数据-异常test.csv
            //最终的调用命令：docker exec audit_project python3 /app/pythonProject1/model/autoglu_pre.py /app/pythonProject1/data/训练数据-异常test.csv
            //python .\model\autoglu_pre.py --data_path .\data\训练数据-正常_Sheet1.csv --model_name ag-20240905_064427
            StringBuilder command = new StringBuilder();
            if (ftpProperties.getContainerName() == null) {
                //普通部署
                command.append(ftpProperties.getPythonPath())
                        .append(" ")
                        .append(ftpProperties.getPythonbasePath())
                        .append("/model/autoglu_pre.py")
                        .append(" ")
                        .append("--data_path")
                        .append(" ")
                        .append(ftpProperties.getAiPath())
                        .append("/")
                        .append(fileName)
                        .append(" ")
                        .append("--model_name")
                        .append(" ")
                        .append(ftpProperties.getPythonbasePath())
                        .append("/AutogluonModels")
                        .append("/")
                        .append(modelName);
            }else {
                //docker容器部署
                command.append("docker exec")
                        .append(" ")
                        .append(ftpProperties.getContainerName())
                        .append(" ")
                        .append("python3")
                        .append("  ")
//                    .append(ftpProperties.getPythonbasePath())
                        .append("/app/pythonProject1/model/autoglu_pre.py")
                        .append("  ")
                        .append("--data_path")
                        .append(" ")
                        .append("/app/pythonProject1/data/")
                        .append(fileName)
                        .append(" ")
                        .append("--model_name")
                        .append(" ")
                        .append("/app/pythonProject1/AutogluonModels/")
                        .append(modelName);
            }
            log.info("command :{}", command);
            String commandRes = FTPAiUtils.doCommandAndGetRes(command.toString());
            log.info("commandRes :{}", commandRes);
            if (commandRes.isEmpty()){
                log.info("检查 ftp 主机是否存在 docker 环境，docker 中是否存在 audit_project容器能够执行对应的命令.");
                throw new IllegalArgumentException("ai稽核异常");
            }
            ByteArrayOutputStream fileByteOutStream = FTPAiUtils.getFileOutputStream(fileName);


            ByteArrayInputStream inputStream = new ByteArrayInputStream(addColumnToCsv(fileByteOutStream, commandRes).toByteArray());
            FTPAiUtils.doUploadWithStream(inputStream, fileName);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    public static ByteArrayOutputStream addColumnToCsv(ByteArrayOutputStream byteArrayOutputStream, String commandRes) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        String[] aiAuditRes = commandRes.split(",");
        try {
            // 将字节流转换为字符串
            String csvContent = byteArrayOutputStream.toString(StandardCharsets.UTF_8.name());

            // 使用BufferedReader逐行读取CSV内容
            BufferedReader reader = new BufferedReader(new StringReader(csvContent));
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream, StandardCharsets.UTF_8));

            String line;
            for (int i = 0; i < aiAuditRes.length + 1; i++) {
                if ((line = reader.readLine()) != null) {
                    String modifiedLine;
                    if (i == 0) {
                        modifiedLine = line + "," + "AI_AUDIT_RES";
                    } else {
                        modifiedLine = line + "," + aiAuditRes[i - 1];
                    }
                    writer.write(modifiedLine);
                    writer.newLine();
                }
            }

            writer.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return outputStream;
    }
}
