package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetUniqueDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class UniqueHandler extends ExcelHandleRuler {
    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetUniqueDto condition = (SheetUniqueDto) param;
        Sheet sheet = condition.getSheet();
        List<Integer> uniqueColumns = condition.getColumns();


        Set<String> set = new HashSet<>();
        boolean res = true;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);
            String setStr = getTargetColumnsStr(oneRow, uniqueColumns);
            if (set.contains(setStr)) {
                setRowTargetColumnsRed(oneRow, uniqueColumns, condition.getCellStyle());
                res = false;
            } else {
                set.add(setStr);
            }
        }
        return res ? success() : fail();

    }

    private void setRowTargetColumnsRed(Row row, List<Integer> uniqueColumns, CellStyle cellStyle) {
        for (Integer column : uniqueColumns) {
            Cell cell = row.getCell(column);
            setCellRed(cell, cellStyle);
        }
    }

    private String getTargetColumnsStr(Row row, List<Integer> uniqueColumns) {
        StringBuilder strBuild = new StringBuilder();
        for (Integer column : uniqueColumns) {
            Cell cell = row.getCell(column);
            setCellStr(cell);
            strBuild.append(cell.getStringCellValue());
        }
        return strBuild.toString();
    }
}
