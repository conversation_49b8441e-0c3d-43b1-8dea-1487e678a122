package com.suke.czx.newland.service.impl;

import com.suke.czx.modules.sys.entity.SysUserEntity;
import com.suke.czx.newland.consts.UploadFileType;
import com.suke.czx.newland.dao.mgrdbmapper.AuditUploadFileMapper;
import com.suke.czx.newland.po.AuditUploadFilePo;
import com.suke.czx.newland.service.FileService;
import com.suke.czx.newland.util.FileUtil;
import com.suke.czx.newland.util.FragmentUploadUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import static com.suke.czx.newland.util.FragmentUploadUtil.*;

@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${upload.path}")
    private String uploadLocalPath;

    private final AuditUploadFileMapper uploadFileMapper;

    public static final String EXCEL_POSTFIX = ".xlsx";
    public static final String CSV_POSTFIX = ".csv";

    public FileServiceImpl(AuditUploadFileMapper uploadFileMapper) {
        this.uploadFileMapper = uploadFileMapper;
    }


    @Override
    public void upload(String name, String md5, MultipartFile file, UploadFileType fileType) {
        String path = uploadLocalPath + generateFileName();
        try {
            FileUtil.write(path, file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        AuditUploadFilePo record = new AuditUploadFilePo();
        record.setFileName(name);
        record.setMd5(md5);
        record.setFilePath(path);
        record.setFileSize(getFileSizeStr(path));
        record.setUploadTime(LocalDateTime.now());
        record.setFileType(fileType.getFileType());
        SysUserEntity curUserInfo = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        record.setUploadUser(curUserInfo.getUserName());
        uploadFileMapper.insert(record);
    }

    @Override
    public void uploadWithBlock(String name, String md5, Long size, Integer chunks, Integer chunk, MultipartFile file, UploadFileType fileType) throws IOException {
        String fileName = getFileName(md5, chunks);
        FileUtil.writeWithBlok(uploadLocalPath + fileName, size, file.getInputStream(), file.getSize(), chunks, chunk);
        addChunk(md5, chunk);
        if (isUploaded(md5)) {
            removeKey(md5);
            AuditUploadFilePo record = new AuditUploadFilePo();
            record.setFileName(name);
            record.setMd5(md5);
            record.setFilePath(uploadLocalPath + fileName);
            record.setUploadTime(LocalDateTime.now());
            record.setFileType(fileType.getFileType());
            record.setFileSize(getFileSizeStr(uploadLocalPath + fileName));
            SysUserEntity curUserInfo = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
            record.setUploadUser(curUserInfo.getUserName());
            uploadFileMapper.insert(record);
        }
    }

    @Override
    public void delFileWithAuditFileId(String auditFileId) {
        AuditUploadFilePo fileInfo = uploadFileMapper.qryAuditFileWithAuditFileId(auditFileId);
        if (fileInfo == null) {
            log.info("文件在数据库中无记录.");
            return;
        }
        String filePath = fileInfo.getFilePath();
        File file = new File(filePath);
        if (!file.exists()) {
            uploadFileMapper.delAuditFileWithAuditFileId(auditFileId);
            log.info("文件在服务器中不存在.");
            return;
        }

        uploadFileMapper.delAuditFileWithAuditFileId(auditFileId);
        log.info("文件删除成功.");

    }

    @Override
    public void delBatchFileWithAuditFileIds(List<String> ids) {
        List<AuditUploadFilePo> fileInfos = uploadFileMapper.qryAuditFileWithAuditFileIds(ids);
        if (fileInfos == null) {
            throw new RuntimeException("文件在数据库中无记录.");
        }
        for (AuditUploadFilePo fileInfo : fileInfos) {
            String filePath = fileInfo.getFilePath();
            File file = new File(filePath);
            if (!file.exists()) {
                uploadFileMapper.delAuditFileWithAuditFileId(fileInfo.getId());
                throw new RuntimeException("文件在服务器中不存在.文件名称：" + fileInfo.getFileName());
            }
        }
        uploadFileMapper.delBatchAuditFileWithAuditFileIds(ids);
    }

    @Override
    public String getAuditedFileName(AuditUploadFilePo fileInfo) {
        if (fileInfo == null) {
            throw new IllegalArgumentException("文件信息为空, 无法生成稽核文件名.");
        }
        String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String oldFileName = fileInfo.getFileName();
        String newFileName = "";
        if (oldFileName.endsWith(EXCEL_POSTFIX)) {
            newFileName = oldFileName.substring(0, oldFileName.length() - EXCEL_POSTFIX.length()) + "_out_" + dateStr + EXCEL_POSTFIX;
        } else if (oldFileName.endsWith(CSV_POSTFIX)) {
            newFileName = oldFileName.substring(0, oldFileName.length() - CSV_POSTFIX.length()) + "_out_" + dateStr + CSV_POSTFIX;
        }
        return newFileName;
    }

    @Override
    public String uploadReturnId(String name, String md5, MultipartFile file, UploadFileType fileType) {
        String path = uploadLocalPath + generateFileName();
        try {
            FileUtil.write(path, file.getInputStream());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        AuditUploadFilePo record = new AuditUploadFilePo();
        record.setFileName(name);
        record.setMd5(md5);
        record.setFilePath(path);
        record.setFileSize(getFileSizeStr(path));
        record.setUploadTime(LocalDateTime.now());
        record.setFileType(fileType.getFileType());
        SysUserEntity curUserInfo = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        record.setUploadUser(curUserInfo.getUserName());
        uploadFileMapper.insertReturnId(record);
        return record.getId();
    }

    /**
     * 生成随机文件名
     */
    public static String generateFileName() {
        return UUID.randomUUID().toString();
    }
}
