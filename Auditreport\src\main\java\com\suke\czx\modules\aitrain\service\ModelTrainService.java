package com.suke.czx.modules.aitrain.service;

import com.suke.czx.modules.aitrain.entity.ModelTrain;

import java.util.List;
import java.util.Map;

public interface ModelTrainService {

    /**
     * 获取全部的训练数据信息
     * @return
     */
    List<ModelTrain> getAllModelTrain();

    /**
     * 根据训练名称查询训练数据信息
     * @param trainName
     * @return
     */
    List<ModelTrain> queryByTrainName(String trainName);

    /**
     * 查询全部训练名称
     * @return
     */
    List<String> queryAllTrainName();

    /**
     * 获取模型数量
     * @return
     */
    int getModelSum();

    /**
     * 根据训练名称获取模型和得分
     * @param trainName
     * @return
     */
    Map<String,Object> getModelAndScoreTest(String trainName);
}
