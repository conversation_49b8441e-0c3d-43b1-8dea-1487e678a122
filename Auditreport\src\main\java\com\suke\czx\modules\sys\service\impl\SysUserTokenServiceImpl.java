package com.suke.czx.modules.sys.service.impl;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.sys.dao.SysUserTokenDao;
import com.suke.czx.modules.sys.entity.SysUserTokenEntity;
import com.suke.czx.modules.sys.oauth2.TokenGenerator;
import com.suke.czx.modules.sys.service.SysUserTokenService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;


@Service("sysUserTokenService")
public class SysUserTokenServiceImpl implements SysUserTokenService {
	@Autowired
	private SysUserTokenDao sysUserTokenDao;
	//12小时后过期
	private final static int EXPIRE = 3600 * 12;

	@Override
	public SysUserTokenEntity queryByUserId(String userId) {
		return sysUserTokenDao.queryByUserId(userId);
	}

	@Override
	public void save(SysUserTokenEntity token){
		sysUserTokenDao.save(token);
	}
	
	@Override
	public void update(SysUserTokenEntity token){
		sysUserTokenDao.update(token);
	}

	@Override
	public R createToken(String userId) {

		//生成一个token
		String token = TokenGenerator.generateValue();

		//当前时间
		Date now = new Date();
		//过期时间(12小时)
		Date expireTime = new Date(now.getTime() + EXPIRE * 1000);

		//判断是否生成过token
		SysUserTokenEntity tokenEntity = queryByUserId(userId);
		if(tokenEntity == null){
			//未生成过token
			tokenEntity = new SysUserTokenEntity();
			tokenEntity.setUserId(userId);
			tokenEntity.setToken(token);
			tokenEntity.setUpdateTime(LocalDateTime.now());
			tokenEntity.setExpireTime(expireTime.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime());

			//保存token
			save(tokenEntity);
			// int res = sysUserTokenDao.saveToken(tokenEntity);
		}else{
			//已经存在了token，更新token
			tokenEntity.setToken(token);
			tokenEntity.setUpdateTime(LocalDateTime.now());
			tokenEntity.setExpireTime(expireTime.toInstant().atZone(ZoneOffset.ofHours(8)).toLocalDateTime());

			//更新token
			update(tokenEntity);
		}

		R r = R.ok().put("token", token).put("expire", EXPIRE);

		return r;
	}

	@Override
	public void logout(String userId) {
		//生成一个token
		String token = TokenGenerator.generateValue();

		//修改token
		SysUserTokenEntity tokenEntity = new SysUserTokenEntity();
		tokenEntity.setUserId(userId);
		tokenEntity.setToken(token);
		update(tokenEntity);
	}
}
