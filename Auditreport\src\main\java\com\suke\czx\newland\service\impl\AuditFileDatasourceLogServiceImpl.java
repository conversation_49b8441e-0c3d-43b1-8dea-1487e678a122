package com.suke.czx.newland.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.newland.dao.mgrdbmapper.AuditFileDatasourceLogMapper;
import com.suke.czx.newland.dto.AuditFileDatasourceLogQryDto;
import com.suke.czx.newland.dto.AuditFileDatasourceLogResDto;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;
import com.suke.czx.newland.po.AuditFileDatasourceRulePo;
import com.suke.czx.newland.service.AuditFileDatasourceLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Slf4j
@Service
public class AuditFileDatasourceLogServiceImpl implements AuditFileDatasourceLogService {

    private final AuditFileDatasourceLogMapper logMapper;

    public AuditFileDatasourceLogServiceImpl(AuditFileDatasourceLogMapper logMapper) {
        this.logMapper = logMapper;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void addBatchAuditFileDatasourceLog(List<AuditFileDatasourceLogPo> records) {
        if (records == null || records.isEmpty()) {
            throw new IllegalArgumentException("文件数据源稽核日志记录不可为空.");
        }
        int res = logMapper.addBatchAuditFileDatasourceLog(records);
        if (res != records.size()) {
            throw new RuntimeException("文件数据源稽核，日志插入异常.");
        }
    }

    @Override
    public PageInfo<AuditFileDatasourceLogResDto> qryAuditDatasourceLogWithPageInfo(AuditFileDatasourceLogQryDto condition) {
        PageHelper.startPage(condition.getCurrent(), condition.getPageSize());
        List<AuditFileDatasourceLogResDto> res = logMapper.qryAuditFileDatasourceLogByCondition(condition);
        return new PageInfo<>(res);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void delBatchAuditFileDatasourceLog(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new IllegalArgumentException("ids为空.");
        }
        int res = logMapper.delBatchAuditFileDatasourceLog(ids);
        if (res != ids.size()) {
            throw new RuntimeException("文件数据源稽核，日志删除异常.");
        }
    }
}
