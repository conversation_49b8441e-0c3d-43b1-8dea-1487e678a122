package com.suke.czx.util;

import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@Component
public class JwtUtil {

    public static final String SECRET_KEY = "xing-dou\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0";

    /**
     * 一个小时过期
     */
    public static final long EXPIRE_TIME = 1000 * 60 * 60;

    /**
     * 生成token
     * @param payload 传入负载
     * @return
     */
    /**
     * 生成token
     * @param claims 传入负载
     * @return
     */
    public String generateToken(Map<String, Object> claims) {
        try {
            // 使用固定的密钥字符串
            SecretKey key = Keys.hmacShaKeyFor(SECRET_KEY.getBytes(StandardCharsets.UTF_8));
            return Jwts.builder()
                    .setClaims(claims)
                    .signWith(key,SignatureAlgorithm.HS256)
                    .compact();
        } catch (Exception e) {
            log.error("token生成失败", e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析token
     * @param token
     * @return
     */
    public Map<String, Object> parseToken(String token) {
        try {
            SecretKey key = Keys.hmacShaKeyFor(SECRET_KEY.getBytes(StandardCharsets.UTF_8));
            return Jwts.parserBuilder()
                    .setSigningKey(key)
                    .setAllowedClockSkewSeconds(Integer.MAX_VALUE)
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (JwtException | IllegalArgumentException e) {
            log.error("无效的token", e);
            throw new RuntimeException(e);
        }
    }


}
