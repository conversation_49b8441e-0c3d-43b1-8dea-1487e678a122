package com.suke.czx.modules.sys.dao;

import com.suke.czx.modules.sys.entity.SysUserTokenEntity;
import org.apache.ibatis.annotations.Mapper;

/**
 * 系统用户Token
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017-03-23 15:22:07
 */
@Mapper
public interface SysUserTokenDao extends BaseDao<SysUserTokenEntity> {
    
    SysUserTokenEntity queryByUserId(String userId);

    SysUserTokenEntity queryByToken(String token);

    //测试时间类型是否可以正确插入
    int saveToken(SysUserTokenEntity tokenEntity);
}
