package com.suke.czx.modules.AI.util;

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import com.alibaba.fastjson.JSONObject;
import com.suke.czx.modules.audit.util.Ftp;
import org.assertj.core.util.Lists;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class ConvertUtil {
    public static int convertToCsv(List<JSONObject> param, Ftp ftp, String csvFilename, String baseFilePath) throws Exception {
        Objects.requireNonNull(param);
        Objects.requireNonNull(csvFilename);
        ByteArrayOutputStream byteOutPut = new ByteArrayOutputStream();
        BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(byteOutPut, StandardCharsets.UTF_8));
        CsvWriter csvWriter = CsvUtil.getWriter(bw);
        JSONObject rowOne = param.get(0);
        Set<String> heads = rowOne.keySet();
        List<String> headList = Lists.newArrayList(heads);
        String headsStr = String.join(",", headList);
        csvWriter.write(Lists.newArrayList(headsStr));
        for (JSONObject item : param) {
            List<String> values = new ArrayList<>();
            for (String header : headList) {
                // 尝试从JSONObject中获取值，如果键不存在则填充空字符串或进行其他处理
                String value = item.getString(header);
                if (item.containsKey(header)) {
                    values.add(value);
                } else {
                    values.add("");
                }
            }
            csvWriter.write(Lists.newArrayList(String.join(",", values)));
        }
        csvWriter.flush();
        bw.flush();
        byteOutPut.flush();
        int fileSize = byteOutPut.size();
        ftp.sshSftp(byteOutPut.toByteArray(), csvFilename, baseFilePath);
        csvWriter.close();
        bw.close();
        bw.close();
        return fileSize;
    }
}
