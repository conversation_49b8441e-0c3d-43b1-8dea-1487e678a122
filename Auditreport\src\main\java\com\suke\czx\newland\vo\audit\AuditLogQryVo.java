package com.suke.czx.newland.vo.audit;

import lombok.Data;

import java.util.Date;

@Data
public class AuditLogQryVo {
    private long    logId;             //日志编号
    private String  dbName;            //执行DB用户名
    private String  dbMachine;         //执行主目路径
    private long    ruleId;            //规则编号
    private String  modifyPerson;      //规则最近修改人
    private Date modifyDate;        //规则最近修改时间
    private String  chkTypeId;        //规则项编号
    private String  chkTypeName;      //规则项名称
    private String  centerId;          //业务中心编号
    private String  centerName;        //业务中心名称
    private String  modleId;           //业务模块编号
    private String  modleName;        //业务模块名
    private String  ruleName;          //规则名称
    private String  ruleSql;           //规则SQL
    private String  limitValue;        //规则阈值
    private String  sqlValue;          //规则SQL值
    private short   status;             //状态
    private String  conclusion;         //执行结果
    private long    execMsec;          //执行时间（毫秒）
    private String  execRemark;        //执行备注
    private Date    createDate;        //日志执行时间
    private String  createPerson;      //日志执行人
    private Date    execDate;
    private String  execPerson;
    private String analysisRes;

    private String datasourceId;

    private Date beginTime;
    private Date endTime;
}
