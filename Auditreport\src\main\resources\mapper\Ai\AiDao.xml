<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.AI.dao.AiFileDao">

    <insert id="save" parameterType="list" useGeneratedKeys="false">
        <foreach collection="list" item="file" separator="union">
            insert into AUDIT_aiFile (id, name, report_size, type, url, upload_user_id)
            values (AUDIT_UPLOAD_AI_FILE_SEQ.nextval, #{file.name}, #{file.reportSize}, #{file.type}, #{file.url}, #{file.uploadUserId})
        </foreach>
    </insert>
    <insert id="insertAuditedAIFileInfo">
        insert into AUDIT_aiFile (id, name, report_size, type, url, upload_user_id, SOURCE_FILE_ID)
        values (AUDIT_UPLOAD_AI_FILE_SEQ.nextval, #{name}, #{reportSize}, #{type}, #{url},
                #{uploadUserId}, #{sourceFileId})
    </insert>
    <update id="updateAiFileTypesByFileId">
        update AUDIT_aiFile set type = '1' where id = #{id}
    </update>
    <delete id="delByIds">
        update AUDIT_aiFile set DELETED = '1' where ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>

    <select id="qyrAuditAiFileWithCondition" resultType="com.suke.czx.modules.AI.entity.AiFile">
        select ID,
        NAME,
        REPORT_SIZE,
        TYPE,
        URL,
        CREATE_TIME,
        UPLOAD_USER_ID from AUDIT_aiFile where
        1 = 1
        <if test="fileNameKeyword != null and fileNameKeyword != ''">
            and NAME like concat(concat('%',#{fileNameKeyword}),'%')
        </if>
        and DELETED = '0' and type != '2'
        order by CREATE_TIME desc
    </select>
    <select id="qyrAuditAiFileWithId" resultType="com.suke.czx.modules.AI.entity.AiFile">
        select ID,
               NAME,
               REPORT_SIZE,
               TYPE,
               URL,
               CREATE_TIME,
               UPLOAD_USER_ID from AUDIT_aiFile where ID = #{id}
    </select>
    <select id="qyrAuditAiFileWithIds" resultType="com.suke.czx.modules.AI.entity.AiFile">
        select ID,
        NAME,
        REPORT_SIZE,
        TYPE,
        URL,
        CREATE_TIME,
        UPLOAD_USER_ID from AUDIT_aiFile where
        ID in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and DELETED = '0'
        order by CREATE_TIME desc
    </select>
    <select id="qryAuditAiFileWithName" resultType="com.suke.czx.modules.AI.entity.AiFile">
        select ID,
               NAME,
               REPORT_SIZE,
               TYPE,
               URL,
               CREATE_TIME,
               UPLOAD_USER_ID
        from AUDIT_aiFile
        where name = #{name}
          and DELETED = '0'
          and type = '0'
        union all
        select ID,
               NAME,
               REPORT_SIZE,
               TYPE,
               URL,
               CREATE_TIME,
               UPLOAD_USER_ID
        from AUDIT_aiFile
        where name = #{name}
          and DELETED = '0'
          and type = '1'
    </select>
    <select id="qryAuditAiFileWithNameType" resultType="com.suke.czx.modules.AI.entity.AiFile">
        select ID,
               NAME,
               REPORT_SIZE,
               TYPE,
               URL,
               CREATE_TIME,
               UPLOAD_USER_ID
        from AUDIT_aiFile
        where name = #{name} and DELETED = '0' and type = '0'
    </select>
    <select id="qryAuditedAiFileBySourceFileId" resultType="com.suke.czx.modules.AI.entity.AiFile">
        select ID,
               NAME,
               REPORT_SIZE,
               TYPE,
               URL,
               CREATE_TIME,
               UPLOAD_USER_ID,
               SOURCE_FILE_ID
        from AUDIT_aiFile
        where SOURCE_FILE_ID = #{sourceFileId}
    </select>


</mapper>