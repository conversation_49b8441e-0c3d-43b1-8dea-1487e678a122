package com.suke.czx.newland.vo.cfg;

import com.suke.czx.newland.util.ThreadPoolUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;



/**
 * 文件名称：InitCfg
 * 描    述：初始化公共配置
 *              - 线程池
 * 作    者：xumengfan
 * 创建日期：2024-03-4
 * 创建时间：16:28
 * 当前版本：1.0
 */
@Component
public class InitCfg implements ApplicationRunner
{
    private static final Logger logger = LoggerFactory.getLogger(InitCfg.class);

    @Autowired
//    private ISmSysCfgService iSmSysCfgService;         // 系统参数配置接口服务
//    @Autowired
//    private ISmNoticeSendService iSmNoticeSendService; // 通知工单轮询发送接口
//    @Autowired
//    private ISmFlwJobService iSmFlwJobService;         // 作业管理
//    @Autowired
//    private ICronService iCronService;                 // 定时任务控制
//    @Value("${notice-send}")
//    private String noticeSend;
//    @Value("${cron-exec}")
//    private String cronExec;

    /**
     * 容器启动
     * <AUTHOR>
     * 时间 2020-07-16
     */
    @Override
    public void run(ApplicationArguments args) throws Exception
    {
        int corePoolSize = -1;
        int maximumPoolSize = -1;
        int keepAliveTime = -1;
        // 捞取系统线程池配置信息
//        List<Map<String, Object>> mapList = iSmSysCfgService.queryThreadPoolParam();
//        for (Map<String, Object> map : mapList)
//        {
//            switch (map.get("CFG_ID").toString())
//            {
//                case "1001":
//                    corePoolSize = DataUtils.protectStrToInt(map.get("CFG_VALUE").toString());
//                    break;
//                case "1002":
//                    maximumPoolSize = DataUtils.protectStrToInt(map.get("CFG_VALUE").toString());
//                    break;
//                case "1003":
//                    keepAliveTime = DataUtils.protectStrToInt(map.get("CFG_VALUE").toString());
//                    break;
//            }
//        }

        ThreadPoolUtil util = ThreadPoolUtil.getInstance();
        // 初始化线程池
        util.init(50, 200, 300);


    }


}
