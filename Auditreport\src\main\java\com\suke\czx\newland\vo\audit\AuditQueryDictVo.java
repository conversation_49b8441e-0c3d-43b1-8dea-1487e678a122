package com.suke.czx.newland.vo.audit;

import java.util.List;

public class AuditQueryDictVo {

    private AuditDictTypeVo typeVo;
    private List<AuditDictQueryVo> dictList;

    public AuditDictTypeVo getTypeVo() {
        return typeVo;
    }

    public void setTypeVo(AuditDictTypeVo typeVo) {
        this.typeVo = typeVo;
    }

    public List<AuditDictQueryVo> getDictList() {
        return dictList;
    }

    public void setDictList(List<AuditDictQueryVo> dictList) {
        this.dictList = dictList;
    }

    @Override
    public String toString() {
        return "AuditQueryDictVo{" +
                "typeVo=" + typeVo +
                ", dictList=" + dictList +
                '}';
    }
}
