package com.suke.czx.newland.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * 文件名称：SmFlwResourceVo
 * 描    述：【资源配置表】【sm_flw_resource】与【流程节点 + 任务】一起关联的任务资源
 *              说明：由于流程节点ID是唯一的，但是任务是可以复用的；且相同任务可能会绑定不同的资源，
 *                      所以只有在明确流程节点ID和任务ID的情况下，才能得到资源ID（由BPMN图保存）
 * 作    者：jianggy
 * 创建日期：2020-06-17
 * 创建时间：17:49
 * 当前版本：1.0
 */
public class SmFlwResourceVo
{
    private String resId;
    private String resName;
    private int resType; // 1、Linux-SSH 2、Oracle 3、API 4、时序库 5、Linux-Salt 6、Oracle-AutoBindName
    private String resAddr;
    private String loginAcc;
    private String tns;      // 暂用于自动获取密码
    private String password; // 密码，暂用于SaltStack的Master
    private short status;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;
    private String createOperator;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date modiDate;
    private String modiOperator;
    private String remark;
    private boolean linkTest; // 数据库连接测试结果存放字段
    private int fix; // 数据库连接测试结果存放字段

    public boolean isLinkTest()
    {
        return linkTest;
    }

    public void setLinkTest(boolean linkTest)
    {
        this.linkTest = linkTest;
    }

    public String getResId()
    {
        return resId;
    }

    public void setResId(String resId)
    {
        this.resId = resId;
    }

    public String getResName()
    {
        return resName;
    }

    public void setResName(String resName)
    {
        this.resName = resName;
    }

    public int getResType()
    {
        return resType;
    }

    public void setResType(int resType)
    {
        this.resType = resType;
    }

    public String getResAddr()
    {
        return resAddr;
    }

    public void setResAddr(String resAddr)
    {
        this.resAddr = resAddr;
    }

    public String getLoginAcc()
    {
        return loginAcc;
    }

    public void setLoginAcc(String loginAcc)
    {
        this.loginAcc = loginAcc;
    }

    public String getTns() {
        return tns;
    }

    public void setTns(String tns) {
        this.tns = tns;
    }

    public short getStatus()
    {
        return status;
    }

    public void setStatus(short status)
    {
        this.status = status;
    }

    public Date getCreateDate()
    {
        return createDate;
    }

    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    public String getCreateOperator()
    {
        return createOperator;
    }

    public void setCreateOperator(String createOperator)
    {
        this.createOperator = createOperator;
    }

    public Date getModiDate()
    {
        return modiDate;
    }

    public void setModiDate(Date modiDate)
    {
        this.modiDate = modiDate;
    }

    public String getModiOperator()
    {
        return modiOperator;
    }

    public void setModiOperator(String modiOperator)
    {
        this.modiOperator = modiOperator;
    }

    public String getRemark()
    {
        return remark;
    }

    public void setRemark(String remark)
    {
        this.remark = remark;
    }

    public String getPassword()
    {
        return password;
    }

    public void setPassword(String password)
    {
        this.password = password;
    }

    public int getFix() {
        return fix;
    }

    public void setFix(int fix) {
        this.fix = fix;
    }

    @Override
    public String toString() {
        return "SmFlwResourceVo{" +
                "resId='" + resId + '\'' +
                ", resName='" + resName + '\'' +
                ", resType=" + resType +
                ", resAddr='" + resAddr + '\'' +
                ", loginAcc='" + loginAcc + '\'' +
                ", tns='" + tns + '\'' +
                ", password='" + password + '\'' +
                ", status=" + status +
                ", createDate=" + createDate +
                ", createOperator='" + createOperator + '\'' +
                ", modiDate=" + modiDate +
                ", modiOperator='" + modiOperator + '\'' +
                ", remark='" + remark + '\'' +
                ", linkTest=" + linkTest +
                ", fix=" + fix +
                '}';
    }
}
