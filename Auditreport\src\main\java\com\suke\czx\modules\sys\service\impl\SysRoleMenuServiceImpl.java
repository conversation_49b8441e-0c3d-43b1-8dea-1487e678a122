package com.suke.czx.modules.sys.service.impl;

import com.suke.czx.modules.sys.dao.SysRoleMenuDao;
import com.suke.czx.modules.sys.service.SysRoleMenuService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;



/**
 * 角色与菜单对应关系
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:44:35
 */
@Service("sysRoleMenuService")
public class SysRoleMenuServiceImpl implements SysRoleMenuService {
	@Autowired
	private SysRoleMenuDao sysRoleMenuDao;

	@Override
	@Transactional
	public void saveOrUpdate(Long roleId, List<Long> menuIdList) {
		//先删除角色与菜单关系
		sysRoleMenuDao.delete(roleId);

		if(menuIdList.size() == 0){
			return ;
		}

		//保存角色与菜单关系
		Map<String, Object> map = new HashMap<>();
		map.put("roleId", roleId);
		map.put("menuIdList", menuIdList);
		sysRoleMenuDao.save(map);
	}

	@Override
	public List<Long> queryMenuIdList(Long roleId) {
		return sysRoleMenuDao.queryMenuIdList(roleId);
	}

	/**
	 * 根据角色id查询角色角色菜单管理表中是否存在关联数据，若存在，则不允许删除
	 * @param id
	 * @return
	 */
	@Override
	public Integer queryRoleById(Long id) {
		return sysRoleMenuDao.queryTotal(id);
	}

	@Override
	@Transactional
	public void saveRoleMenu(Long roleId, Long[] menuIds) {
		//先删除角色与菜单关系
		sysRoleMenuDao.delete(roleId);
		for (Long item : menuIds) {
			sysRoleMenuDao.saveRoleMenu(roleId,item);
		}
	}

	/**
	 * 根据菜单id查询角色菜单关联表
	 * @param menuId
	 * @return
	 */
	@Override
	public Integer queryTotal(Long menuId) {
		return sysRoleMenuDao.queryTotal(menuId);
	}


}
