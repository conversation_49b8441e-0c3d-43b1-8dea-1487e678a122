<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.modules.aitrain.mapper.ModelTrainMapper">
    <insert id="insert">
        insert into ai_model_train(id,train_name,model,score_test,eval_metric,fit_time,can_infer,fit_order,remark)
        values
            (ai_model_train_seq.nextval,#{trainName,jdbcType=VARCHAR},#{model,jdbcType=VARCHAR},#{scoreTest,jdbcType=DOUBLE},#{evalMetric,jdbcType=VARCHAR},#{fitTime,jdbcType=DOUBLE},#{canInfer,jdbcType=VARCHAR},#{fitOrder,jdbcType=INTEGER},#{remark,jdbcType=VARCHAR})
        <selectKey keyProperty="id" resultType="int" order="AFTER">
            SELECT ai_model_train_seq.CURRVAL AS id FROM dual
        </selectKey>
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_model_train(id, train_name, model, score_test, eval_metric, fit_time, can_infer, fit_order, remark)
        select ai_model_train_seq.nextval,a.* from (
        <foreach collection="list" item="item" separator="union">
            select #{item.trainName,jdbcType=VARCHAR},
            #{item.model,jdbcType=VARCHAR},
            #{item.scoreTest,jdbcType=DOUBLE},
            #{item.evalMetric,jdbcType=VARCHAR},
            #{item.fitTime,jdbcType=DOUBLE},
            #{item.canInfer,jdbcType=VARCHAR},
            #{item.fitOrder,jdbcType=INTEGER},
            #{item.remark,jdbcType=VARCHAR}
            from dual
        </foreach>
        ) a
    </insert>
    <update id="update">
        UPDATE ai_model_train
        SET
        <if test="trainName != null and trainName != ''">
            train_name = #{trainName,jdbcType=VARCHAR},
        </if>
        <if test="model != null and model != ''">
            model = #{model,jdbcType=VARCHAR},
        </if>
        <if test="scoreTest != null">
            score_test = #{scoreTest,jdbcType=DOUBLE},
        </if>
        <if test="evalMetric != null and evalMetric != ''">
            eval_metric = #{evalMetric,jdbcType=VARCHAR},
        </if>
        <if test="fitTime != null">
            fit_time = #{fitTime,jdbcType=DOUBLE},
        </if>
        <if test="canInfer != null and canInfer != ''">
            can_infer = #{canInfer,jdbcType=VARCHAR},
        </if>
        <if test="fitOrder != null">
            fit_order = #{fitOrder,jdbcType=INTEGER},
        </if>
        <if test="remark != null and remark != ''">
            remark = #{remark,jdbcType=VARCHAR},
        </if>
        WHERE
        id = #{id,jdbcType=INTEGER}
    </update>
    <delete id="deleteById">
        delete from ai_model_train where id = #{id,jdbcType=INTEGER}
    </delete>


    <select id="getAll" resultType="com.suke.czx.modules.aitrain.entity.ModelTrain">
        select id,train_name,model,score_test,eval_metric,fit_time,can_infer,fit_order,create_time from ai_model_train
    </select>
    <select id="getTrainNameList" resultType="java.lang.String">
        SELECT train_name FROM ai_model_train group by train_name
    </select>
    <select id="queryByTrainName" resultType="com.suke.czx.modules.aitrain.entity.ModelTrain">
        select id,train_name,model,score_test,eval_metric,fit_time,can_infer,fit_order,remark,create_time from ai_model_train where train_name = #{trainName,jdbcType=VARCHAR}
    </select>
    <select id="queryAllTrainName" resultType="java.lang.String">
        SELECT
            t.train_name
        FROM (
                 SELECT
                     train_name,
                     MAX(create_time) AS max_create_time
                 FROM
                     ai_model_train
                 GROUP BY
                     train_name
             ) t
        ORDER BY
            t.max_create_time DESC
    </select>
    <select id="getModelSum" resultType="java.lang.Integer">
        SELECT count(1) FROM (select train_name from ai_model_train group by train_name)
    </select>
    <select id="getModelByTrainName" resultType="java.lang.String">
        SELECT model FROM ai_model_train where train_name=#{trainName,jdbcType=VARCHAR}
    </select>
    <select id="getScoreTestByTrainName" resultType="java.lang.Double">
        SELECT score_test FROM ai_model_train where train_name=#{trainName,jdbcType=VARCHAR}
    </select>
    <select id="getFitTimeByTrainName" resultType="java.lang.Double">
        SELECT fit_time FROM ai_model_train where train_name=#{trainName,jdbcType=VARCHAR}
    </select>
</mapper>