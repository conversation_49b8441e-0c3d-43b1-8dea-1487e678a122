package com.suke.czx.modules.log.controller;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.log.dto.OperationLogDTO;
import com.suke.czx.modules.log.entity.OperationEntity;
import com.suke.czx.modules.log.service.OperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 操作日志
 */
@RestController
@RequestMapping("/audit/log")
public class OperationController {

    @Autowired
    private OperationService operationService;

    /**
     * 查询所有日志列表
     * @return
     */
    @RequestMapping(value = "/list",method = RequestMethod.POST)
    public R getAllLog(@RequestParam(defaultValue = "1") Integer current,
                       @RequestParam(defaultValue = "10") Integer pageSize,
                       @RequestBody OperationLogDTO operationLogDTO) {

        System.out.println("==============参数：" + operationLogDTO);
        System.out.println(operationLogDTO == null);
        String userName = "";
        String operation = "";

        assert operationLogDTO != null;
        if (operationLogDTO.getUsername() != null) {
            userName = operationLogDTO.getUsername();
        }
        if (operationLogDTO.getOperation() != null) {
            operation = operationLogDTO.getOperation();
        }
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        LocalDateTime startTime = null;
        LocalDateTime endTime = null;
        if (operationLogDTO.getStartTime() != null && !Objects.equals(operationLogDTO.getStartTime(), "")) {
            System.out.println("开始日期：" + operationLogDTO.getStartTime());
            startTime = LocalDateTime.parse(operationLogDTO.getStartTime(), df);
        }
        if (operationLogDTO.getEndTime() != null && !Objects.equals(operationLogDTO.getEndTime(), "")) {
            System.out.println("结束日期：" + operationLogDTO.getEndTime());
            endTime = LocalDateTime.parse(operationLogDTO.getEndTime(), df);
        }

        //开启分页
        PageHelper.startPage(current,pageSize);
        List<OperationEntity> operationList = operationService.queryLogList(userName,operation);
        if (startTime != null && endTime != null) {
            //根据时间范围对结果进行过滤
            LocalDateTime finalEndTime = endTime;
            LocalDateTime finalStartTime = startTime;
            List<OperationEntity> finalList = operationList.stream().filter(item -> item.getCreateTime().isBefore(finalEndTime) && item.getCreateTime().isAfter(finalStartTime)).collect(Collectors.toList());

            //封装
            PageInfo<OperationEntity> operationEntityPageInfo = new PageInfo<>(finalList);
            long pageTotal = operationEntityPageInfo.getTotal();
            // System.out.println("总条数：" + operationEntityPageInfo.getTotal());
            // System.out.println("每页大小：" + operationEntityPageInfo.getPageSize());
            return R.ok().put("logList",finalList).put("total",pageTotal);
        }else {
            //封装
            PageInfo<OperationEntity> operationEntityPageInfo = new PageInfo<>(operationList);
            long pageTotal = operationEntityPageInfo.getTotal();
            // System.out.println("总条数：" + operationEntityPageInfo.getTotal());
            // System.out.println("每页大小：" + operationEntityPageInfo.getPageSize());
            return R.ok().put("logList",operationList).put("total",pageTotal);
        }
    }
}
