package com.suke.czx.modules.aitrain.utils;

import com.suke.czx.config.FtpProperties;
import com.suke.czx.modules.aitrain.entity.ModelTrain;
import com.suke.czx.modules.aitrain.entity.ModelTrainParam;
import com.suke.czx.modules.aitrain.mapper.ModelTrainMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * 模型训练工具类
 */
@Slf4j
@Component
public class ModelTrainUtil {

    @Resource
    private ModelTrainMapper modelTrainMapper;

    /**
     * 调用python脚本执行AI模型训练
     * @param modelTrainParam
     * @param modelName
     * @param normalPath
     * @param abnormalPath
     * @param ftpProperties
     * @return
     */
    public String trainModel(ModelTrainParam modelTrainParam, String modelName, String normalPath, String abnormalPath, FtpProperties ftpProperties,int modelTrainId) {
        log.info("模型训练开始，模型名称：{}，正常文件路径：{}，异常文件路径：{}", modelName, normalPath, abnormalPath);
        //脚本地址：D:/PycharmWorkspace/pythonProject1/model/train_api.py --anomaly_path D:/PycharmWorkspace/pythonProject1/data/训练数据-异常_Sheet1.csv --normal_path D:/PycharmWorkspace/pythonProject1/data/训练数据-正常_Sheet1.csv --path D:/PycharmWorkspace/pythonProject1/AutogluonModels/pumpkin
        //python .\model\train_api.py --anomaly_path data/训练数据-异常_Sheet1.csv --normal_path data/训练数据-正常_Sheet1.csv --label_column label --metric balanced_accuracy --preset medium_quality --test_size 0.2 --random_state 1926 --problem_type binary --path AutogluonModels/pumpkin
        //基础参数
        String scriptParam = ftpProperties.getPythonbasePath() + (ftpProperties.getPythonbasePath().endsWith(File.separator) ? "" : File.separator) + "model" + File.separator+ "train_api.py";
        String abnormalName = "--anomaly_path";
        //这里的参数需要改为传递进来的入参：abnormalPath和normalPath
//        String abnormalFilePathParam = "D:\\PycharmWorkspace\\pythonProject1\\data\\训练数据-异常_Sheet1.csv";
        String abnormalFilePathParam = abnormalPath;
        String normalName = "--normal_path";
//        String normalFilePathParam = "D:\\PycharmWorkspace\\pythonProject1\\data\\训练数据-正常_Sheet1.csv";
        String normalFilePathParam = normalPath;
        //训练的指标参数
        String labelColumnName = "--label_column";
        String labelColumnParam = modelTrainParam.getLabelColumn();
        String metricName = "--metric";
        String metricParam = modelTrainParam.getMetric();
        String presetName = "--preset";
        String presetParam = modelTrainParam.getPreset();
        String testSizeName = "--test_size";
        String testSizeParam = modelTrainParam.getTestSize();
        String randomStateName = "--random_state";
        String randomStateParam = modelTrainParam.getRandomState();
        String problemTypeName = "--problem_type";
        String problemTypeParam = modelTrainParam.getProblemType();
        //模型路径
        String modelPathName = "--path";
        String modelPathParam = ftpProperties.getPythonbasePath() + (ftpProperties.getPythonbasePath().endsWith(File.separator) ? "" : File.separator) + "AutogluonModels" + File.separator + modelName;
        //训练结果输出路径
        String outputPathName = "--output_leaderboard_path";
        String outputFileName = "leaderboard_" + DateUtils.getCurrentTimestampLegacy() +".csv";
        //结果统一放在result目录下（完整的路径）
        String outputPathParam = ftpProperties.getPythonbasePath() + (ftpProperties.getPythonbasePath().endsWith(File.separator) ? "" : File.separator) + "result" + File.separator + outputFileName;

        // 创建一个ArrayList来存储命令参数
        List<String> commandList = new ArrayList<>();
        if (ftpProperties.getContainerName() == null) {
            //普通部署
            commandList.add(ftpProperties.getPythonPath());
//            commandList.add(ftpProperties.getPythonbasePath() + "/model/train_api.py");
            commandList.add(scriptParam);

            // 添加参数
            commandList.add(abnormalName);
            commandList.add(abnormalFilePathParam);
            commandList.add(normalName);
            commandList.add(normalFilePathParam);
            commandList.add(labelColumnName);
            commandList.add(labelColumnParam);
            commandList.add(metricName);
            commandList.add(metricParam);
            commandList.add(presetName);
            commandList.add(presetParam);
            commandList.add(testSizeName);
            commandList.add(testSizeParam);
            commandList.add(randomStateName);
            commandList.add(randomStateParam);
            commandList.add(outputPathName);
            commandList.add(outputPathParam);

            // 检查problemTypeParam是否为None，如果不是则添加参数
            if (!"None".equals(problemTypeParam)) {
                commandList.add(problemTypeName);
                commandList.add(problemTypeParam);
            }

            // 添加模型路径参数
            commandList.add(modelPathName);
            commandList.add(modelPathParam);
        }else {
            //容器部署
            commandList.add("python3");
            commandList.add("/app/pythonProject1/model/train_api.py");

            if (abnormalFilePathParam.contains("abnormalpath")) {
                //说明是异常文件地址
                int lastIndexOf = abnormalFilePathParam.lastIndexOf("/");
                String fileName = "";
                if (lastIndexOf != -1) {
                    fileName = abnormalFilePathParam.substring(lastIndexOf + 1);
                } else {
                    // 如果没有找到斜杠，则整个字符串都是文件名
                    fileName = abnormalFilePathParam;
                }
                abnormalFilePathParam = "/app/pythonProject1/abnormalpath/" + fileName;
                log.info("容器部署中的异常文件地址为：{}",abnormalFilePathParam);
            }
            //如果文件字段发生修改
            if (abnormalFilePathParam.contains("finalTrainPath")) {
                //说明是异常文件地址
                int lastIndexOf = abnormalFilePathParam.lastIndexOf("/");
                String fileName = "";
                if (lastIndexOf != -1) {
                    fileName = abnormalFilePathParam.substring(lastIndexOf + 1);
                } else {
                    // 如果没有找到斜杠，则整个字符串都是文件名
                    fileName = abnormalFilePathParam;
                }
                abnormalFilePathParam = "/app/pythonProject1/finalTrainPath/" + fileName;
                log.info("容器部署中的异常文件地址为：{}",abnormalFilePathParam);
            }
            // 添加参数
            commandList.add(abnormalName);
            commandList.add(abnormalFilePathParam);
            commandList.add(normalName);
            if (normalFilePathParam.contains("normalpath")) {
                //说明是正常文件地址
                int lastIndexOf = normalFilePathParam.lastIndexOf("/");
                String fileName = "";
                if (lastIndexOf != -1) {
                    fileName = normalFilePathParam.substring(lastIndexOf + 1);
                } else {
                    // 如果没有找到斜杠，则整个字符串都是文件名
                    fileName = normalFilePathParam;
                }
                normalFilePathParam = "/app/pythonProject1/normalpath/" + fileName;
                log.info("容器部署中的正常文件地址为：{}",normalFilePathParam);
            }
            //如果文件字段发生修改
            if (normalFilePathParam.contains("finalTrainPath")) {
                //说明是正常文件地址
                int lastIndexOf = normalFilePathParam.lastIndexOf("/");
                String fileName = "";
                if (lastIndexOf != -1) {
                    fileName = normalFilePathParam.substring(lastIndexOf + 1);
                } else {
                    // 如果没有找到斜杠，则整个字符串都是文件名
                    fileName = normalFilePathParam;
                }
                normalFilePathParam = "/app/pythonProject1/finalTrainPath/" + fileName;
                log.info("容器部署中的正常文件地址为：{}",normalFilePathParam);
            }

            commandList.add(normalFilePathParam);
            commandList.add(labelColumnName);
            commandList.add(labelColumnParam);
            commandList.add(metricName);
            commandList.add(metricParam);
            commandList.add(presetName);
            commandList.add(presetParam);
            commandList.add(testSizeName);
            commandList.add(testSizeParam);
            commandList.add(randomStateName);
            commandList.add(randomStateParam);
            commandList.add(outputPathName);
            outputPathParam = "/app/pythonProject1/result/" + outputFileName;
            commandList.add(outputPathParam);

            // 检查problemTypeParam是否为None，如果不是则添加参数
            if (!"None".equals(problemTypeParam)) {
                commandList.add(problemTypeName);
                commandList.add(problemTypeParam);
            }

            // 添加模型路径参数
            commandList.add(modelPathName);
            modelPathParam = "/app/pythonProject1/AutogluonModels/" + modelName;
            commandList.add(modelPathParam);
        }
        // 添加基本命令（docker中需要进行调整）
//        commandList.add("cmd.exe");
//        commandList.add("/c");
//        commandList.add("python");
//        commandList.add(scriptParam);


        try {
            ProcessBuilder pb = new ProcessBuilder(commandList);
            pb.redirectErrorStream(true); // 将错误流重定向到标准输出流

            // 获取命令列表
            List<String> commandParamList = pb.command();
            // 将命令列表转换成字符串
            StringBuilder commandStringBuilder = new StringBuilder();
            for (String cmd : commandParamList) {
                if (commandStringBuilder.length() > 0) {
                    commandStringBuilder.append(" ");
                }
                commandStringBuilder.append(cmd);
            }
            // 打印命令行字符串
            log.info("执行的命令行字符串：{}", commandStringBuilder);


            Process process = pb.start();
            // 读取并打印输出结果
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            StringBuilder output = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            int exitCode = process.waitFor();
            log.info("退出码：{}", exitCode);
            log.info("模型训练结束，输出结果：{}", output.toString());
            return outputPathParam;
        } catch (IOException | InterruptedException e) {
            //执行异常，更新状态
            ModelTrain modelTrain = new ModelTrain();
            modelTrain.setId(modelTrainId);
            modelTrain.setRemark("执行AI模型训练异常");
            int updateRes = modelTrainMapper.update(modelTrain);
            log.error("更新模型训练状态结果条数：{}", updateRes);
            log.error("模型训练异常：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
