package com.suke.czx.newland.vo.audit;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@Data
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class AuditConfigVo {

    private long ruleId;
    private String ruleName;
    private short status;
    private String createPerson;
    private Date createDate;
    private String modifyPerson;
    private Date modifyDate;
    private String chkTypeId;
    private String centerId;
    private String modleId;
    private String ruleSql;
    private String limitValue;
    private String ruleDesc;
    private String envType;
    private String limitSign;
}
