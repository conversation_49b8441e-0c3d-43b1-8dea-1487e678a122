package com.suke.czx.modules.sys.controller;

import com.alibaba.druid.util.Utils;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.R;
import com.suke.czx.common.validator.Assert;
import com.suke.czx.common.validator.ValidatorUtils;
import com.suke.czx.common.validator.group.AddGroup;
import com.suke.czx.common.validator.group.UpdateGroup;
import com.suke.czx.modules.sys.entity.SysUserAddVo;
import com.suke.czx.modules.sys.entity.SysUserEntity;
import com.suke.czx.modules.sys.entity.UserListQryVo;
import com.suke.czx.modules.sys.service.SysUserRoleService;
import com.suke.czx.modules.sys.service.SysUserService;
import org.apache.commons.lang.ArrayUtils;
import org.apache.shiro.authz.annotation.RequiresAuthentication;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.authz.annotation.RequiresRoles;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 系统用户
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月31日 上午10:40:10
 */
@RestController
@RequestMapping("/audit/user")
public class SysUserController extends AbstractController {
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysUserRoleService sysUserRoleService;
	
	/**
	 * 所有用户列表
	 */
	@PostMapping("/list")
	// @RequiresPermissions("sys:user:list")
	public R list(@RequestBody(required = false) UserListQryVo condition){
		List<SysUserEntity> userList = sysUserService.queryList(condition);
		return R.ok().put("userList", userList);
	}
	
	/**
	 * 获取登录的用户信息
	 */
	@RequestMapping("/info")
	public R info(){
		return R.ok().put("user", getUser());
	}

	/**
	 * 获取当前登录的用户id
	 * @return
	 */
	@RequestMapping(value = "/userId",method = RequestMethod.GET)
	public R userId() {
		return R.ok().put("userId",getUserId());
	}
	
	/**
	 * 修改登录用户密码
	 */
	@SysLog("修改密码")
	@RequestMapping("/password")
	public R password(String password, String newPassword){
		Assert.isBlank(newPassword, "新密码不为能空");
		
		//sha256加密
		password = new Sha256Hash(password, "111").toHex();
		//sha256加密
		newPassword = new Sha256Hash(newPassword, "111").toHex();
				
		//更新密码
		int count = sysUserService.updatePassword(getUserId(), password, newPassword);
		if(count == 0){
			return R.error("原密码不正确");
		}
		
		return R.ok();
	}
	
	/**
	 * 用户信息
	 */
	@RequestMapping("/info/{userId}")
	// @RequiresPermissions("sys:user:info")
	public R info(@PathVariable("userId") String userId){
		SysUserEntity user = sysUserService.queryObject(userId);
		
		//获取用户所属的角色列表
		List<Long> roleIdList = sysUserRoleService.queryRoleIdList(userId);
		// user.setRoleIdList(roleIdList);
		
		return Objects.requireNonNull(R.ok().put("user", user)).put("roleIdList",roleIdList);
	}
	
	/**
	 * 保存用户
	 */
	@SysLog("保存用户")
	@RequestMapping("/save")
	@RequiresPermissions("sys:user:save")
	public R save(@RequestBody SysUserAddVo user){
		try {
			ValidatorUtils.validateEntity(user, AddGroup.class);
			sysUserService.save(user);
		}catch (IllegalArgumentException e){
			logger.error(Utils.getStackTrace(e));
			return R.error(e.getMessage());
		}
		return R.ok();
	}
	
	/**
	 * 修改用户
	 */
	@SysLog("修改用户")
	@RequestMapping("/update")
	// @RequiresPermissions("sys:user:update")
	public R update(@RequestBody SysUserEntity user){
		ValidatorUtils.validateEntity(user, UpdateGroup.class);
		
		// user.setCreateUserId(getUserId());
		sysUserService.update(user);
		
		return R.ok();
	}
	
	/**
	 * 删除用户
	 */
	@SysLog("删除用户")
	@RequestMapping("/delUserBatch")
	@RequiresRoles({"ROLE_ADMIN"})
	public R delete(@RequestBody String[] userIds){
		try {
			if(ArrayUtils.contains(userIds, 1L)){
				return R.error("系统管理员不能删除");
			}

			if(ArrayUtils.contains(userIds, getUserId())){
				return R.error("当前用户不能删除");
			}

			sysUserService.deleteBatch(userIds);
		}catch (IllegalArgumentException e){
			logger.error(Utils.getStackTrace(e));
			return R.error(e.getMessage());
		}

		
		return R.ok();
	}

	/**
	 * 更新用户的角色信息（先将关联表中的数据全部删除，再进行插入操作）
	 * @param userId
	 * @param roleIds
	 * @return
	 */
	@SysLog("更新用户的角色信息")
	@RequestMapping(value = "/updateUserRole/{userId}",method = RequestMethod.PUT)
	public R updateUserRole(@PathVariable("userId") String userId,@RequestBody Long[] roleIds) {
		System.out.println("用户ID：" + userId);
		System.out.println(Arrays.toString(roleIds));
		//在service实现类处理
		sysUserRoleService.saveUserRole(userId,roleIds);

		return R.ok();
	}
}
