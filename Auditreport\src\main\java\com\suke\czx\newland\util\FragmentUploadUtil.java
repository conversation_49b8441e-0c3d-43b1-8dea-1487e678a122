package com.suke.czx.newland.util;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static com.suke.czx.newland.service.impl.FileServiceImpl.generateFileName;

public class FragmentUploadUtil {
    /**
     * 内部类记录分块上传文件信息
     */
    private static class Value {
        String name;
        boolean[] status;

        Value(int n) {
            this.name = generateFileName();
            this.status = new boolean[n];
        }
    }

    private static final Map<String, Value> chunkMap = new HashMap<>();

    /**
     * 判断文件所有分块是否已上传
     */
    public static boolean isUploaded(String key) {
        if (isExist(key)) {
            for (boolean b : chunkMap.get(key).status) {
                if (!b) {
                    return false;
                }
            }
            return true;
        }
        return false;
    }

    /**
     * 判断文件是否有分块已上传
     */
    private static boolean isExist(String key) {
        return chunkMap.containsKey(key);
    }

    /**
     * 为文件添加上传分块记录
     */
    public static void addChunk(String key, int chunk) {
        chunkMap.get(key).status[chunk] = true;
    }

    /**
     * 从map中删除键为key的键值对
     */
    public static void removeKey(String key) {
        if (isExist(key)) {
            chunkMap.remove(key);
        }
    }

    /**
     * 获取随机生成的文件名
     */
    public static String getFileName(String key, int chunks) {
        if (!isExist(key)) {
            synchronized (FragmentUploadUtil.class) {
                if (!isExist(key)) {
                    chunkMap.put(key, new Value(chunks));
                }
            }
        }
        return chunkMap.get(key).name;
    }

    public static String getFileSizeStr(String filePath) {
        File file = new File(filePath);
        if (file.exists() && file.isFile()) {
            long fileLength = file.length();
            double fileSizeKb = fileLength / 1024.0;
            if (fileSizeKb > 1024) {
                Double fileSizeMB = fileSizeKb / 1024.0;
                return String.format("%.2f MB",fileSizeMB);
            } else {
                return String.format("%.2f KB",fileSizeKb);
            }
        } else {
            return "";
        }
    }
}
