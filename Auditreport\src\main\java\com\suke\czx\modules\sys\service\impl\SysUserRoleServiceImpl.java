package com.suke.czx.modules.sys.service.impl;

import com.suke.czx.modules.sys.dao.SysUserRoleDao;
import com.suke.czx.modules.sys.service.SysUserRoleService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 用户与角色对应关系
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:45:48
 */
@Service("sysUserRoleService")
public class SysUserRoleServiceImpl implements SysUserRoleService {
	@Autowired
	private SysUserRoleDao sysUserRoleDao;

	@Override
	public void saveOrUpdate(Long userId, List<Long> roleIdList) {
		if(roleIdList.size() == 0){
			return ;
		}
		
		//先删除用户与角色关系
		sysUserRoleDao.delete(userId);
		
		//保存用户与角色关系
		Map<String, Object> map = new HashMap<>();
		map.put("userId", userId);
		map.put("roleIdList", roleIdList);
		sysUserRoleDao.save(map);
	}

	@Override
	public List<Long> queryRoleIdList(String userId) {
		return sysUserRoleDao.queryRoleIdList(userId);
	}

	@Override
	public void delete(String userId) {
		sysUserRoleDao.delete(userId);
	}

	/**
	 * 根据角色id查询用户角色关联表，查询是否存在数据，若存在则不允许删除
	 * @param id
	 * @return
	 */
	@Override
	public Integer queryRoleById(Long id) {

		return sysUserRoleDao.queryTotal(id);
	}

	/**
	 * 保存用户的角色信息
	 * @param userId
	 * @param roleIds
	 */
	@Override
	@Transactional
	public void saveUserRole(String userId, Long[] roleIds) {
		//先根据userId在关联表中查询是否存在关联数据，如果存在，先进行删除，再插入
		List<Long> roleIdList = sysUserRoleDao.queryRoleIdList(userId);

		if (roleIdList.size() > 0) {
			//存在关联数据，先删除
			sysUserRoleDao.delete(userId);
		}
		//在关联表中插入数据之前先判断传递的数组是否不为空，不为空再进行插入
		if (roleIds.length != 0) {
			for (Long item : roleIds) {
				sysUserRoleDao.saveUserRole(userId,item);
			}
		}
	}
}
