package com.suke.czx.newland.controller;

import com.alibaba.druid.util.Utils;
import com.github.pagehelper.PageInfo;
import com.suke.czx.modules.audit.controller.AbstractController;
import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.consts.UploadFileType;
import com.suke.czx.newland.dto.*;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;
import com.suke.czx.newland.po.AuditFileDatasourceRulePo;
import com.suke.czx.newland.po.AuditUploadFilePo;
import com.suke.czx.newland.service.AuditFileDatasourceLogService;
import com.suke.czx.newland.service.FileAuditWithDatasourceService;
import com.suke.czx.newland.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.PathParam;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/auditFileForDatasource")
public class AuditFileForDatasourceController extends AbstractController {
    private final FileService fileService;

    private final FileAuditWithDatasourceService auditWithDatasourceService;

    private final AuditFileDatasourceLogService auditFileDatasourceLogService;

    public AuditFileForDatasourceController(FileService fileService, FileAuditWithDatasourceService auditWithDatasourceService, AuditFileDatasourceLogService auditFileDatasourceLogService) {
        this.fileService = fileService;
        this.auditWithDatasourceService = auditWithDatasourceService;
        this.auditFileDatasourceLogService = auditFileDatasourceLogService;
    }

    @PostMapping("/uploadAuditFile")
    public Result uploadAuditFile(String name,
                                  String md5,
                                  Long size,
                                  Integer chunks,
                                  Integer chunk,
                                  MultipartFile file) {
        try {
            log.info("chunks:{},chunk{},size:{}", chunks, chunk, size);
            if (chunks != null && chunks != 0) {
                fileService.uploadWithBlock(name, md5, size, chunks, chunk, file, UploadFileType.NOT_AUDIT_FILE);
            } else {
                fileService.upload(name, md5, file, UploadFileType.NOT_AUDIT_FILE);
            }
        } catch (IOException e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }
        return new Result().setCode(10000).setMsg("文件上传成功.");
    }

    @PostMapping("/listAuditFileWithPageInfo")
    public Result listAuditFileWithPageInfo(@RequestBody AuditUploadQryConditionDto param) {
        PageInfo<AuditUploadFilePo> res = auditWithDatasourceService.qryAuditUploadFileWithPageInfo(param);
        return new Result().setCode(10000).setData(res);
    }

    @PostMapping("/delAuditFileWithId/{auditFileId}")
    public Result delAuditFileWithId(@PathVariable("auditFileId") String auditFileId) {
        try {
            fileService.delFileWithAuditFileId(auditFileId);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("服务器出现异常，请联系管理员查看.");
        }
        return new Result().setCode(10000).setMsg("文件已从服务器移除.");
    }
    @PostMapping("/delBatchAuditFileWithIds")
    public Result delAuditFileWithId(@RequestBody List<String> auditFileIds) {
        try {
            fileService.delBatchFileWithAuditFileIds(auditFileIds);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("服务器出现异常，请联系管理员查看.");
        }
        return new Result().setCode(10000).setMsg("文件已从服务器移除.");
    }

    @PostMapping("/addAuditFileDatasourceRule")
    public Result addAuditFileDatasourceRule(@RequestBody AuditFileDatasourceRuleDto param) {
        try {
            auditWithDatasourceService.addAuditDatasourceRule(constructAuditFileDatasourceRulePo(param));
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("添加文件数据源稽核规则失败");
        }
        return new Result().setCode(10000).setMsg("添加文件数据源稽核规则成功.");
    }

    @PostMapping("/editAuditFileDatasourceRule")
    public Result editAuditFileDatasourceRule(@RequestBody AuditFileDatasourceRuleDto param) {
        try {
            auditWithDatasourceService.editAuditDatasourceRule(constructAuditFileDatasourceRulePo(param));
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("更新文件数据源稽核规则失败");
        }
        return new Result().setCode(10000).setMsg("更新文件数据源稽核规则成功.");
    }

    @PostMapping("/qryAuditFileDatasourceRuleWithPageInfo")
    public Result qryAuditFileDatasourceRuleWithPageInfo(@RequestBody AuditFileDatasourceRuleQryDto param) {
        PageInfo<AuditFileDatasourceRulePo> res;
        try {
            res = auditWithDatasourceService.qryAuditDatasourceRuleWithPageInfo(param);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("查询文件数据源稽核规则失败");
        }
        return new Result().setCode(10000).setData(res);
    }

    @PostMapping("/delAuditFileDatasourceRule")
    public Result delAuditFileDatasourceRule(@RequestBody AuditFileDatasourceRuleDelDto param) {
        try {
            auditWithDatasourceService.delAuditDatasourceRule(param.getIds());
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("删除文件数据源稽核规则失败");
        }
        return new Result().setCode(10000).setMsg("删除文件数据源稽核规则成功");
    }

    @PostMapping("/doAuditFileDatasource/{ruleId}/{fileId}")
    public Result doAuditFileDatasource(@PathVariable String ruleId, @PathVariable String fileId) {
        try {
            auditWithDatasourceService.doDatasourceFileAudit(fileId, ruleId);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            if (e.getMessage() != null) {
                return new Result().setCode(10001).setMsg(e.getMessage());
            } else {
                return new Result().setCode(10001).setMsg("文件数据源稽核失败");
            }
        }
        return new Result().setCode(10000);
    }

    @PostMapping("/qryAuditFileDatasourceLogWithPageInfo")
    public Result qryAuditFileDatasourceLogWithPageInfo(@RequestBody AuditFileDatasourceLogQryDto param) {
        PageInfo<AuditFileDatasourceLogResDto> res;
        try {
            res = auditFileDatasourceLogService.qryAuditDatasourceLogWithPageInfo(param);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("查询文件数据源稽核规则结果失败");
        }
        return new Result().setCode(10000).setData(res);
    }

    @PostMapping("/downloadAuditFileDatasourceRes")
    public void downloadAuditFileDatasourceRes(@PathParam("fileId") String fileId, HttpServletResponse response) {
        auditWithDatasourceService.downloadAuditFileDatasourceLog(fileId, response);
    }


    @PostMapping("/delAuditFileDatasourceLogByIds")
    public Result delAuditFileDatasourceLogByIds(@RequestBody List<String> ids) {
        try {
            auditFileDatasourceLogService.delBatchAuditFileDatasourceLog(ids);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            return new Result().setCode(10001).setMsg("查询文件数据源稽核规则结果失败");
        }
        return new Result().setCode(10000);
    }

    private AuditFileDatasourceRulePo constructAuditFileDatasourceRulePo(AuditFileDatasourceRuleDto param) {
        AuditFileDatasourceRulePo po = new AuditFileDatasourceRulePo();
        po.setCreateBy(getUserId());
        po.setRuleName(param.getAuditFileRuleName());
        po.setAuditFields(String.join(",", param.getAuditFileHeaders()));
        po.setAuditFieldsIndex(String.join(",", param.getAuditFileHeadersIndex()));
        po.setRunDatasourceId(param.getRunDatasourceId());
        po.setAuditSqlClob(param.getAuditFileSqlText());
        po.setFileType(param.getFileType());
        if (param.getId() != null) {
            po.setId(param.getId());
        }
        return po;
    }
}
