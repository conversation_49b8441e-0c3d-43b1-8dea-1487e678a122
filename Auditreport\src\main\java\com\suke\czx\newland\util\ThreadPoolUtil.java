package com.suke.czx.newland.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 文件名称：ThreadPoolUtil
 * 描    述：
 * 作    者：jianggy
 * 创建日期：2020-07-14
 * 创建时间：11:27
 * 当前版本：1.0
 */
public class  ThreadPoolUtil
{
    private Logger logger = LoggerFactory.getLogger(ThreadPoolUtil.class);
    public static Map<String,List<Thread>> execThreads = new ConcurrentHashMap<>();

    private ThreadPoolExecutor threadPool;
    private ThreadPoolExecutor checkPool;
    private ThreadPoolExecutor processPool;
    private static ThreadPoolUtil util = new ThreadPoolUtil();

    private ThreadPoolUtil()
    {
    }

    /**
     * 获取唯一可用的对象
     */
    public static ThreadPoolUtil getInstance()
    {
        return util;
    }

    public void init(int corePoolSize, int maximumPoolSize, int keepAliveTime)
    {
        if (threadPool == null)
        {
            this.threadPool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS, new SynchronousQueue<>());
        }

        if (checkPool == null)
        {
            this.checkPool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS, new SynchronousQueue<>());
        }

        if (processPool == null)
        {
            this.processPool = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS, new SynchronousQueue<>());
        }
    }

    public ThreadPoolExecutor getThreadPool() throws Exception
    {
        if (threadPool == null)
        {
            throw new Exception("流程线程池尚未初始化！");
        }
        return threadPool;
    }

    public ThreadPoolExecutor getCheckPool() throws Exception
    {
        if (checkPool == null)
        {
            throw new Exception("检查线程池尚未初始化！");
        }
        return checkPool;
    }
    public ThreadPoolExecutor getProcessPool() throws Exception{
        if (processPool == null)
        {
            throw new Exception("任务线程池尚未初始化！");
        }
        return processPool;
    }
}
