package com.suke.czx.modules.audit.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 报表比对结果
 */
@Data
public class AuditComparison {

    /**
     * id
     */
    private Long id;

    /**
     * 比对结果名称（报表名称）
     */
    private String name;

    /**
     * 比对次数
     */
    private Long comparisonTimes;

    /**
     * 比对状态 0：未比对，1：比对中，2：比对完成，3：出现异常，4：比对异常
     */
    private Integer status;

    /**
     * 比对操作人id
     */
    private String comparisonUserId;

    /**
     * 比对耗时
     */
    private Long spendTime;

    /**
     * 下载地址
     */
    private String url;

    /**
     * 是否删除，默认0未删除
     */
    private Integer deleted;

    /**
     * 创建时间（比对开始时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 比对结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime endTime;
}
