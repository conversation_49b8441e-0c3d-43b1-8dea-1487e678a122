package com.suke.czx.modules.rule.dao;

import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AuditRuleConfigDao extends BaseDao<AuditRuleConfig>{

    /**
     * 将规则信息保存到子表中
     * @param item
     * @return
     */
    void saveRuleConfig(AuditRuleConfig item);

    /**
     * 根据规则父表的id查询规则子表
     * @param faId
     * @param type
     * @return
     */
    List<AuditRuleConfig> queryRuleConfigList(@Param("faId") String faId, @Param("type") String type);

    /**
     * 根据faId删除子表中的规则
     * @param id
     * @return
     */
    Integer deleteByFaId(String id);

    /**
     * 根据Id查询子表中的规则
     * @param faId
     * @return
     */
    List<AuditRuleConfig> getAuditRuleById(@Param("faId") String faId);

    void delRuleConfigByIds(@Param("ids") String[] ids);
}
