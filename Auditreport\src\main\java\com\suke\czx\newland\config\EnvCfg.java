package com.suke.czx.newland.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

public class EnvCfg {

    public static String env;
    public static String log;

    public static String username;
    public static String password;
    public static String url;

    @Value("${profiles}")
    public void setEnv(String env){
        EnvCfg.env = env;
    }

    @Value("${spring.datasource.druid.mgrdb.username}")
    public void setUsername(String username) {
        EnvCfg.username = username;
    }

    @Value("${spring.datasource.druid.mgrdb.password}")
    public void setPassword(String password) {
        EnvCfg.password = password;
    }
    @Value("${spring.datasource.druid.mgrdb.url}")
    public void setUrl(String url) {
        EnvCfg.url = url;
    }
    @Value("${log.path}")
    public void setLog(String log) {
        EnvCfg.log = log;
    }
}
