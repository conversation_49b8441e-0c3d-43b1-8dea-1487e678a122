package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.AuditFileDatasourceRuleQryDto;
import com.suke.czx.newland.po.AuditFileDatasourceRulePo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AuditFileDatasourceRuleMapper {

    int addBatchAuditFileDatasourceRule(List<AuditFileDatasourceRulePo> records);

    int updateAuditFileDatasourceRule(AuditFileDatasourceRulePo record);

    int deleteAuditFileDatasourceRule(List<String> ids);

    List<AuditFileDatasourceRulePo> qryAuditFileDatasourceRuleByCondition(AuditFileDatasourceRuleQryDto condition);

    AuditFileDatasourceRulePo qryAuditFileDatasourceRuleById(String id);
}
