package com.suke.czx.modules.AI.service;

import com.github.pagehelper.PageInfo;
import com.suke.czx.modules.AI.entity.AIFileVo;
import com.suke.czx.modules.AI.entity.AuditAiFileQryCondition;
import com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AIAuditService {
    PageInfo<AIFileVo> qryAuditAiFile(AuditAiFileQryCondition condition);

    void generateAIAuditFile(GenerateAIFileConditionVo condition, HttpServletRequest request, String userId);

    void updateAuditFileDeleted(List<String> ids);

    void downloadAuditedFile(String sourceFileId, HttpServletResponse response);

    void auditFileByFileId(String fileId,String modelName);
}
