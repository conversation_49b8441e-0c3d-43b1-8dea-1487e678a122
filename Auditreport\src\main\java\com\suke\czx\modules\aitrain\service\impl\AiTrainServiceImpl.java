package com.suke.czx.modules.aitrain.service.impl;

import cn.hutool.extra.ssh.JschUtil;
import com.alibaba.fastjson.JSONObject;
import com.suke.czx.common.exception.RRException;
import com.suke.czx.common.utils.R;
import com.suke.czx.config.FtpProperties;
import com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo;
import com.suke.czx.modules.aitrain.async.ModelTrainAsync;
import com.suke.czx.modules.aitrain.entity.GenerateFileFromSqlVO;
import com.suke.czx.modules.aitrain.entity.ModelTrainInfo;
import com.suke.czx.modules.aitrain.entity.ModelTrainParam;
import com.suke.czx.modules.aitrain.entity.ParseVO;
import com.suke.czx.modules.aitrain.service.AiTrainService;
import com.suke.czx.modules.aitrain.utils.DateUtils;
import com.suke.czx.modules.aitrain.utils.ExcelHandleUtil;
import com.suke.czx.modules.audit.util.Ftp;
import com.suke.czx.newland.util.DataSourceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class AiTrainServiceImpl implements AiTrainService {

    @Resource
    private DataSourceUtil dataSourceUtil;

    @Resource
    private ExcelHandleUtil excelHandleUtil;

    private final FtpProperties ftpProperties;

    @Lazy
    @Resource
    private ModelTrainAsync modelTrainAsync;

    public AiTrainServiceImpl(FtpProperties ftpProperties) {
        this.ftpProperties = ftpProperties;
    }

    /**
     * 通过查询sql生成xlsx文件
     *
     * @param condition
     * @param request
     */
    @Override
    public ModelTrainInfo generateTrainDataFile(GenerateFileFromSqlVO condition, HttpServletResponse response, HttpServletRequest request) {
        try {
            List<JSONObject> queryResultList = dataSourceUtil.executeSqlReturnAll(condition.getRunDatasourceId(), condition.getSqlText(), true);
            // 找出所有字段
            Set<String> allFields = new HashSet<>();
            for (JSONObject jsonObject : queryResultList) {
                allFields.addAll(jsonObject.keySet());
            }
            // 确保所有字段都包含在每个 JSON 对象中
            for (JSONObject jsonObject : queryResultList) {
                for (String field : allFields) {
                    if (!jsonObject.containsKey(field)) {
                        jsonObject.put(field, "");
                    }
                }
            }
            for (JSONObject item : queryResultList) {
//                log.info("查询到的数据集：{}",item);
            }
            Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            String fileName = "database-" + DateUtils.getCurrentTimestampLegacy() + ".xlsx";
            return excelHandleUtil.coverToExcel(queryResultList, ftp, fileName, condition.getFileType().equals("0") ? ftpProperties.getMtNormalPath() : ftpProperties.getMtAbnormalPath(), response);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传训练数据（包含正常和异常训练数据）
     *
     * @param file
     * @param fileType 0：代表正常，1：代表异常
     * @return
     */
    @Override
    public R uploadModelTrainFile(MultipartFile file, String fileType) {
        Workbook workbook = null;
        try {
            //对文件进行校验，包含文件大小以及文件格式（xls或xlsx）大于50MB的不允许
            if (file.getSize() == 0 || file.getSize() > 50 * 1024 * 1024)  {
                throw new RuntimeException("文件大小超过限制，最大支持50MB");
            }
            String fileName = file.getOriginalFilename();
            String fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
            List<String> allowedExtensions = Arrays.asList("xls", "xlsx");
            if (!allowedExtensions.contains(fileExtension)) {
                throw new RuntimeException("不支持的文件类型，只支持.xls和.xlsx文件");
            }
            //解析文件中的每行数据，获取文件名、字段map、总行数、总列数、存在50%以上字段为空的行数、字符串类型的单元格个数、数字类型的单元格个数；构建ModelTrainInfo返回前端
            // 根据文件类型选择Workbook的实现
//            InputStream inputStream = file.getInputStream();
//            if (fileExtension.equalsIgnoreCase("xlsx")) {
//                workbook = new XSSFWorkbook(inputStream);
//            } else if (fileExtension.equalsIgnoreCase("xls")) {
//                workbook = new HSSFWorkbook(inputStream);
//            } else {
//                throw new IllegalArgumentException("不支持的文件类型");
//            }
            //总行数
//            int totalRows = 0;
//            //总列数
//            int totalCols = 0;
//            //字符串类型个数
//            int stringCells = 0;
//            //数字类型个数
//            int numericCells = 0;
//            //空单元格个数
//            int blankCells = 0;
//            //空单元格比例大于50%的行数
//            int blankCellsRowNum = 0;
//            int fieldNullCount = 0;
//
//            //字段map
//            List<String> headList = new ArrayList<>();
//            //第一行包含了所有字段
//            Sheet workbookSheet = workbook.getSheetAt(0);
//            Row firstRow = workbookSheet.getRow(0);
//            if (firstRow != null) {
//                totalCols = firstRow.getLastCellNum();
//                for (int i = 0; i < totalCols; i++) {
//                    Cell cell = firstRow.getCell(i);
//                    if (cell != null) {
//                        headList.add(cell.getStringCellValue());
//                    }
//                }
////                log.info("文件：{}，总列数：{}", fileName,totalCols);
//            }
//            int tmp = 0;
//            for (Sheet sheet : workbook) {
//                totalRows = Math.max(totalRows, sheet.getLastRowNum() + 1);
//                for (Row row : sheet) {
//                    //第一行跳过
//                    if (tmp == 0) {
//                        tmp++;
//                        continue;
//                    }
//                    if (row != null) {
//                        for (Cell cell : row) {
//                            if (cell != null) {
//                                DataFormatter formatter = new DataFormatter();
//                                String cellValue = formatter.formatCellValue(cell);
////                                log.info("文件：{}，单元格值：{}", fileName, cellValue);
//                                if (cellValue == null || cellValue.isEmpty()) {
//                                    blankCells++;
//                                    fieldNullCount++;
//                                    continue;
//                                }
//                                //判断单元格值的类型
//                                if (cellValue.matches("\\d+") || cellValue.matches("-?\\d+(\\.\\d+)?")) {
//                                    numericCells++;
////                                    log.info("获取到的数字类型的值：{}",cellValue);
//                                }else {
//                                    stringCells++;
////                                    log.info("获取到的字符串类型的值：{}",cellValue);
//                                }
//                            } else {
//                                // 如果cell为null，也可以算作空单元格的一种
//                                blankCells++;
////                                log.info("文件：{}，空单元格数：{}", fileName, blankCells);
//                            }
//                        }
//                        double tmpRes = (double)fieldNullCount / headList.size();
////                        log.info("50%空字段比例：{}",tmpRes);
//                        if (tmpRes >= 0.5) {
//                            //说明此行的空字段比例大于等于50%
//                            blankCellsRowNum++;
//                        }
//                        fieldNullCount = 0;
//                    }
//                }
//            }
            ModelTrainInfo modelTrainInfo = new ModelTrainInfo();
            // 在这里，你可以根据需要对totalRows, totalCols, stringCells, numericCells, blankCells进行处理
            // 例如，打印它们或者将它们作为方法的一部分返回
//            log.info("总行数: {}",totalRows);
//            log.info("总列数（基于第一个非空行）:{}",totalCols);
//            log.info("字符串类型单元格数: {}",stringCells);
//            log.info("数字类型单元格数: {}",numericCells);
//            log.info("空单元格数: {}",blankCells);
//            modelTrainInfo.setFileName(fileName);
//            modelTrainInfo.setTotalRowNum(totalRows);
//            modelTrainInfo.setTotalColumnNum(totalCols);
//            modelTrainInfo.setStringTypeNum(stringCells);
//            modelTrainInfo.setNumberTypeNum(numericCells);
//            modelTrainInfo.setNullCellsOfRowNum(blankCellsRowNum);
//            Map<String,Integer> headMap = new HashMap<>();
//            for (int i = 0; i < headList.size(); i++) {
//                headMap.put(headList.get(i),i);
//            }
//            modelTrainInfo.setFileField(headMap);
            //信息解析完成后，上传文件到对应的目录中
            Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            ftp.sshSftp(file.getBytes(),fileName,fileType.equals("0") ? ftpProperties.getMtNormalPath() : ftpProperties.getMtAbnormalPath());
            return R.ok();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 执行模型训练
     *
     * @param modelTrainParam
     * @return
     */
    @Override
    public R modelTrain(ModelTrainParam modelTrainParam) {
        Map<String,Object> resultMap = new HashMap<>();
        //最终的模型名称：AutogluonModels/ag-[时间戳]
        String modelName = "ag-" + DateUtils.getCurrentTimestampLegacy();
        if (!modelTrainParam.getModelName().isEmpty()) {
            modelName = modelName + "-" + modelTrainParam.getModelName();
        }
        //正常和异常训练文件最后调用python脚本的两个路径入参
        String finalNormalFilePath = "";
        String finalAbnormalFilePath = "";
        //先判断正常和异常文件中字段是否全选，如果全选，直接使用原文件，否则需要重新生成文件
        if (modelTrainParam.getNormalOriginFieldIndexList().size() == modelTrainParam.getNormalCheckFieldIndexList().size()) {
            finalNormalFilePath = ftpProperties.getMtNormalPath() + (ftpProperties.getMtNormalPath().endsWith("/") ? "" : "/") + modelTrainParam.getNormalFileName();
            log.info("正常文件字段默认全选，训练数据文件路径：{}", finalNormalFilePath);
        }else {
            //需要新生成正常训练文件
            log.info("正常文件字段未全选，字段下标：{}", modelTrainParam.getNormalCheckFieldIndexList());
            //新生成正常的文件名称
            String fileName = "final-" + modelTrainParam.getNormalFileName();
            finalNormalFilePath = ftpProperties.getFinalTrainPath() + (ftpProperties.getFinalTrainPath().endsWith("/") ? "" : "/") + fileName;
            log.info("新生成的正常训练文件路径：{}", finalNormalFilePath);
            try {
                //先获取到原始文件
                Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
                //正常文件
                Workbook workbook = ftp.getWorkbook(ftpProperties.getMtNormalPath() + (ftpProperties.getMtNormalPath().endsWith("/") ? "" : "/") + modelTrainParam.getNormalFileName());
                if (workbook == null) {
                    log.error("获取到的远程正常训练原文件为空");
                    throw new RRException("获取到的远程正常训练原文件为空");
                }
                //差异的字段下标
                List<Integer> diffList = compareDiffList(modelTrainParam.getNormalOriginFieldIndexList(),modelTrainParam.getNormalCheckFieldIndexList());
                log.info("正常训练文件差异字段下标：{}",diffList);
                int[] checkColIndex = diffList.stream().mapToInt(Integer::intValue).toArray();
                // 对要删除的列索引进行排序，并逆序，以便从后往前删除
                for (int i = 0; i < checkColIndex.length; i++) {
                    for (int j = checkColIndex.length - 1; j > i; j--) {
                        int temp = checkColIndex[i];
                        if (checkColIndex[i] < checkColIndex[j]) {
                            checkColIndex[i] = checkColIndex[j];
                            checkColIndex[j] = temp;
                        }
                    }
                }
                log.info("正常文件逆序后的列索引下标：{}", Arrays.toString(checkColIndex));
                Sheet sheet = workbook.getSheetAt(0);
                //根据列索引下标删除对应列
                deleteCells(sheet,checkColIndex);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                workbook.write(outputStream);
                ftp.sshSftp(outputStream.toByteArray(), fileName,ftpProperties.getFinalTrainPath());
                log.info("新生成正常训练文件上传完成: {}，文件路径：{}", fileName,finalNormalFilePath);
            } catch (Exception e) {
                log.error("生成正常训练文件失败",e);
                throw new RRException("生成正常训练文件失败");
            }
        }
        //判断异常文件是否需要生成新文件
        if (modelTrainParam.getAbnormalOriginFieldIndexList().size() == modelTrainParam.getAbnormalCheckFieldIndexList().size()) {
            finalAbnormalFilePath = ftpProperties.getMtAbnormalPath() + (ftpProperties.getMtAbnormalPath().endsWith("/") ? "" : "/") + modelTrainParam.getAbnormalFileName();
            log.info("异常文件字段默认全选，训练数据文件路径：{}", finalAbnormalFilePath);
        }else {
            //需要新生成异常训练文件
            log.info("异常文件字段未全选，字段下标：{}", modelTrainParam.getAbnormalCheckFieldIndexList());
            String fileName = "final-" + modelTrainParam.getAbnormalFileName();
            finalAbnormalFilePath = ftpProperties.getFinalTrainPath() + (ftpProperties.getFinalTrainPath().endsWith("/") ? "" : "/") + fileName;
            log.info("新生成的异常训练文件路径：{}", finalAbnormalFilePath);
            try {
                //先获取到原始文件
                Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
                //正常文件
                Workbook workbook = ftp.getWorkbook(ftpProperties.getMtAbnormalPath() + (ftpProperties.getMtAbnormalPath().endsWith("/") ? "" : "/") + modelTrainParam.getAbnormalFileName());
                if (workbook == null) {
                    log.error("获取到的远程异常训练原文件为空");
                    throw new RRException("获取到的远程异常训练原文件为空");
                }
                //差异的字段下标
                List<Integer> diffList = compareDiffList(modelTrainParam.getAbnormalOriginFieldIndexList(),modelTrainParam.getAbnormalCheckFieldIndexList());
                log.info("异常训练文件差异字段下标：{}",diffList);
                int[] checkColIndex = diffList.stream().mapToInt(Integer::intValue).toArray();
                // 对要删除的列索引进行排序，并逆序，以便从后往前删除
                for (int i = 0; i < checkColIndex.length; i++) {
                    for (int j = checkColIndex.length - 1; j > i; j--) {
                        int temp = checkColIndex[i];
                        if (checkColIndex[i] < checkColIndex[j]) {
                            checkColIndex[i] = checkColIndex[j];
                            checkColIndex[j] = temp;
                        }
                    }
                }
                log.info("异常文件逆序后的列索引下标：{}", Arrays.toString(checkColIndex));
                Sheet sheet = workbook.getSheetAt(0);
                //根据列索引下标删除对应列
                deleteCells(sheet,checkColIndex);
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                workbook.write(outputStream);
                ftp.sshSftp(outputStream.toByteArray(), fileName,ftpProperties.getFinalTrainPath());
                log.info("新生成异常训练文件上传完成: {}，文件路径：{}", fileName,finalAbnormalFilePath);
            } catch (Exception e) {
                log.error("生成异常训练文件失败",e);
                throw new RRException("生成异常训练文件失败");
            }
        }
        resultMap.put("modelName",modelName);
        resultMap.put("normalPath",finalNormalFilePath);
        resultMap.put("abnormalPath",finalAbnormalFilePath);
        //调用异步模型训练方法（先调用模型训练方法，再调用解析csv方法，再入库存储）
        log.info("开始异步AI训练========================");
        modelTrainAsync.trainModel(modelTrainParam,modelName, finalNormalFilePath,finalAbnormalFilePath,ftpProperties);
        return R.ok(resultMap);
    }

    /**
     * 获取远程中的正常训练数据文件名
     *
     * @return
     */
    @Override
    public Map<String,List<String>> getRemoteNormalFileName() {
        try {
            Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            return ftp.getRemoteFileName(ftpProperties.getMtNormalPath(), ftpProperties.getMtAbnormalPath());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析模型训练文件（字段、行数、列数等）
     *
     * @param parseVO
     */
    @Override
    public Map<String,Object> parseModelTrainFile(ParseVO parseVO) {
        //先获取正常和异常的文件名称，验证是否存在
        String normalFileName = parseVO.getNormalFileName();
        String abnormalFileName = parseVO.getAbnormalFileName();
        if (normalFileName.isEmpty() || abnormalFileName.isEmpty()) {
            throw new RRException("文件名称不能为空");
        }
        //最终返回的解析结果
        Map<String,Object> parseMap = new HashMap<>();
        try {
            Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            //获取正常文件
            Workbook normalWorkbook = ftp.getWorkbook(ftpProperties.getMtNormalPath() + (ftpProperties.getMtNormalPath().endsWith("/") ? "" : "/") + normalFileName);
            if (normalWorkbook == null) {
                log.error("正常训练文件不存在");
                throw new RRException("正常训练文件不存在");
            }
            ModelTrainInfo normalModelTrainInfo = parseExcelFile(normalWorkbook, normalFileName);
            //获取异常文件
            Workbook abnormalWorkbook = ftp.getWorkbook(ftpProperties.getMtAbnormalPath() + (ftpProperties.getMtAbnormalPath().endsWith("/") ? "" : "/") + abnormalFileName);
            if (abnormalWorkbook == null) {
                log.error("异常训练文件不存在");
                throw new RRException("异常训练文件不存在");
            }
            ModelTrainInfo abnormalModelTrainInfo = parseExcelFile(abnormalWorkbook, abnormalFileName);
            parseMap.put("normal",normalModelTrainInfo);
            parseMap.put("abnormal",abnormalModelTrainInfo);
            return parseMap;
        } catch (Exception e) {
            log.error("解析训练数据文件异常",e);
            throw new RRException("解析训练数据文件异常");
        }
    }

    /**
     * 根据workbook解析excel文件方法
     * @param workbook
     * @param fileName
     * @return
     */
    private ModelTrainInfo parseExcelFile(Workbook workbook,String fileName) {
        //总行数
        int totalRows = 0;
        //总列数
        int totalCols = 0;
        //字符串类型个数
        int stringCells = 0;
        //数字类型个数
        int numericCells = 0;
        //空单元格个数
        int blankCells = 0;
        //空单元格比例大于50%的行数
        int blankCellsRowNum = 0;
        int fieldNullCount = 0;

        //字段map
        List<String> headList = new ArrayList<>();
        //第一行包含了所有字段
        Sheet workbookSheet = workbook.getSheetAt(0);
        Row firstRow = workbookSheet.getRow(0);
        if (firstRow != null) {
            totalCols = firstRow.getLastCellNum();
            for (int i = 0; i < totalCols; i++) {
                Cell cell = firstRow.getCell(i);
                if (cell != null) {
                    headList.add(cell.getStringCellValue());
                }
            }
        }
        int tmp = 0;
        for (Sheet sheet : workbook) {
            totalRows = Math.max(totalRows, sheet.getLastRowNum() + 1);
            for (Row row : sheet) {
                //第一行跳过
                if (tmp == 0) {
                    tmp++;
                    continue;
                }
                if (row != null) {
                    for (Cell cell : row) {
                        if (cell != null) {
                            DataFormatter formatter = new DataFormatter();
                            String cellValue = formatter.formatCellValue(cell);
                            if (cellValue == null || cellValue.isEmpty()) {
                                blankCells++;
                                fieldNullCount++;
                                continue;
                            }
                            //判断单元格值的类型
                            if (cellValue.matches("\\d+") || cellValue.matches("-?\\d+(\\.\\d+)?")) {
                                numericCells++;
                            }else {
                                stringCells++;
                            }
                        } else {
                            blankCells++;
                        }
                    }
                    double tmpRes = (double)fieldNullCount / headList.size();
                    if (tmpRes >= 0.5) {
                        //说明此行的空字段比例大于等于50%
                        blankCellsRowNum++;
                    }
                    fieldNullCount = 0;
                }
            }
        }
        ModelTrainInfo modelTrainInfo = new ModelTrainInfo();
        // 在这里，你可以根据需要对totalRows, totalCols, stringCells, numericCells, blankCells进行处理
        // 例如，打印它们或者将它们作为方法的一部分返回
        modelTrainInfo.setFileName(fileName);
        modelTrainInfo.setTotalRowNum(totalRows);
        modelTrainInfo.setTotalColumnNum(totalCols);
        modelTrainInfo.setStringTypeNum(stringCells);
        modelTrainInfo.setNumberTypeNum(numericCells);
        modelTrainInfo.setNullCellsOfRowNum(blankCellsRowNum);
        Map<String,Integer> headMap = new HashMap<>();
        for (int i = 0; i < headList.size(); i++) {
            headMap.put(headList.get(i),i);
        }
        modelTrainInfo.setFileField(headMap);
        return modelTrainInfo;
    }

    private void deleteCells(Sheet sheet, int[] deleteIndex) {
        int maxColumn = 0;
        //获取全部列的数量
        int cellNum = sheet.getRow(0).getLastCellNum();
        for (int i = 0; i < deleteIndex.length; i++) {
            //要删除的当前行列的下标
            int columnToDelete = deleteIndex[i];
            //遍历每行对应列的单元格
            for (Row row : sheet) {
                if (row == null) {
                    continue;
                }
                //获取当前行的最后一个列数
                int lastColumn = row.getLastCellNum();
                if (lastColumn > maxColumn) {
                    maxColumn = lastColumn;
                }
                //如果当前行最后一个列数小于要删除的列数，则跳过
                if (lastColumn < columnToDelete) {
                    continue;
                }
                for (int x = columnToDelete + 1; x < lastColumn + 1; x++) {
                    //获取要删除的单元格
                    Cell oldCell = row.getCell(x - 1);
                    if (oldCell != null) {
                        row.removeCell(oldCell);
                    }
                    //右侧的单元格
                    Cell nextCell = row.getCell(x);
                    if (nextCell != null) {
                        Cell newCell = row.createCell(x - 1, nextCell.getCellType());
                        //移动右侧的单元格到左边
                        cloneCell(newCell, nextCell);
                    }
                }
            }
        }
    }

    /**
     * 将右边列的内容移动到左边
     * @param cNew
     * @param cOld
     */
    private void cloneCell(Cell cNew, Cell cOld) {
        cNew.setCellComment(cOld.getCellComment());
        cNew.setCellStyle(cOld.getCellStyle());

        switch (cNew.getCellType()) {
            case BOOLEAN: {
                cNew.setCellValue(cOld.getBooleanCellValue());
                break;
            }
            case NUMERIC: {
                cNew.setCellValue(cOld.getNumericCellValue());
                break;
            }
            case STRING: {
                //这样不丢样式
                cNew.setCellValue(cOld.getRichStringCellValue());
                break;
            }
            case ERROR: {
                cNew.setCellValue(cOld.getErrorCellValue());
                break;
            }
            case FORMULA: {
                cNew.setCellFormula(cOld.getCellFormula());
                break;
            }
            case BLANK:
            case _NONE: {
                cNew.setCellValue("");
                break;
            }
        }

    }


    /**
     * 获取原始集合与选中的集合中的差异的元素
     * @param originList
     * @param checkList
     * @return
     */
    private List<Integer> compareDiffList(List<Integer> originList, List<Integer> checkList) {
        // 将listB转换为HashSet以提高查找效率
        Set<Integer> checkSet = new HashSet<>(checkList);

        // 存储A中存在但B中不存在的元素
        List<Integer> diffList = new ArrayList<>();

        // 遍历listA，检查每个元素是否存在于setB中
        for (Integer item : originList) {
            if (!checkSet.contains(item)) {
                diffList.add(item);
            }
        }
        return diffList;
    }
}
