package com.suke.czx.newland.po;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AuditUploadFilePo {
    private String id;
    private String fileName;
    private String md5;
    private String filePath;
    private String fileSize;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;
    private String uploadUser;
    private String fileType;
}
