package com.suke.czx.modules.rule.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 规则子表
 */
@Data
public class AuditRuleConfig {

    /**
     * id
     */
    private Long id;

    /**
     * 父规则文件id
     */
    private String faId;

    /**
     * 规则名称
     */
    private String faName;

    /**
     * 子规则名称
     */
    private String name;
    /**
     * 规则类型
     */
    private String type;

    /**
     * 行
     */
    private Integer ruleRow;

    /**
     * 列
     */
    private Integer ruleColumn;

    /**
     * 规则参数
     */
    private String parameter;

    /**
     * 规则说明
     */
    private String remarks;

    /**
     * 是否删除，默认0：未删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createName;


    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

    private Integer current;
    private Integer pageSize;

    /*
    设置报表标题行
  */
    private Integer titleRow;

    private Integer titleEndRow;
}
