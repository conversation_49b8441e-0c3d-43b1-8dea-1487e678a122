package com.suke.czx.newland.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.newland.dao.mgrdbmapper.AuditFileDatasourceRuleMapper;
import com.suke.czx.newland.dao.mgrdbmapper.AuditUploadFileMapper;
import com.suke.czx.newland.dto.AuditFileDatasourceLogQryDto;
import com.suke.czx.newland.dto.AuditFileDatasourceRuleQryDto;
import com.suke.czx.newland.dto.AuditUploadQryConditionDto;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;
import com.suke.czx.newland.po.AuditFileDatasourceRulePo;
import com.suke.czx.newland.po.AuditUploadFilePo;
import com.suke.czx.newland.service.AuditFileDatasourceLogService;
import com.suke.czx.newland.service.FileAuditWithDatasourceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class FileAuditWithDatasourceServiceImpl implements FileAuditWithDatasourceService {
    @Autowired
    private AuditUploadFileMapper uploadFileMapper;

    @Autowired
    private AuditFileDatasourceRuleMapper ruleMapper;

    @Autowired
    private AuditFileDatasourceLogService auditFileDatasourceLogService;

    @Override
    public PageInfo<AuditUploadFilePo> qryAuditUploadFileWithPageInfo(AuditUploadQryConditionDto condition) {
        PageHelper.startPage(condition.getCurrent(), condition.getPageSize());
        List<AuditUploadFilePo> res = uploadFileMapper.qryAuditWithCondition(condition);
        return new PageInfo<>(res);
    }

    @Override
    public void addAuditDatasourceRule(AuditFileDatasourceRulePo record) {
        int res = ruleMapper.addBatchAuditFileDatasourceRule(Collections.singletonList(record));
        if (res != 1) {
            throw new RuntimeException("新增规则异常.");
        }
    }

    @Override
    public void editAuditDatasourceRule(AuditFileDatasourceRulePo record) {
        int res = ruleMapper.updateAuditFileDatasourceRule(record);
        if (res != 1) {
            throw new RuntimeException("更新规则异常.");
        }
    }

    @Override
    public PageInfo<AuditFileDatasourceRulePo> qryAuditDatasourceRuleWithPageInfo(AuditFileDatasourceRuleQryDto condition) {
        PageHelper.startPage(condition.getCurrent(), condition.getPageSize());
        List<AuditFileDatasourceRulePo> res = ruleMapper.qryAuditFileDatasourceRuleByCondition(condition);
        return new PageInfo<>(res);
    }

    @Override
    public void delAuditDatasourceRule(List<String> ids) {
        int res = ruleMapper.deleteAuditFileDatasourceRule(ids);
        if (res <= 0) {
            throw new RuntimeException("删除规则异常.");
        }
    }

    @Override
    public void doDatasourceFileAudit(String fileId, String ruleId) {
        Objects.requireNonNull(fileId, "文件信息不能为空.");
        Objects.requireNonNull(ruleId, "规则信息不能为空.");
        AuditFileDatasourceRulePo ruleInfo = ruleMapper.qryAuditFileDatasourceRuleById(ruleId);
        if (ruleInfo == null) {
            throw new IllegalArgumentException("规则信息查询结果不存在.");
        }
        AuditUploadFilePo fileInfo = uploadFileMapper.qryAuditFileWithAuditFileId(fileId);
        if (fileInfo == null) {
            throw new IllegalArgumentException("文件信息查询结果不存在.");
        }
        FileAuditDatasourceListener fileAuditDatasourceListener = new FileAuditDatasourceListener(ruleInfo, fileInfo);
        ExcelReaderBuilder reader = EasyExcel.read(fileInfo.getFilePath(), fileAuditDatasourceListener);
        reader.sheet().doRead();
    }

    @Override
    public void downloadAuditFileDatasourceLog(String fileId, HttpServletResponse response) {
        if (fileId == null || fileId.isEmpty()) {
            log.info("文件ID,不能为空");
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
        AuditUploadFilePo fileInfo = uploadFileMapper.qryAuditFileWithAuditFileIdWithoutStatus(fileId);
        if (fileInfo == null) {
            log.info("文件ID查询文件数据不存在.");
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            return;
        }
        try {
            Path file = Paths.get(fileInfo.getFilePath());
            if (!Files.exists(file)) {
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileInfo.getFileName(), "UTF-8"));
            response.setContentType("application/octet-stream");
            response.setContentLengthLong(Files.size(file));
            InputStream in = Files.newInputStream(file);
            OutputStream out = response.getOutputStream();
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            out.flush();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
