package com.suke.czx.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.MultipartConfigElement;
import java.io.File;

/**
 * <AUTHOR>
 * @date 2024-02-18 018 16:35:45
 * @description 文件上传配置
 */
@Slf4j
@Configuration
public class MultipartConfig {

    @Bean
    public MultipartConfigElement multipartConfigElement() {
        MultipartConfigFactory factory = new MultipartConfigFactory();
        String location = System.getProperty("user.home") + File.separator + "tmp";
        log.info("文件上传临时路径:{}", location);
        File tmpFile = new File(location);
        if (!tmpFile.exists()) {
            boolean mkdirs = tmpFile.mkdirs();
            log.info("创建文件上传临时路径:{}, 创建结果:{}", location, mkdirs);
        }
        factory.setLocation(location);
        return factory.createMultipartConfig();
    }
}
