package com.suke.czx.newland.service;

import com.suke.czx.newland.consts.UploadFileType;
import com.suke.czx.newland.po.AuditUploadFilePo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface FileService {
    void upload(String name,
                String md5,
                MultipartFile file, UploadFileType fileType);

    void uploadWithBlock(String name,
                         String md5,
                         Long size,
                         Integer chunks,
                         Integer chunk,
                         MultipartFile file, UploadFileType fileType) throws IOException;

    void delFileWithAuditFileId(String auditFileId);

    void delBatchFileWithAuditFileIds(List<String> ids);

    String getAuditedFileName(AuditUploadFilePo fileInfo);

    String uploadReturnId(String name,
                String md5,
                MultipartFile file, UploadFileType fileType);
}
