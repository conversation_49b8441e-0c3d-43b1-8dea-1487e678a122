package com.suke.czx.config;

import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "ftp")
@ToString
public class FtpProperties {

    private String host;
    private int port;
    private String user;
    private String password;
    private String basePath;
    private String comPath;

    private String rulePath;
    private String logPath;

    public String getPythonbasePath() {
        return pythonbasePath;
    }

    public void setPythonbasePath(String pythonbasePath) {
        this.pythonbasePath = pythonbasePath;
    }

    private String logName;
    private String aiPath;
    private String pythonbasePath;
    //主机中python的根目录
    private String pythonPath;
    private String containerName;

    /**
     * 正常训练数据保存路径
     */
    private String mtNormalPath;

    /**
     * 异常训练数据保存路径
     */
    private String mtAbnormalPath;

    /**
     * 最终的训练文件路径
     */
    private String finalTrainPath;

    public void setFinalTrainPath(String finalTrainPath) {
        this.finalTrainPath = finalTrainPath;
    }

    public String getFinalTrainPath() {
        return finalTrainPath;
    }

    public void setMtNormalPath(String mtNormalPath) {
        this.mtNormalPath = mtNormalPath;
    }

    public String getMtNormalPath() {
            return mtNormalPath;
    }

    public void setMtAbnormalPath(String mtAbnormalPath) {
            this.mtAbnormalPath = mtAbnormalPath;
    }

    public String getMtAbnormalPath() {
            return mtAbnormalPath;
    }

    public String getPythonPath() {
        return pythonPath;
    }
    public void setPythonPath(String pythonPath) {
        this.pythonPath = pythonPath;
    }
    public String getContainerName() {
        return containerName;
    }

    public void setContainerName(String containerName) {
        this.containerName = containerName;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getBasePath() {
        return basePath;
    }

    public void setBasePath(String basePath) {
        this.basePath = basePath;
    }

    public String getComPath() {
        return comPath;
    }

    public void setComPath(String comPath) {
        this.comPath = comPath;
    }

    public String getRulePath() {
        return rulePath;
    }

    public void setRulePath(String rulePath) {
        this.rulePath = rulePath;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public String getLogName() {
        return logName;
    }

    public void setLogName(String logName) {
        this.logName = logName;
    }
    public String getAiPath() {
        return aiPath;
    }

    public void setAiPath(String aiPath) {
        this.aiPath = aiPath;
    }
}
