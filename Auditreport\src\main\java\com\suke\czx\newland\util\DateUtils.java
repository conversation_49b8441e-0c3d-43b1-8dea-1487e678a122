package com.suke.czx.newland.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 文件名称：DateUtils
 * 描    述：
 * 作    者：jianggy
 * 创建日期：2020-09-10
 * 创建时间：16:57
 * 当前版本：1.0
 */
public class DateUtils
{
    private static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

    private static final String DATABASE_DATE_FORMAT = "yyyyMMddhh24miss";

    /**
     * 指定东8区
     */
    public static String getTime()
    {
        SimpleDateFormat s = new SimpleDateFormat("yyyyMMddHHmmss");
        s.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return s.format(new Date());
    }

//    /**
//     * 获取数据库的系统时间
//     * @return
//     */
//    public static String getDateBaseNowTime () {
//
//        return PrefabSqlService.getTime(DATABASE_DATE_FORMAT);
//
//    }

    /**
     * 判断是否已经超过最大等待时间
     * @param startTime 任务开始执行的时间
     * @param maxWaitTime 该任务最大可执行时间
     */
    @SuppressWarnings("all")
    public static boolean isOutOfMaxWaitTime(long startTime, long maxWaitTime, long firstWaitTime)
    {
        boolean flag = false;
        long currentTime = System.currentTimeMillis();

        // 当前时间 - 开始时间 > 最大等待时间 - 首次等待时间
        if ((currentTime - startTime) > maxWaitTime - firstWaitTime)
        {
            return true;
        }
        return false;
    }

    /**
     * 获取当前天 零时的毫秒数
     */
    public static long getCurrentDayZeroTime() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String format = sdf.format(new Date());
        Date date = null;
        try {
            date = sdf.parse(format);
        } catch (ParseException e) {
            e.printStackTrace();
            logger.error("[getCurrentDayZeroTime 日期格式化异常]");
            throw e;
        }
        return date.getTime();
    }


    public static String getCurrentMonthEndTime(String date){
        Integer year = Integer.valueOf(date.substring(0,4));
        String month = date.substring(4,6);
        Integer[] maxDay = null;

        if(year % 4== 0 && year%100!=0||year%400==0)
        {
            maxDay = new Integer[]{31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        }
        else
        {
            maxDay = new Integer[]{31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
        }

        String endTheMonth = date + maxDay[Integer.parseInt(month) - 1];


        return endTheMonth;
    }

    /**
     * <AUTHOR>
     * @param str
     * @return
     * @throws ParseException
     * TODO 新增
     */
    public static Date getDate(String str) throws ParseException{
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date =  formatter.parse(str);
        return date;
    }

}
