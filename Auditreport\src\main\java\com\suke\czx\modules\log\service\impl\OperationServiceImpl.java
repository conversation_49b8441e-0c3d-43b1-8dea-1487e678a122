package com.suke.czx.modules.log.service.impl;

import com.suke.czx.modules.log.dao.OperationDao;
import com.suke.czx.modules.log.entity.OperationEntity;
import com.suke.czx.modules.log.service.OperationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作日志
 */
@Service
public class OperationServiceImpl implements OperationService {

    @Autowired
    private OperationDao operationDao;
    /**
     * 查询所有日志列表
     * @return
     */
    @Override
    public List<OperationEntity> queryLogList(String userName,String operation) {
        return operationDao.queryLogList(userName,operation);
    }
}
