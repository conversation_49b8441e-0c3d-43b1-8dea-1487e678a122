package com.suke.czx.newland.util;

import com.suke.czx.newland.config.EnvCfg;
import com.suke.czx.newland.vo.CommonVo;
import com.suke.czx.newland.vo.SmFlwResourceVo;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 文件名称：DBUtil
 * 描    述：数据库连接获取工具 连接池：HikariCP
 * 作    者：jianggy
 * 创建日期：2020-06-23
 * 创建时间：20:36
 * 当前版本：1.0
 */
public class DBUtil
{
    public static Logger logger = LoggerFactory.getLogger(DBUtil.class);

    // 以下为开发环境配置
    private static String username;
    private static String password;
    private static String url;

    private static final Map<String, HikariDataSource> dsMap = new ConcurrentHashMap<>();

    /**
     * 获取bg_smart应用核心配置表所在的数据库连接
     */
    public static Connection getBgSmartConn() throws Exception
    {
        if (!dsMap.containsKey("rhkz"))
        {
            if(!"dev".equals(EnvCfg.env))
            {
                // 生产环境配置
               // CNLDBConnectMgr cnldbConnectMgr = new CNLDBConnectMgr();
                username = "rhkz";
               // password = cnldbConnectMgr.getPasswd(2, "nmgrdb1", username, "", "");
                url = "****************************** = (LOAD_BALANCE=OFF) (FAILOVER=ON) (ADDRESS_LIST = " +
                        "(ADDRESS = (PROTOCOL = TCP)(HOST = nmgrdb1.boss.js.cmcc)(PORT = 1521)) (ADDRESS = (PROTOCOL = TCP)" +
                        "(HOST = nmgrdb2.boss.js.cmcc)(PORT = 1521))) (CONNECT_DATA = (SERVICE_NAME = nmgrdb) (SERVER=DEDICATED) " +
                        "(FAILOVER_MODE= (TYPE=SELECT) (METHOD=basic))))";
            } else {
                url = EnvCfg.url;
                username = EnvCfg.username;
                password = EnvCfg.password;
            }
            HikariConfig config = getHikariConfig(url, username, password);
            dsMap.put("rhkz", new HikariDataSource(config));
        }
        return dsMap.get("rhkz").getConnection();
    }

    /**
     * 获取指定的数据库连接
     */
    public synchronized static Connection getConnection(CommonVo commonVo, boolean isCheckTask) throws Exception
    {
        return getConnection(commonVo, isCheckTask, false);
    }

    /**
     * 获取指定的数据库连接
     */
    public synchronized static Connection getConnection(CommonVo commonVo, boolean isCheckTask, boolean userCheckTask) throws Exception
    {
        SmFlwResourceVo vo;
        if (userCheckTask)
        {
            vo = commonVo.getSmFlwChkResourceVo();
        }
        else
        {
            if (!isCheckTask)
            {
                vo = commonVo.getSmFlwResourceVo();
            }
            else
            {
                vo = commonVo.getSmFlwChkResourceVo();
            }
        }

       // CNLDBConnectMgr cnldbConnectMgr = new CNLDBConnectMgr();
        String password = "";
        if(vo.getFix()==1)
        {
            // 使用密码直接连接数据库
            password = vo.getPassword();
        }
        else
        {
            // 生产环境 自动获取密码
          //  password = cnldbConnectMgr.getPasswd(2, vo.getTns(), vo.getLoginAcc(), "", "");
            if(password == null){
                throw new Exception("使用TNS未获取到密码！！！请检查配置！！！");
            }
        }
        return getConnection(vo.getResAddr(), vo.getLoginAcc(),password);
    }

    private synchronized static Connection getConnection(String url, String username, String password) throws Exception
    {
        if (!dsMap.containsKey(username + url))
        {
            HikariConfig config = getHikariConfig(url, username, password);
            dsMap.put(username + url, new HikariDataSource(config));
        }
        return dsMap.get(username + url).getConnection();
    }

    private static HikariConfig getHikariConfig(String url, String username, String password) {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(url);
        config.setUsername(username);
        config.setPassword(password);
        config.setMinimumIdle(20);
        config.setMaximumPoolSize(200);
        config.setMaxLifetime(1800000);
        config.setConnectionTestQuery("select 1 from dual");
        config.setAutoCommit(false); // 手动提交
        config.addDataSourceProperty("cachePrepStmts", "true");        // 是否自定义配置，为true时下面两个参数才生效
        config.addDataSourceProperty("prepStmtCacheSize", "250");      // 连接池大小默认25，官方推荐250-500
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "4096"); // 单条语句最大长度默认256，官方推荐2048
        return config;
    }
}
