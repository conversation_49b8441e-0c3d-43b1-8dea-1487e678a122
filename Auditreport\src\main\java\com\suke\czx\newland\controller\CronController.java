package com.suke.czx.newland.controller;


import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.vo.CronPreviewVo;
import org.quartz.CronExpression;
import org.quartz.CronScheduleBuilder;
import org.quartz.CronTrigger;
import org.quartz.TriggerBuilder;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/cron")
public class CronController {


    @PostMapping(value = "/cronPreview")
    public Result auditImport(@RequestBody CronPreviewVo param) {
        return new Result().setCode(10000).setMsg("获取成功").setData(getNextTriggerTime(param.getCronExpression(), param.getTimes()));
    }

    private List<String> getNextTriggerTime(String cron, int times) {
        if (cron == null || !CronExpression.isValidExpression(cron)) {
            return null;
        }
        // 创建一个SimpleDateFormat对象，指定所需的日期时间格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<String> res = new ArrayList<>();
        CronTrigger trigger = TriggerBuilder
                .newTrigger()
                .withIdentity("Caclulate Date")
                .withSchedule(CronScheduleBuilder.cronSchedule(cron))
                .build();
        Date startDate = trigger.getStartTime();
        for (int i = 0; i < times; i++) {
            Date beginTime = trigger.getFireTimeAfter(startDate);
            res.add(sdf.format(beginTime));
            startDate = beginTime;
        }
        return res;
    }

}
