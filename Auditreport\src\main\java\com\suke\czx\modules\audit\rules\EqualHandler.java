package com.suke.czx.modules.audit.rules;

import com.alibaba.druid.util.Utils;
import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetEqualDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
public class EqualHandler extends ExcelHandleRuler {

    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetEqualDto condition = (SheetEqualDto) param;
        Sheet sheet = condition.getSheet();
        int targetColumn = condition.getColumn();

        List<Integer> columns = condition.getComponentColumn();


        boolean res = true;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);

            Cell oneRowTargetCell = oneRow.getCell(targetColumn);
            setCellStr(oneRowTargetCell);

            BigDecimal oneRowTargetVal = new BigDecimal(oneRowTargetCell.getStringCellValue());


            BigDecimal calculateSumVal = getRowTargetColumnSum(oneRow, columns);

            if (oneRowTargetVal.compareTo(calculateSumVal) != 0) {
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }

        }
        return res ? success() : fail();
    }


    private BigDecimal getRowTargetColumnSum(Row row, List<Integer> columns) {
        Objects.requireNonNull(row);
        BigDecimal sum = BigDecimal.ZERO;
        try {
            for (Integer oneColumn : columns) {
                Cell oneCell = row.getCell(oneColumn);
                setCellStr(oneCell);
                BigDecimal cellVal = new BigDecimal(oneCell.getStringCellValue());
                sum = sum.add(cellVal);
            }
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new IllegalArgumentException("解析单元格求和失败.");
        }
        return sum;
    }
}
