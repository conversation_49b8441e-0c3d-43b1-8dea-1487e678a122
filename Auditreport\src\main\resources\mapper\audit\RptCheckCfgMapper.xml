<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.RptCheckCfgMapper">
    <resultMap id="rptCheckCfgMap" type="com.suke.czx.newland.po.RptCheckCfgPo">
        <id column="RULE_ID" property="ruleId"/>
        <result column="RULE_NAME" property="ruleName"/>
        <result column="STATUS" property="status"/>
        <result column="CREATE_PERSON" property="createPerson"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="MODIFY_PERSON" property="modifyPerson"/>
        <result column="MODIFY_DATE" property="modifyDate"/>
        <result column="CHK_TYPE_ID" property="chkTypeId"/>
        <result column="CENTER_ID" property="centerId"/>
        <result column="MODLE_ID" property="modleId"/>
        <result column="RULE_SQL" property="ruleSql"/>
        <result column="LIMIT_VALUE" property="limitValue"/>
        <result column="RULE_DESC" property="ruleDesc"/>
        <result column="ENV_TYPE" property="envType"/>
    </resultMap>

    <sql id="rptCheckCfgColumns">
        RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE
    </sql>

    <select id="qryAuditCfgWithCondition" resultMap="rptCheckCfgMap">
        select
        <include refid="rptCheckCfgColumns"></include>
        from RPT_CHECK_CFG where 1=1
        <if test="ruleId != null">
            and RULE_ID = #{ruleId}
        </if>
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="envType != null">
            and ENV_TYPE in (1,3)
        </if>
        order by RULE_ID
    </select>

    <select id="qryRptCheckCfgCreateTimeWithDuration" resultType="com.suke.czx.newland.dto.AuditItemDto">
        select to_char(create_date, 'yyyymmdd') dayTimeStr, count(*) countVal
        from RPT_CHECK_CFG
        where
        CREATE_DATE > sysdate - #{duration}
        GRoUP BY to_char(create_date, 'yyyymmdd')
    </select>

    <select id="qryRptCheckCfUpdateTimeWithDuration" resultType="com.suke.czx.newland.dto.AuditItemDto">
        select to_char(modify_date, 'yyyymmdd') dayTimeStr, count(*) countVal
        from RPT_CHECK_CFG
        where
            modify_date > sysdate - #{duration}
        GRoUP BY to_char(modify_date, 'yyyymmdd')
    </select>

</mapper>