package com.suke.czx.newland.service;

import com.github.pagehelper.PageInfo;
import com.suke.czx.newland.dto.AuditFileDatasourceLogQryDto;
import com.suke.czx.newland.dto.AuditFileDatasourceRuleQryDto;
import com.suke.czx.newland.dto.AuditUploadQryConditionDto;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;
import com.suke.czx.newland.po.AuditFileDatasourceRulePo;
import com.suke.czx.newland.po.AuditUploadFilePo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface FileAuditWithDatasourceService {


    PageInfo<AuditUploadFilePo> qryAuditUploadFileWithPageInfo(AuditUploadQryConditionDto condition);


    void addAuditDatasourceRule(AuditFileDatasourceRulePo record);

    void editAuditDatasourceRule(AuditFileDatasourceRulePo record);

    PageInfo<AuditFileDatasourceRulePo> qryAuditDatasourceRuleWithPageInfo(AuditFileDatasourceRuleQryDto condition);

    void delAuditDatasourceRule(List<String> ids);

    void doDatasourceFileAudit(String fileId, String ruleId);

    void downloadAuditFileDatasourceLog(String fileId, HttpServletResponse response);
}
