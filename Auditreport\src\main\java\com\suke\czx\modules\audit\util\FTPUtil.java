package com.suke.czx.modules.audit.util;

import com.alibaba.druid.util.Utils;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Vector;

/**
 * 报表上传下载工具类
 */
@Slf4j
@Component
public class FTPUtil {

    private static String host;
    @Value("${ftp.host}")
    public void setHost(String host) {
        FTPUtil.host = host;
    }

    private static String user;
    @Value("${ftp.user}")
    public void setUser(String user) {
        FTPUtil.user = user;
    }

    private static String password;
    @Value("${ftp.password}")
    public void setPassword(String password) {
        FTPUtil.password = password;
    }

    private static String basePath;
    @Value("${ftp.basePath}")
    public void setBasePath(String basePath) {
        FTPUtil.basePath = basePath;
    }

    private static Integer port;
    @Value("${ftp.port}")
    public void setPort(Integer portName) {
        port = portName;
    }

    private static String logPath;
    @Value("${ftp.logPath}")
    public void setLogPath(String logPath) {
        FTPUtil.logPath = logPath;
    }

    private static String logName;
    @Value("${ftp.logName}")
    public void setLogName(String logName) {
        FTPUtil.logName = logName;
    }

    /**
     * 存放比对结果路径
     */
    private static String comPath;
    @Value("${ftp.comPath}")
    public void setComPath(String comPath) {
        FTPUtil.comPath = comPath;
    }

    /**
     * 存放规则配置模版路径
     */
    private static String rulePath;
    @Value("${ftp.rulePath}")
    public void setRulePath(String rulePath) {
        FTPUtil.rulePath = rulePath;
    }

    public static InputStream inputStream = null;


    /**
     * 上传文件到服务器
     * @param bytes
     * @param fileName
     * @throws Exception
     */
    public static void sshSftp(byte[] bytes, String fileName) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(basePath);
            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                log.info(v.get(i).toString());
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            outputStream = sftp.put(fileName);
            outputStream.write(bytes);
        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 从服务器下载文件
     * @param fileName
     * @param response
     * @throws Exception
     */
    public static void getFile(String fileName, HttpServletResponse response) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(basePath);
            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                log.info(v.get(i).toString());
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            // outputStream = sftp.put(fileName);
            // outputStream.write(bytes);
            ServletOutputStream os = response.getOutputStream();
            response.setHeader("Content-disposition","attachment;filename=" + URLEncoder.encode(fileName,"UTF-8"));
            response.setContentType("application/octet-stream");
            sftp.get(fileName,os);
            response.flushBuffer();

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 删除远程文件(根据文件名)
     * @param fileName
     * @throws Exception
     */
    public static void delFile(String fileName,int fileCode) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(1000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            if(fileCode==1){
                sftp.cd(basePath);
            }else if(fileCode==2){
                sftp.cd(comPath);
            }

            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                log.info(v.get(i).toString());
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            // outputStream = sftp.put(fileName);
            // outputStream.write(bytes);
            sftp.rm(fileName);

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 获取报表文件流
     * @param fileName
     * @throws Exception
     */
    public static ByteArrayOutputStream getFileInputStream(String fileName) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(basePath);
            inputStream = sftp.get(fileName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > -1) {
                bos.write(buffer,0,len);
            }
            bos.flush();
            return bos;

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
            return null;
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 上传比对后的文件
     * @param bytes
     * @param fileName
     * @throws Exception
     */
    public static void uploadComparisonFile(byte[] bytes, String fileName) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(comPath);
            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                log.info(v.get(i).toString());
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            outputStream = sftp.put(fileName);
            outputStream.write(bytes);
        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 下载比对文件
     * @param fileName
     * @param response
     * @throws Exception
     */
    public static void getComparisonFile(String fileName, HttpServletResponse response) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(comPath);
            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                log.info(v.get(i).toString());
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            // outputStream = sftp.put(fileName);
            // outputStream.write(bytes);
            ServletOutputStream os = response.getOutputStream();
            response.setHeader("Content-disposition","attachment;filename=" + URLEncoder.encode(fileName,"UTF-8"));
            response.setContentType("application/octet-stream");
            sftp.get(fileName,os);

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 删除比对文件
     * @param fileName
     * @throws Exception
     */
    public static void delComparisonFile(String fileName) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(1000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(comPath);
            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                log.info(v.get(i).toString());
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            // outputStream = sftp.put(fileName);
            // outputStream.write(bytes);
            sftp.rm(fileName);

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    /**
     * 获取指定路径下的文件流
     * @return
     * @throws Exception
     */
    public static ByteArrayOutputStream getLogFileInputStream() throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user,host);
        }else {
            session = jsch.getSession(user,host,port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking","no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(1000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(logPath);
            inputStream = sftp.get(logName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) > -1) {
                bos.write(buffer,0,len);
            }
            bos.flush();
            return bos;

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
            return null;
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }


    /**
     * 下载规则配置模版
     * @param fileName
     * @param filePath
     * @param response
     * @throws Exception
     */
    public static void downloadFile(String fileName,String filePath, HttpServletResponse response) throws Exception {


        Path path = Paths.get(filePath+fileName);

        // 设置响应头信息
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(fileName,"UTF-8") );

        // 读取文件内容并写入响应输出流
        try (InputStream inputStream = Files.newInputStream(path)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.flushBuffer();
        }catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }

    }

    /**
     * 预览文件流
     * @param fileName
     * @param response
     */
    public static void preViewFile(String fileName, HttpServletResponse response) {
        try {
        Path path = Paths.get(basePath + (basePath.endsWith("/") ? "" : "/") + fileName);
        // 设置响应头信息
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(fileName,"UTF-8") );
        // 读取文件内容并写入响应输出流
            InputStream inputStream = Files.newInputStream(path);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.flushBuffer();
        }catch (Exception e) {
            e.printStackTrace();
        }
    }
}
