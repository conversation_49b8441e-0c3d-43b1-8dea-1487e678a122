<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.suke.czx.modules.log.dao.OperationDao">

    <!-- 查询所有日志列表 -->
    <select id="queryLogList" resultMap="logList">
        select
               id,
               username,
               operation,
               method,
               params,
               execute_time,
               ip,
               create_time
        from
             audit_user_log
        <where>
            <if test="userName.trim() != '' and userName != null">
                and username like concat(concat('%',#{userName}),'%')
            </if>
            <if test="operation.trim() != '' and operation != null">
                and operation like concat(concat('%',#{operation}),'%')
            </if>
        </where>
        order by create_time desc
    </select>

    <resultMap id="logList" type="com.suke.czx.modules.log.entity.OperationEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="operation" property="operation" jdbcType="VARCHAR"/>
        <result column="method" property="method" jdbcType="VARCHAR"/>
        <result column="params" property="params" jdbcType="CLOB" typeHandler="org.apache.ibatis.type.ClobTypeHandler"/>
        <result column="execute_time" property="executeTime" jdbcType="BIGINT"/>
        <result column="ip" property="ip" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
</mapper>