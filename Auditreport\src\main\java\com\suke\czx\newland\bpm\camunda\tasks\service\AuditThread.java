package com.suke.czx.newland.bpm.camunda.tasks.service;

import com.alibaba.fastjson.JSONObject;
import com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper;
import com.suke.czx.newland.dao.mgrdbmapper.RptCheckCfgMapper;
import com.suke.czx.newland.dao.mgrdbmapper.RptCheckLogMapper;
import com.suke.czx.newland.dao.mgrdbmapper.SystemMapper;
import com.suke.czx.newland.dto.DbSystemInfoDto;
import com.suke.czx.newland.po.RptCheckCfgPo;
import com.suke.czx.newland.util.AuditUtils;
import com.suke.czx.newland.util.DataSourceUtil;
import com.suke.czx.newland.util.SpringConfigTool;
import com.suke.czx.newland.vo.audit.AuditDictVo;
import com.suke.czx.newland.vo.audit.AuditExecVo;
import com.suke.czx.newland.vo.audit.AuditLogVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;
import java.sql.Date;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;

public class AuditThread implements Runnable {

    private Logger logger = LoggerFactory.getLogger(AuditThread.class);

    private IAuditCfgMapper iAuditCfgMapper;

    private final RptCheckLogMapper rptCheckLogMapper;

    private final SystemMapper systemMapper;

    private final RptCheckCfgMapper rptCheckCfgMapper;

    private final DataSourceUtil dataSourceUtil;

    private List<AuditExecVo> execVos;

    private String execPerson;

    public AuditThread(List<AuditExecVo> execVos, IAuditCfgMapper iAuditCfgMapper, String execPerson) {
        //记录稽核开始
        this.execVos = execVos;
        this.iAuditCfgMapper = iAuditCfgMapper;
        this.execPerson = execPerson;
        this.rptCheckLogMapper = (RptCheckLogMapper) SpringConfigTool.getBean(RptCheckLogMapper.class);
        this.systemMapper = ((SystemMapper) SpringConfigTool.getBean(SystemMapper.class));
        this.rptCheckCfgMapper = ((RptCheckCfgMapper) SpringConfigTool.getBean(RptCheckCfgMapper.class));
        this.dataSourceUtil = ((DataSourceUtil) SpringConfigTool.getBean(DataSourceUtil.class));
    }
    @Override
    public void run() {
        //将今天本次稽核之前的记录更改为历史工单状态
        iAuditCfgMapper.updateHisToryByDate();
        //删除9个月之前的历史工单数据
        iAuditCfgMapper.deleteHistoryByDate();
        //开始稽核循环处理工单
        execVos.forEach(execVo -> {
            try {
                long begin = System.currentTimeMillis();
                logger.info("[正在处理稽核工单][id:{}][name:{}]", execVo.getId(), execVo.getName());
                //如果该稽核点存在状态为0的工单，跳过不处理
                AuditLogVo condition = new AuditLogVo();
                condition.setStatus((short) 0);
                condition.setRuleId(execVo.getId());
                List<AuditLogVo> processingRecordList = rptCheckLogMapper.qryRptCheckLogWithCondition(condition);
                if (processingRecordList != null && processingRecordList.size() > 0) {
                    logger.info("[稽核工单][id:{}][name:{}]:存在状态为0的工单，跳过不处理.", execVo.getId(), execVo.getName());
                    return;
                }

                //获取用户信息
                DbSystemInfoDto dbSystemInfo = systemMapper.qryDbSystemInfo();
                if (execVo.getName() != null && !execVo.getName().isEmpty()) {
                    dbSystemInfo.setExecPerson(execPerson);
                }

                //开始处理
                RptCheckCfgPo cfgCondition = new RptCheckCfgPo();
                cfgCondition.setRuleId(String.valueOf(execVo.getId()));
                cfgCondition.setStatus("1");
                List<RptCheckCfgPo> checkCfgList = rptCheckCfgMapper.qryAuditCfgWithCondition(cfgCondition);
                if (checkCfgList == null || checkCfgList.isEmpty()) {
                    logger.info("[稽核工单][id:{}][name:{}]:配置不存在.", execVo.getId(), execVo.getName());
                    return;
                }

                //执行稽核sql
                RptCheckCfgPo rptCfg = checkCfgList.get(0);
                JSONObject res = dataSourceUtil.executeSql(execVo.getDatasourceId(), rptCfg.getRuleSql().replaceAll(";", ""), true);
                String resStr = null;
                for (Map.Entry<String, Object> entry : res.entrySet()) {
                    resStr = entry.getValue().toString();
                    break;
                }
                AuditLogVo logRecord = constructLog(dbSystemInfo, rptCfg, resStr, execPerson);
                logRecord.setExecMsec(System.currentTimeMillis() - begin);
                logger.info("log record : {}", logRecord);
                rptCheckLogMapper.insertRptCheckLog(logRecord);
            } catch (Exception e) {
                logger.error("[稽核工单处理异常][id:{}][name:{}][message:{}]", execVo.getId(), execVo.getName(), e.getMessage());
            }
            logger.info("[稽核工单处理完成][id:{}][name:{}]", execVo.getId(), execVo.getName());
        });
        logger.info("[稽核工单处理结束]");
        AuditUtils.setStauts(-1);
    }

    private AuditLogVo constructLog(DbSystemInfoDto dbSystemInfo, RptCheckCfgPo rptCfg, Object sqlVal, String execPerson) throws ScriptException {
        AuditLogVo auditLogVo = new AuditLogVo();
        auditLogVo.setDbName(dbSystemInfo.getDbName());
        auditLogVo.setDbMachine(dbSystemInfo.getDbMachine());
        auditLogVo.setRuleId(Long.parseLong(rptCfg.getRuleId()));
        if (rptCfg.getModifyPerson() != null){
            auditLogVo.setModifyPerson(rptCfg.getModifyPerson());
        }else {
            auditLogVo.setModifyPerson("admin");
        }
        if (rptCfg.getModifyDate() != null) {
            auditLogVo.setModifyDate(Date.from(rptCfg.getModifyDate().atZone(ZoneId.systemDefault()).toInstant()));
        } else {
            auditLogVo.setModifyDate(new java.util.Date());
        }
        auditLogVo.setChkTypeId(rptCfg.getChkTypeId());
        auditLogVo.setCenterId(rptCfg.getCenterId());
        auditLogVo.setModleId(rptCfg.getModleId());
        AuditDictVo chkTypeDicInfo = iAuditCfgMapper.qryAuditDictByDictValueWithoutStatus(rptCfg.getChkTypeId());
        AuditDictVo centerDicInfo = iAuditCfgMapper.qryAuditDictByDictValueWithoutStatus(rptCfg.getCenterId());
        AuditDictVo modleDicInfo = iAuditCfgMapper.qryAuditDictByDictValueWithoutStatus(rptCfg.getModleId());
        if (chkTypeDicInfo != null && chkTypeDicInfo.getDictName() != null) {
            auditLogVo.setChkTypeName(chkTypeDicInfo.getDictName());
        } else {
            throw new IllegalArgumentException("chk type id : {" + rptCfg.getChkTypeId() + "} check type dic info 不存在，请检查配置.");
        }
        if (centerDicInfo != null && centerDicInfo.getDictName() != null) {
            auditLogVo.setCenterName(centerDicInfo.getDictName());
        } else {
            throw new IllegalArgumentException("center id : {" + rptCfg.getCenterId() + "} center dic info 不存在，请检查配置.");
        }

        if (modleDicInfo != null && modleDicInfo.getDictName() != null) {
            auditLogVo.setModleName(modleDicInfo.getDictName());
        } else {
            throw new IllegalArgumentException("modle id : {" + rptCfg.getModleId() + "} modle dic info 不存在，请检查配置.");
        }

        auditLogVo.setRuleName(rptCfg.getRuleName());
        auditLogVo.setRuleSql(rptCfg.getRuleSql());
        String limitValue = rptCfg.getLimitValue();
        auditLogVo.setLimitValue(limitValue);
        auditLogVo.setSqlValue(String.valueOf(sqlVal));
        auditLogVo.setStatus((short) 1);
        auditLogVo.setCreateDate(Date.from(rptCfg.getCreateDate().atZone(ZoneId.systemDefault()).toInstant()));
        auditLogVo.setCreatePerson(rptCfg.getCreatePerson());
        auditLogVo.setExecDate(new java.util.Date());
        auditLogVo.setExecPerson(execPerson);
        auditLogVo.setExecRemark("");
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine engine = manager.getEngineByName("js");
        boolean conclusion;
        //不等于号 js 脚本引擎 不等于使用 !=
        if (limitValue.contains("<>")){
            limitValue = limitValue.replace("<>", "!=");
            conclusion = (boolean) engine.eval(sqlVal + limitValue);
        } else if (limitValue.contains("=")) {
            conclusion = (boolean) engine.eval(sqlVal + "=" + limitValue);
        } else {
            conclusion = (boolean) engine.eval(sqlVal + limitValue);
        }
        if (conclusion) {
            auditLogVo.setConclusion("异常");
            auditLogVo.setAnalysisRes("待分析");
        } else {
            auditLogVo.setConclusion("正常");
            auditLogVo.setAnalysisRes("无问题");
        }
        return auditLogVo;
    }


}
