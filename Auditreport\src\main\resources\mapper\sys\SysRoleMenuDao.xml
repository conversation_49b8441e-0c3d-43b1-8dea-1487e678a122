<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.sys.dao.SysRoleMenuDao">

	<!-- oracle使用foreach批量插入要使用到序列 -->
	<insert id="save">
		<selectKey keyProperty="id" resultType="long" order="BEFORE">
			select nvl(max(id),0)+1 from audit_role_menu
		</selectKey>
		insert into audit_role_menu
		(
		 	id,
			role_id,
			menu_id
		)
		<foreach collection="menuIdList" item="item" index="index" separator="UNION ALL">
		select
		    #{id},
			#{roleId}, 
			#{item} 
		from dual
		</foreach>
	</insert>
	<insert id="saveRoleMenu">
		<selectKey keyProperty="id" resultType="long" order="BEFORE">
			select nvl(max(id),0)+1 from audit_role_menu
		</selectKey>
		insert into audit_role_menu(id,role_id,menu_id) values (#{id},#{roleId},#{item})
	</insert>

	<delete id="delete">
		delete from audit_role_menu where role_id = #{value}
	</delete>
	
	<select id="queryMenuIdList" resultType="long">
		select menu_id from audit_role_menu where role_id = #{value}
	</select>

	<select id="queryTotal" resultType="int">
		select count(*) from audit_role_menu where menu_id = #{id}
	</select>

</mapper>