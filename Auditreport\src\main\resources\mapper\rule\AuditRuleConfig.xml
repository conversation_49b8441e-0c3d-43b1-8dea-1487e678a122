<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.modules.rule.dao.AuditRuleConfigDao">

    <!-- 将规则信息保存到子表中 -->
    <insert id="saveRuleConfig">
        <selectKey keyProperty="id" resultType="long" order="BEFORE">
            select nvl(max(id),0)+1 from audit_rule_config
        </selectKey>
        <if test="titleEndRow == null">
            insert into audit_rule_config(id,fa_id,name,type,rule_row,rule_column,parameter,remarks,title_row) values (#{id},#{faId},#{name},#{type},#{ruleRow},#{ruleColumn},#{parameter,jdbcType=VARCHAR},#{remarks,jdbcType=VARCHAR},#{titleRow})
        </if>
        <if test="titleEndRow != null">
            insert into audit_rule_config(id,fa_id,name,type,rule_row,rule_column,parameter,remarks,title_row,title_end_row) values (#{id},#{faId},#{name},#{type},#{ruleRow},#{ruleColumn},#{parameter,jdbcType=VARCHAR},#{remarks,jdbcType=VARCHAR},#{titleRow},#{titleEndRow})
        </if>
    </insert>

    <!-- 根据规则父表的id查询规则子表 -->
    <select id="queryRuleConfigList" resultType="com.suke.czx.modules.rule.entity.AuditRuleConfig">
        select b.name faName, a.id,
        a.fa_id,
        a.parameter,
        a.remarks,
        a.create_time,
        a.deleted,
        a.rule_row,
        a.rule_column,
        a.name,
        a.type,
        b.CREATE_USER_ID create_name,
        a.update_name,
        a.update_time,
        a.title_row,
        a.title_end_row
        from audit_rule_config a,audit_rule b
        <where>
            <if test="faId != '' and faId != null">
                and fa_id = #{faId}
            </if>
            <if test="type != '' and type != null">
                and type = #{type}
            </if>
            and a.fa_id=b.id
        </where>
        order by id
    </select>

    <!-- 根据id更新规则信息 -->
    <update id="update" parameterType="com.suke.czx.modules.rule.entity.AuditRuleConfig">
        update audit_rule_config
        <set>
            <if test="name != null and name != ''">
            name = #{name},
            </if>
            <if test="ruleRow != null and ruleRow != ''">
                rule_row = #{ruleRow},
            </if>
            <if test="ruleColumn != null and ruleColumn != ''">
                rule_column = #{ruleColumn},
            </if>
            <if test="type != null and type != ''">
                type = #{type},
            </if>
            <if test="titleRow != null and titleRow != ''">
                TITLE_ROW = #{titleRow},
            </if>

            <if test="titleEndRow != null and titleEndRow != ''">
                TITLE_END_ROW = #{titleEndRow},
            </if>
            <if test="titleEndRow == null">
                TITLE_END_ROW = null,
            </if>
            <!-- <if test="parameter != null and parameter != ''"> -->
            <!--     parameter = #{parameter}, -->
            <!-- </if> -->
            parameter = #{parameter,jdbcType=VARCHAR},
            <!-- <if test="remarks != null and remarks != ''"> -->
            <!--     remarks = #{remarks} -->
            <!-- </if> -->
            remarks = #{remarks,jdbcType=VARCHAR}
        </set>
        where id = #{id}
    </update>
    <!-- 根据id删除规则信息 -->
    <delete id="delete" parameterType="long">
        delete from audit_rule_config where id = #{id}
    </delete>

    <select id="queryObject" resultType="com.suke.czx.modules.rule.entity.AuditRuleConfig">
        select * from audit_rule_config where id = #{value}
    </select>
    <select id="queryTotal" resultType="int">
        select count(*) from audit_rule_config where fa_id = #{id}
    </select>
    <select id="getAuditRuleById" resultType="com.suke.czx.modules.rule.entity.AuditRuleConfig">
        select * from audit_rule_config where fa_id = #{faId}
    </select>
    <delete id="delRuleConfigByIds">
        delete from audit_rule_config where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </delete>

</mapper>