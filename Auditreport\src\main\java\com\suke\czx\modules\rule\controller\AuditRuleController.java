package com.suke.czx.modules.rule.controller;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.druid.util.Utils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.audit.util.FTPUtil;
import com.suke.czx.modules.rule.entity.AuditRule;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import com.suke.czx.modules.rule.service.AuditRuleConfigService;
import com.suke.czx.modules.rule.service.AuditRuleService;
import com.suke.czx.modules.rule.util.DownloadExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.print.DocFlavor;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 规则父表
 */
@Slf4j
@RestController
@RequestMapping("/audit/rule")
public class AuditRuleController extends AbstractController{

    @Autowired
    private AuditRuleService auditRuleService;

    @Autowired
    private AuditRuleConfigService auditRuleConfigService;

    /**
     * 上传规则表(将信息存入规则父表和子表中)
     * @param file
     * @return
     * @throws IOException
     */
    @SysLog("上传稽核规则")
    @RequestMapping(value = "/upload",method = RequestMethod.POST)
    public R uploadRule(MultipartFile file)  {
        //父表存入成功，再开始存子表(全部在此完成，返回值：-1文件名错误；-2解析异常；-3保存异常)
        try {
            return auditRuleService.saveAuditRule(file,getUserId());
        }catch (Exception e){
            log.error(Utils.getStackTrace(e));
            return R.error("上传失败，请联系管理员查看.");
        }
    }

    /**
     * 查询全部规则分页（父表）
     * @return
     */
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public R getAllRule(@RequestParam(value = "id",defaultValue = "") String id) {
        List<AuditRule> auditRules = auditRuleService.qryAuditRuleWithKeyword(id);
        int total = 0;
        if (!auditRules.isEmpty()) {
            total = auditRules.size();
        }
        return Objects.requireNonNull(R.ok().put("auditRuleList", auditRules)).put("total",total);
    }

    /**
     * 根据id更新规则名称
     * @param id
     * @param name
     * @return
     */
    @SysLog("更新稽核规则名称")
    @RequestMapping(value = "/update/{id}",method = RequestMethod.PUT)
    public R updateRule(@PathVariable("id") String id, @RequestParam("name") String name) {
        Integer auditRuleres = auditRuleService.updateRuleById(id,name,getUserId());
//        AuditRuleConfig auditRuleConfig = new AuditRuleConfig();
//        auditRuleConfig.setName(name);
//        auditRuleConfig.setFaId(name);
//        Integer auditRuleConfigres = auditRuleConfigService.updateRuleConfig(auditRuleConfig);
        if (auditRuleres > 0) {
            return R.ok().put("result","规则名称更新成功!");
        }else {
            return R.error().put("result","已经存在此规则名称!");
        }
    }

    @SysLog("删除稽核规则")
    @RequestMapping(value = "/delete/{id}",method = RequestMethod.DELETE)
    public R deleteRule(@PathVariable("id") String id) {
        Integer res = auditRuleService.deleteRuleById(id);
        if (res > 0) {
            return R.ok().put("result","删除成功!");
        }else if (res < 0) {
            return R.error().put("result","该规则下还存在子规则，请先删除子规则!");
        }else {
            return R.error().put("result","父规则删除失败!");
        }
    }


    @SysLog("下载规则配置模版")
    @RequestMapping(value = "/download",method = RequestMethod.GET)
    public void downloadComparisonFile(@RequestParam String fileName, HttpServletResponse response) {
        log.info("file name:{}", fileName);
//        Integer url_id = 1001;
//        Map<String,Object> url = auditRuleService.queryAuditRuleUrl(url_id);
//        String filePath=String.valueOf(url.get("URL"));
//        try {
//            FTPUtil.downloadFile(fileName,filePath,response);
//        } catch (Exception e) {
//            log.error(Utils.getStackTrace(e));
//            log.info("异常情况，默认反馈项目自带配置模板.");
//            downLoadExcepHandle(response);
//        }
        try {
            //构建ClassPathResource对象
//            Resource resource = new FileSystemResource(fileName);
            List<URL> resources = ResourceUtil.getResources("template/" + fileName);
            URL resource = resources.get(0);
            InputStream inputStream = resource.openStream();
            //设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(fileName,"UTF-8") );
            //获取文件输入流
//            InputStream inputStream = resource.getInputStream();
            //将文件内容写入响应输出流
            byte[] buffer = new byte[1024];
            int byteRead;
            while ((byteRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer,0,byteRead);
            }
            response.flushBuffer();
            logger.info("下载配置规则模板成功！");
        } catch (IOException e) {
            log.error("下载配置规则模板异常：{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void downLoadExcepHandle(HttpServletResponse response){
        File file = new File("./src/main/resources/template/配置规则模板.xlsx");
        // 读取文件内容并写入响应输出流
        try (InputStream inputStream = Files.newInputStream(file.toPath())) {
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(file.getName(),"UTF-8") );
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                response.getOutputStream().write(buffer, 0, bytesRead);
            }
            response.flushBuffer();
        }catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }
}