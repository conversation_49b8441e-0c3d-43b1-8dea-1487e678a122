package com.suke.czx.modules.AI.util;

import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.Vector;

/**
 * 报表上传下载工具类
 */
@Slf4j
@Component
public class FTPAiUtils {

    private static String host;

    @Value("${ftp.host}")
    public void setHost(String host) {
        FTPAiUtils.host = host;
    }

    private static String user;

    @Value("${ftp.user}")
    public void setUser(String user) {
        FTPAiUtils.user = user;
    }

    private static String password;

    @Value("${ftp.password}")
    public void setPassword(String password) {
        FTPAiUtils.password = password;
    }

    private static String basePath;

    @Value("${ftp.basePath}")
    public void setBasePath(String basePath) {
        FTPAiUtils.basePath = basePath;
    }

    private static String aiPath;

    @Value("${ftp.aiPath}")
    public void setAiPath(String basePath) {
        FTPAiUtils.aiPath = basePath;
    }


    private static Integer port;

    @Value("${ftp.port}")
    public void setPort(Integer portName) {
        port = portName;
    }

    private static String logPath;

    @Value("${ftp.logPath}")
    public void setLogPath(String logPath) {
        FTPAiUtils.logPath = logPath;
    }

    private static String logName;

    @Value("${ftp.logName}")
    public void setLogName(String logName) {
        FTPAiUtils.logName = logName;
    }

    /**
     * 存放比对结果路径
     */
    private static String comPath;

    @Value("${ftp.comPath}")
    public void setComPath(String comPath) {
        FTPAiUtils.comPath = comPath;
    }

    /**
     * 存放规则配置模版路径
     */
    private static String rulePath;

    @Value("${ftp.rulePath}")
    public void setRulePath(String rulePath) {
        FTPAiUtils.rulePath = rulePath;
    }

    public static InputStream inputStream = null;


    /**
     * 从服务器下载文件
     *
     * @param fileName
     * @param response
     * @throws Exception
     */
    public static void getFile(String fileName, HttpServletResponse response) throws Exception {
        Session session = null;
        Channel channel = null;

        JSch jsch = new JSch();
        if (port <= 0) {
            //连接服务器，采用默认端口
            session = jsch.getSession(user, host);
        } else {
            session = jsch.getSession(user, host, port);
        }
        //如果服务器连接不上，则抛出异常
        if (session == null) {
            throw new Exception("session is null");
        }
        //设置登录主机的密码
        session.setPassword(password);
        //设置第一次登陆的时候提示，可选值：{ask | yes | no}
        session.setConfig("StrictHostKeyChecking", "no");
        //设置登录超时时间
        session.connect(30000);
        OutputStream outputStream = null;

        try {
            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(aiPath);
            //列出服务器中指定的文件列表
            Vector v = sftp.ls("*");
            for (int i = 0; i < v.size(); i++) {
                System.out.println(v.get(i));
            }
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            // outputStream = sftp.put(fileName);
            // outputStream.write(bytes);
            ServletOutputStream os = response.getOutputStream();
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType("application/octet-stream");
            sftp.get(fileName, os);
            response.flushBuffer();

        } catch (JSchException | SftpException e) {
            e.printStackTrace();
        } finally {
            //关流操作
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    public static void getAIAuditedFile(String fileName, HttpServletResponse response) {
        Session session = null;
        Channel channel = null;
        try {
            JSch jsch = new JSch();
            if (port <= 0) {
                //连接服务器，采用默认端口
                session = jsch.getSession(user, host);
            } else {
                session = jsch.getSession(user, host, port);
            }
            //如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new RuntimeException("session is null");
            }
            //设置登录主机的密码
            session.setPassword(password);
            //设置第一次登陆的时候提示，可选值：{ask | yes | no}
            session.setConfig("StrictHostKeyChecking", "no");
            //设置登录超时时间
            session.connect(30000);

            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(aiPath + "/out");
            //列出服务器中指定的文件列表
            ServletOutputStream os = response.getOutputStream();
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setContentType("application/octet-stream");
            sftp.get(fileName, os);
            response.flushBuffer();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            //关流操作
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    public static ByteArrayOutputStream getFileOutputStream(String orgFileName) {
        Session session = null;
        Channel channel = null;
        ByteArrayOutputStream res;
        try {
            res = new ByteArrayOutputStream();
            JSch jsch = new JSch();
            if (port <= 0) {
                //连接服务器，采用默认端口
                session = jsch.getSession(user, host);
            } else {
                session = jsch.getSession(user, host, port);
            }
            //如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new RuntimeException("session is null");
            }
            //设置登录主机的密码
            session.setPassword(password);
            //设置第一次登陆的时候提示，可选值：{ask | yes | no}
            session.setConfig("StrictHostKeyChecking", "no");
            //设置登录超时时间
            session.connect(30000);

            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(aiPath);

            sftp.get(orgFileName, res);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            //关流操作
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
        return res;
    }

    public static void doUploadWithStream(ByteArrayInputStream input, String fileName) {
        Session session = null;
        Channel channel = null;
        try {
            JSch jsch = new JSch();
            if (port <= 0) {
                //连接服务器，采用默认端口
                session = jsch.getSession(user, host);
            } else {
                session = jsch.getSession(user, host, port);
            }
            //如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new RuntimeException("session is null");
            }
            //设置登录主机的密码
            session.setPassword(password);
            //设置第一次登陆的时候提示，可选值：{ask | yes | no}
            session.setConfig("StrictHostKeyChecking", "no");
            //设置登录超时时间
            session.connect(30000);

            //建立sftp通信通道
            channel = session.openChannel("sftp");
            channel.connect(10000);
            ChannelSftp sftp = (ChannelSftp) channel;
            //进入服务器指定的文件夹
            sftp.cd(aiPath + "/out");
            sftp.put(input, aiPath + "/out/" + fileName);
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            //关流操作
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }

    public static String doCommandAndGetRes(String command) {
        Session session = null;
        ChannelExec channel = null;
        try {
            JSch jsch = new JSch();
            if (port <= 0) {
                //连接服务器，采用默认端口
                session = jsch.getSession(user, host);
            } else {
                session = jsch.getSession(user, host, port);
            }
            //如果服务器连接不上，则抛出异常
            if (session == null) {
                throw new RuntimeException("session is null");
            }
            //设置登录主机的密码
            session.setPassword(password);
            //设置第一次登陆的时候提示，可选值：{ask | yes | no}
            session.setConfig("StrictHostKeyChecking", "no");
            //设置登录超时时间
            session.connect(30000);

            //建立sftp通信通道
            channel = (ChannelExec) session.openChannel("exec");
            channel.setCommand(command);
            // 获取命令的输入流
            InputStream in = channel.getInputStream();
            channel.connect();
            // 读取命令的输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            String line;
            StringBuilder result = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                result.append(line).append("\n");
            }
            log.info("command org res : " + result);
            if (result.length() == 0 || result.toString().isEmpty()) {
                return "";
            }else {
                return result.substring(result.indexOf("[") + 1, result.indexOf("]"));
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            //关流操作
            if (session != null) {
                session.disconnect();
            }
            if (channel != null) {
                channel.disconnect();
            }
        }
    }
}
