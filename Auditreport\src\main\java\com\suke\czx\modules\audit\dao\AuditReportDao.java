package com.suke.czx.modules.audit.dao;

import com.suke.czx.modules.audit.entity.AuditReport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表信息
 */
@Mapper
public interface AuditReportDao extends BaseDao<AuditReport>{

    /**
     * 根据id查询报表
     * @param ids
     * @return
     */
    List<AuditReport> queryReportsById(Long[] ids);

    /**
     * 修改报表信息
     * @param reportId
     * @param reportType
     */
    void updateById(@Param("reportId") Long reportId, @Param("reportType") String reportType);

    int deleteBatch(Long[] id);
}
