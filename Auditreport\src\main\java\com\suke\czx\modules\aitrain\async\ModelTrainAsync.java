package com.suke.czx.modules.aitrain.async;

import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import com.opencsv.exceptions.CsvValidationException;
import com.suke.czx.config.FtpProperties;
import com.suke.czx.modules.aitrain.entity.ModelTrain;
import com.suke.czx.modules.aitrain.entity.ModelTrainParam;
import com.suke.czx.modules.aitrain.mapper.ModelTrainMapper;
import com.suke.czx.modules.aitrain.utils.ModelTrainUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.*;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

/**
 * 异步模型训练方法
 */

@Slf4j
@Component
public class ModelTrainAsync {

    @Resource
    private ModelTrainUtil modelTrainUtil;

    @Resource
    private ModelTrainMapper modelTrainMapper;

    /**
     * 执行AI模型训练的总体流程
     * @param modelTrainParam
     * @param modelName
     * @param normalPath
     * @param abnormalPath
     * @param ftpProperties
     */
    @Async("taskExecutor")
    public void trainModel(ModelTrainParam modelTrainParam, String modelName, String normalPath, String abnormalPath, FtpProperties ftpProperties) {
        log.info("调用模型训练工具类，开始进行模型训练=========================");
        //保存模型训练信息
        int modelTrainId = saveModelTrainInfo(modelTrainParam, modelName);
        String outputPath = modelTrainUtil.trainModel(modelTrainParam, modelName, normalPath, abnormalPath, ftpProperties,modelTrainId);
        log.info("调用模型训练工具类，返回训练结果文件路径========================={}",outputPath);
        log.info("调用模型训练工具类，结束进行模型训练=========================");
        List<ModelTrain> modelTrainList = parseTrainResultCsv(outputPath,modelName);
        log.info("解析训练结果csv文件=========================：{}",modelTrainList);
        //批量插入数据
        if (!modelTrainList.isEmpty()) {
            int batchInsertRes = modelTrainMapper.batchInsert(modelTrainList);
            log.info("批量插入数据条数：{}",batchInsertRes);
        }else {
            ModelTrain modelTrain = new ModelTrain();
            modelTrain.setId(modelTrainId);
            modelTrain.setRemark("训练结果为空，请联系管理员");
            int updateRes = modelTrainMapper.update(modelTrain);
            log.info("模型训练结果为空，更新模型训练信息备注（remark）条数：{}",updateRes);
            throw new RuntimeException("训练结果为空，请联系管理员");
        }
        //删除初始化第一条数据
        int delRes = modelTrainMapper.deleteById(modelTrainId);
        log.info("删除初始化第一条数据条数：{}",delRes);
    }

    /**
     * 进行模型信息初始化
     * @param modelTrainParam
     * @param modelName
     * @return
     */
    private int saveModelTrainInfo(ModelTrainParam modelTrainParam, String modelName){
        ModelTrain modelTrain = new ModelTrain();
        modelTrain.setTrainName(modelName);
        modelTrain.setRemark("正在进行模型训练");
        int saveRes = modelTrainMapper.insert(modelTrain);
        log.info("保存模型训练信息初始化数据条数：{}",saveRes);
        return modelTrain.getId();
    }


    /**
     * 从结果csv中解析出训练结果
     * @param filePath
     * @param modelName
     * @return
     */
    public List<ModelTrain> parseTrainResultCsv(String filePath,String modelName) {
        try {
            CSVReader csvReader = new CSVReader(new FileReader(filePath));
            List<ModelTrain> modelTrainList = new ArrayList<>();
//            String[] readNext = csvReader.readNext();
            ModelTrain modelTrain;
            int i = 0;
            while (true){
                modelTrain = new ModelTrain();
                String[] content = csvReader.readNext();
                log.info("content:{}", (Object) content);
                if (i == 0) {
                    i++;
                    continue;
                }
                if(content==null){
                    break;
                }
                modelTrain.setTrainName(modelName);
                modelTrain.setModel(content[0]);
                modelTrain.setScoreTest(Double.parseDouble(content[1]));
                modelTrain.setEvalMetric(content[3]);
                modelTrain.setFitTime(Double.parseDouble(content[6]));
                modelTrain.setCanInfer(content[11]);
                modelTrain.setFitOrder(Integer.parseInt(content[12]));
                modelTrain.setRemark("训练完成");
                modelTrainList.add(modelTrain);
            }
            return modelTrainList;
        } catch (IOException | CsvValidationException e) {
            throw new RuntimeException(e);
        }
    }
}
