package com.suke.czx.newland.vo;

import java.util.Date;

/**
 * 文件名称：SmFlwTaskVo
 * 描    述：任务管理表sm_flw_task实体类
 * 作    者：jianggy
 * 创建日期：2020-06-18
 * 创建时间：10:30
 * 当前版本：1.0
 */
public class SmFlwTaskVo
{
    private String taskId;         // 任务ID
    private String taskName;       // 任务名称
    private int busiType;          // 业务分类 1：出账 2：稽核 3：报表
    private int taskType;          // 任务类型 1：shell 2：oracle 3：api 4：时序库
    private int exeType;           // 操作类型 1：处理 2：检查
    private String taskContent;    // 执行内容
    private String minionNames;    // Saltstack任务的执行主机
    private String expectResult;   // 期待结果
    private int timingRequired;    // 是否需要定时 1：是，0：否
    // 配置规则参照：
    //    流程启动当天，定时执行的任务：
    //        N 03：00
    //    流程启动后，需要第2天定时执行的任务：
    //        N+1 03：00
    //    依此类推...
    private String timingRule;     // 定时规则
    private short status;          // 状态 1、生效 2、失效
    private String taskDesc;       // 任务说明
    private Date createDate;       // 创建时间
    private String createOperator; // 创建者工号
    private Date modiDate;         // 更新时间
    private String modiOperator;   // 更新者工号

    public String getTaskId()
    {
        return taskId;
    }

    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskName()
    {
        return taskName;
    }

    public void setTaskName(String taskName)
    {
        this.taskName = taskName;
    }

    public int getBusiType()
    {
        return busiType;
    }

    public void setBusiType(int busiType)
    {
        this.busiType = busiType;
    }

    public int getTaskType()
    {
        return taskType;
    }

    public void setTaskType(int taskType)
    {
        this.taskType = taskType;
    }

    public int getExeType()
    {
        return exeType;
    }

    public void setExeType(int exeType)
    {
        this.exeType = exeType;
    }

    public String getTaskContent()
    {
        return taskContent;
    }

    public void setTaskContent(String taskContent)
    {
        this.taskContent = taskContent;
    }

    public int getTimingRequired() {
        return timingRequired;
    }

    public void setTimingRequired(int timingRequired) {
        this.timingRequired = timingRequired;
    }

    public String getTimingRule() {
        return timingRule;
    }

    public void setTimingRule(String timingRule) {
        this.timingRule = timingRule;
    }

    public short getStatus()
    {
        return status;
    }

    public void setStatus(short status)
    {
        this.status = status;
    }

    public String getTaskDesc()
    {
        return taskDesc;
    }

    public void setTaskDesc(String taskDesc)
    {
        this.taskDesc = taskDesc;
    }

    public Date getCreateDate()
    {
        return createDate;
    }

    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    public String getCreateOperator()
    {
        return createOperator;
    }

    public void setCreateOperator(String createOperator)
    {
        this.createOperator = createOperator;
    }

    public Date getModiDate()
    {
        return modiDate;
    }

    public void setModiDate(Date modiDate)
    {
        this.modiDate = modiDate;
    }

    public String getModiOperator()
    {
        return modiOperator;
    }

    public void setModiOperator(String modiOperator)
    {
        this.modiOperator = modiOperator;
    }

    public String getExpectResult()
    {
        return expectResult;
    }

    public void setExpectResult(String expectResult)
    {
        this.expectResult = expectResult;
    }

    public String getMinionNames()
    {
        return minionNames;
    }

    public void setMinionNames(String minionNames)
    {
        this.minionNames = minionNames;
    }

    @Override
    public String toString() {
        return "SmFlwTaskVo{" +
                "taskId='" + taskId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", busiType=" + busiType +
                ", taskType=" + taskType +
                ", exeType=" + exeType +
                ", taskContent='" + taskContent + '\'' +
                ", minionNames='" + minionNames + '\'' +
                ", expectResult='" + expectResult + '\'' +
                ", timingRequired=" + timingRequired +
                ", timingRule='" + timingRule + '\'' +
                ", status=" + status +
                ", taskDesc='" + taskDesc + '\'' +
                ", createDate=" + createDate +
                ", createOperator='" + createOperator + '\'' +
                ", modiDate=" + modiDate +
                ", modiOperator='" + modiOperator + '\'' +
                '}';
    }
}
