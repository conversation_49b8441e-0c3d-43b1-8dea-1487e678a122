!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SwaggerUIStandalonePreset=t():e.SwaggerUIStandalonePreset=t()}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={exports:{},id:r,loaded:!1};return e[r].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var n={};return t.m=e,t.c=n,t.p="/dist",t(0)}(function(e){for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))switch(typeof e[t]){case"function":break;case"object":e[t]=function(t){var n=t.slice(1),r=e[t[0]];return function(e,t,i){r.apply(this,[e,t,i].concat(n))}}(e[t]);break;default:e[t]=e[e[t]]}return e}([function(e,t,n){n(1),e.exports=n(56)},function(e,t,n){"use strict";n(2)},function(e,t,n){n(3),n(17),n(30),n(36),e.exports=n(16).Promise},function(e,t,n){"use strict";var r=n(4),i={};i[n(6)("toStringTag")]="z",i+""!="[object z]"&&n(10)(Object.prototype,"toString",function(){return"[object "+r(this)+"]"},!0)},function(e,t,n){var r=n(5),i=n(6)("toStringTag"),o="Arguments"==r(function(){return arguments}());e.exports=function(e){var t,n,s;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=(t=Object(e))[i])?n:o?r(t):"Object"==(s=r(t))&&"function"==typeof t.callee?"Arguments":s}},function(e,t){var n={}.toString;e.exports=function(e){return n.call(e).slice(8,-1)}},function(e,t,n){var r=n(7)("wks"),i=n(9),o=n(8).Symbol;e.exports=function(e){return r[e]||(r[e]=o&&o[e]||(o||i)("Symbol."+e))}},function(e,t,n){var r=n(8),i="__core-js_shared__",o=r[i]||(r[i]={});e.exports=function(e){return o[e]||(o[e]={})}},function(e,t){var n=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(e,t){var n=0,r=Math.random();e.exports=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++n+r).toString(36))}},function(e,t,n){var r=n(8),i=n(11),o=n(9)("src"),s="toString",a=Function[s],u=(""+a).split(s);n(16).inspectSource=function(e){return a.call(e)},(e.exports=function(e,t,n,s){"function"==typeof n&&(n.hasOwnProperty(o)||i(n,o,e[t]?""+e[t]:u.join(String(t))),n.hasOwnProperty("name")||i(n,"name",t)),e===r?e[t]=n:(s||delete e[t],i(e,t,n))})(Function.prototype,s,function(){return"function"==typeof this&&this[o]||a.call(this)})},function(e,t,n){var r=n(12),i=n(13);e.exports=n(14)?function(e,t,n){return r.setDesc(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t){var n=Object;e.exports={create:n.create,getProto:n.getPrototypeOf,isEnum:{}.propertyIsEnumerable,getDesc:n.getOwnPropertyDescriptor,setDesc:n.defineProperty,setDescs:n.defineProperties,getKeys:n.keys,getNames:n.getOwnPropertyNames,getSymbols:n.getOwnPropertySymbols,each:[].forEach}},function(e,t){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},function(e,t,n){e.exports=!n(15)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(e,t){e.exports=function(e){try{return!!e()}catch(e){return!0}}},function(e,t){var n=e.exports={version:"1.2.6"};"number"==typeof __e&&(__e=n)},function(e,t,n){"use strict";var r=n(18)(!0);n(21)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){var r=n(19),i=n(20);e.exports=function(e){return function(t,n){var o,s,a=String(i(t)),u=r(n),c=a.length;return u<0||u>=c?e?"":void 0:(o=a.charCodeAt(u),o<55296||o>56319||u+1===c||(s=a.charCodeAt(u+1))<56320||s>57343?e?a.charAt(u):o:e?a.slice(u,u+2):(o-55296<<10)+(s-56320)+65536)}}},function(e,t){var n=Math.ceil,r=Math.floor;e.exports=function(e){return isNaN(e=+e)?0:(e>0?r:n)(e)}},function(e,t){e.exports=function(e){if(void 0==e)throw TypeError("Can't call method on  "+e);return e}},function(e,t,n){"use strict";var r=n(22),i=n(23),o=n(10),s=n(11),a=n(26),u=n(27),c=n(28),l=n(29),h=n(12).getProto,p=n(6)("iterator"),f=!([].keys&&"next"in[].keys()),d="@@iterator",m="keys",g="values",x=function(){return this};e.exports=function(e,t,n,y,v,b,D){c(n,t,y);var w,E,k=function(e){if(!f&&e in F)return F[e];switch(e){case m:return function(){return new n(this,e)};case g:return function(){return new n(this,e)}}return function(){return new n(this,e)}},A=t+" Iterator",C=v==g,S=!1,F=e.prototype,T=F[p]||F[d]||v&&F[v],B=T||k(v);if(T){var N=h(B.call(new e));l(N,A,!0),!r&&a(F,d)&&s(N,p,x),C&&T.name!==g&&(S=!0,B=function(){return T.call(this)})}if(r&&!D||!f&&!S&&F[p]||s(F,p,B),u[t]=B,u[A]=x,v)if(w={values:C?B:k(g),keys:b?B:k(m),entries:C?k("entries"):B},D)for(E in w)E in F||o(F,E,w[E]);else i(i.P+i.F*(f||S),t,w);return w}},function(e,t){e.exports=!1},function(e,t,n){var r=n(8),i=n(16),o=n(11),s=n(10),a=n(24),u="prototype",c=function(e,t,n){var l,h,p,f,d=e&c.F,m=e&c.G,g=e&c.S,x=e&c.P,y=e&c.B,v=m?r:g?r[t]||(r[t]={}):(r[t]||{})[u],b=m?i:i[t]||(i[t]={}),D=b[u]||(b[u]={});m&&(n=t);for(l in n)h=!d&&v&&l in v,p=(h?v:n)[l],f=y&&h?a(p,r):x&&"function"==typeof p?a(Function.call,p):p,v&&!h&&s(v,l,p),b[l]!=p&&o(b,l,f),x&&D[l]!=p&&(D[l]=p)};r.core=i,c.F=1,c.G=2,c.S=4,c.P=8,c.B=16,c.W=32,e.exports=c},function(e,t,n){var r=n(25);e.exports=function(e,t,n){if(r(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}}},function(e,t){e.exports=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e}},function(e,t){var n={}.hasOwnProperty;e.exports=function(e,t){return n.call(e,t)}},function(e,t){e.exports={}},function(e,t,n){"use strict";var r=n(12),i=n(13),o=n(29),s={};n(11)(s,n(6)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=r.create(s,{next:i(1,n)}),o(e,t+" Iterator")}},function(e,t,n){var r=n(12).setDesc,i=n(26),o=n(6)("toStringTag");e.exports=function(e,t,n){e&&!i(e=n?e:e.prototype,o)&&r(e,o,{configurable:!0,value:t})}},function(e,t,n){n(31);var r=n(8),i=n(11),o=n(27),s=n(6)("iterator"),a=r.NodeList,u=r.HTMLCollection,c=a&&a.prototype,l=u&&u.prototype,h=o.NodeList=o.HTMLCollection=o.Array;c&&!c[s]&&i(c,s,h),l&&!l[s]&&i(l,s,h)},function(e,t,n){"use strict";var r=n(32),i=n(33),o=n(27),s=n(34);e.exports=n(21)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,i(1)):"keys"==t?i(0,n):"values"==t?i(0,e[n]):i(0,[n,e[n]])},"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(e,t,n){var r=n(6)("unscopables"),i=Array.prototype;void 0==i[r]&&n(11)(i,r,{}),e.exports=function(e){i[r][e]=!0}},function(e,t){e.exports=function(e,t){return{value:t,done:!!e}}},function(e,t,n){var r=n(35),i=n(20);e.exports=function(e){return r(i(e))}},function(e,t,n){var r=n(5);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},function(e,t,n){"use strict";var r,i=n(12),o=n(22),s=n(8),a=n(24),u=n(4),c=n(23),l=n(37),h=n(38),p=n(25),f=n(39),d=n(40),m=n(45).set,g=n(46),x=n(6)("species"),y=n(47),v=n(48),b="Promise",D=s.process,w="process"==u(D),E=s[b],k=function(){},A=function(e){var t,n=new E(k);return e&&(n.constructor=function(e){e(k,k)}),(t=E.resolve(n)).catch(k),t===n},C=function(){function e(t){var n=new E(t);return m(n,e.prototype),n}var t=!1;try{if(t=E&&E.resolve&&A(),m(e,E),e.prototype=i.create(E.prototype,{constructor:{value:e}}),e.resolve(5).then(function(){})instanceof e||(t=!1),t&&n(14)){var r=!1;E.resolve(i.setDesc({},"then",{get:function(){r=!0}})),t=r}}catch(e){t=!1}return t}(),S=function(e,t){return!(!o||e!==E||t!==r)||g(e,t)},F=function(e){var t=h(e)[x];return void 0!=t?t:e},T=function(e){var t;return!(!l(e)||"function"!=typeof(t=e.then))&&t},B=function(e){var t,n;this.promise=new e(function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r}),this.resolve=p(t),this.reject=p(n)},N=function(e){try{e()}catch(e){return{error:e}}},I=function(e,t){if(!e.n){e.n=!0;var n=e.c;v(function(){for(var r=e.v,i=1==e.s,o=0,a=function(t){var n,o,s=i?t.ok:t.fail,a=t.resolve,u=t.reject;try{s?(i||(e.h=!0),n=s===!0?r:s(r),n===t.promise?u(TypeError("Promise-chain cycle")):(o=T(n))?o.call(n,a,u):a(n)):u(r)}catch(e){u(e)}};n.length>o;)a(n[o++]);n.length=0,e.n=!1,t&&setTimeout(function(){var t,n,i=e.p;P(i)&&(w?D.emit("unhandledRejection",r,i):(t=s.onunhandledrejection)?t({promise:i,reason:r}):(n=s.console)&&n.error&&n.error("Unhandled promise rejection",r)),e.a=void 0},1)})}},P=function(e){var t,n=e._d,r=n.a||n.c,i=0;if(n.h)return!1;for(;r.length>i;)if(t=r[i++],t.fail||!P(t.promise))return!1;return!0},M=function(e){var t=this;t.d||(t.d=!0,t=t.r||t,t.v=e,t.s=2,t.a=t.c.slice(),I(t,!0))},O=function(e){var t,n=this;if(!n.d){n.d=!0,n=n.r||n;try{if(n.p===e)throw TypeError("Promise can't be resolved itself");(t=T(e))?v(function(){var r={r:n,d:!1};try{t.call(e,a(O,r,1),a(M,r,1))}catch(e){M.call(r,e)}}):(n.v=e,n.s=1,I(n,!1))}catch(e){M.call({r:n,d:!1},e)}}};C||(E=function(e){p(e);var t=this._d={p:f(this,E,b),c:[],a:void 0,s:0,d:!1,v:void 0,h:!1,n:!1};try{e(a(O,t,1),a(M,t,1))}catch(e){M.call(t,e)}},n(53)(E.prototype,{then:function(e,t){var n=new B(y(this,E)),r=n.promise,i=this._d;return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,i.c.push(n),i.a&&i.a.push(n),i.s&&I(i,!1),r},catch:function(e){return this.then(void 0,e)}})),c(c.G+c.W+c.F*!C,{Promise:E}),n(29)(E,b),n(54)(b),r=n(16)[b],c(c.S+c.F*!C,b,{reject:function(e){var t=new B(this),n=t.reject;return n(e),t.promise}}),c(c.S+c.F*(!C||A(!0)),b,{resolve:function(e){if(e instanceof E&&S(e.constructor,this))return e;var t=new B(this),n=t.resolve;return n(e),t.promise}}),c(c.S+c.F*!(C&&n(55)(function(e){E.all(e).catch(function(){})})),b,{all:function(e){var t=F(this),n=new B(t),r=n.resolve,o=n.reject,s=[],a=N(function(){d(e,!1,s.push,s);var n=s.length,a=Array(n);n?i.each.call(s,function(e,i){var s=!1;t.resolve(e).then(function(e){s||(s=!0,a[i]=e,--n||r(a))},o)}):r(a)});return a&&o(a.error),n.promise},race:function(e){var t=F(this),n=new B(t),r=n.reject,i=N(function(){d(e,!1,function(e){t.resolve(e).then(n.resolve,r)})});return i&&r(i.error),n.promise}})},function(e,t){e.exports=function(e){return"object"==typeof e?null!==e:"function"==typeof e}},function(e,t,n){var r=n(37);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},function(e,t){e.exports=function(e,t,n){if(!(e instanceof t))throw TypeError(n+": use the 'new' operator!");return e}},function(e,t,n){var r=n(24),i=n(41),o=n(42),s=n(38),a=n(43),u=n(44);e.exports=function(e,t,n,c){var l,h,p,f=u(e),d=r(n,c,t?2:1),m=0;if("function"!=typeof f)throw TypeError(e+" is not iterable!");if(o(f))for(l=a(e.length);l>m;m++)t?d(s(h=e[m])[0],h[1]):d(e[m]);else for(p=f.call(e);!(h=p.next()).done;)i(p,d,h.value,t)}},function(e,t,n){var r=n(38);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(t){var o=e.return;throw void 0!==o&&r(o.call(e)),t}}},function(e,t,n){var r=n(27),i=n(6)("iterator"),o=Array.prototype;e.exports=function(e){return void 0!==e&&(r.Array===e||o[i]===e)}},function(e,t,n){var r=n(19),i=Math.min;e.exports=function(e){return e>0?i(r(e),9007199254740991):0}},function(e,t,n){var r=n(4),i=n(6)("iterator"),o=n(27);e.exports=n(16).getIteratorMethod=function(e){if(void 0!=e)return e[i]||e["@@iterator"]||o[r(e)]}},function(e,t,n){var r=n(12).getDesc,i=n(37),o=n(38),s=function(e,t){if(o(e),!i(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,i){try{i=n(24)(Function.call,r(Object.prototype,"__proto__").set,2),i(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,n){return s(e,n),t?e.__proto__=n:i(e,n),e}}({},!1):void 0),check:s}},function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e===1/t:e!=e&&t!=t}},function(e,t,n){var r=n(38),i=n(25),o=n(6)("species");e.exports=function(e,t){var n,s=r(e).constructor;return void 0===s||void 0==(n=r(s)[o])?t:i(n)}},function(e,t,n){var r,i,o,s=n(8),a=n(49).set,u=s.MutationObserver||s.WebKitMutationObserver,c=s.process,l=s.Promise,h="process"==n(5)(c),p=function(){var e,t,n;for(h&&(e=c.domain)&&(c.domain=null,e.exit());r;)t=r.domain,n=r.fn,t&&t.enter(),n(),t&&t.exit(),r=r.next;i=void 0,e&&e.enter()};if(h)o=function(){c.nextTick(p)};else if(u){var f=1,d=document.createTextNode("");new u(p).observe(d,{characterData:!0}),o=function(){d.data=f=-f}}else o=l&&l.resolve?function(){l.resolve().then(p)}:function(){a.call(s,p)};e.exports=function(e){var t={fn:e,next:void 0,domain:h&&c.domain};i&&(i.next=t),r||(r=t,o()),i=t}},function(e,t,n){var r,i,o,s=n(24),a=n(50),u=n(51),c=n(52),l=n(8),h=l.process,p=l.setImmediate,f=l.clearImmediate,d=l.MessageChannel,m=0,g={},x="onreadystatechange",y=function(){var e=+this;if(g.hasOwnProperty(e)){var t=g[e];delete g[e],t()}},v=function(e){y.call(e.data)};p&&f||(p=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return g[++m]=function(){a("function"==typeof e?e:Function(e),t)},r(m),m},f=function(e){delete g[e]},"process"==n(5)(h)?r=function(e){h.nextTick(s(y,e,1))}:d?(i=new d,o=i.port2,i.port1.onmessage=v,r=s(o.postMessage,o,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(r=function(e){l.postMessage(e+"","*")},l.addEventListener("message",v,!1)):r=x in c("script")?function(e){u.appendChild(c("script"))[x]=function(){u.removeChild(this),y.call(e)}}:function(e){setTimeout(s(y,e,1),0)}),e.exports={set:p,clear:f}},function(e,t){e.exports=function(e,t,n){var r=void 0===n;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)}},function(e,t,n){e.exports=n(8).document&&document.documentElement},function(e,t,n){var r=n(37),i=n(8).document,o=r(i)&&r(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,t,n){var r=n(10);e.exports=function(e,t){for(var n in t)r(e,n,t[n]);return e}},function(e,t,n){"use strict";var r=n(8),i=n(12),o=n(14),s=n(6)("species");e.exports=function(e){var t=r[e];o&&t&&!t[s]&&i.setDesc(t,s,{configurable:!0,get:function(){return this}})}},function(e,t,n){var r=n(6)("iterator"),i=!1;try{var o=[7][r]();o.return=function(){i=!0},Array.from(o,function(){throw 2})}catch(e){}e.exports=function(e,t){if(!t&&!i)return!1;var n=!1;try{var o=[7],s=o[r]();s.next=function(){return{done:n=!0}},o[r]=function(){return s},e(o)}catch(e){}return n}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}var i=n(57),o=r(i);n(174);var s=n(178),a=r(s),u=n(181),c=r(u),l=[a.default,c.default,function(){return{components:{StandaloneLayout:o.default}}}];e.exports=l},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var i=n(58),o=r(i),s=n(84),a=r(s),u=n(85),c=r(u),l=n(89),h=r(l),p=n(136),f=r(p),d=n(144),m=r(d),g=function(e){function t(){return(0,a.default)(this,t),(0,h.default)(this,(t.__proto__||(0,o.default)(t)).apply(this,arguments))}return(0,f.default)(t,e),(0,c.default)(t,[{key:"render",value:function(){var e=this.props,t=e.getComponent,n=e.specSelectors,r=t("Container"),i=t("Row"),o=t("Col"),s=t("Topbar",!0),a=t("BaseLayout",!0),u=t("onlineValidatorBadge",!0),c=n.loadingStatus();return m.default.createElement(r,{className:"swagger-ui"},s?m.default.createElement(s,null):null,"loading"===c&&m.default.createElement("div",{className:"info"},m.default.createElement("h4",{className:"title"},"Loading...")),"failed"===c&&m.default.createElement("div",{className:"info"},m.default.createElement("h4",{className:"title"},"Failed to load spec.")),"failedConfig"===c&&m.default.createElement("div",{className:"info",style:{maxWidth:"880px",marginLeft:"auto",marginRight:"auto",textAlign:"center"}},m.default.createElement("h4",{className:"title"},"Failed to load config.")),!c||"success"===c&&m.default.createElement(a,null),m.default.createElement(i,null,m.default.createElement(o,null,m.default.createElement(u,null))))}}]),t}(m.default.Component);g.propTypes={errSelectors:d.PropTypes.object.isRequired,errActions:d.PropTypes.object.isRequired,specActions:d.PropTypes.object.isRequired,specSelectors:d.PropTypes.object.isRequired,layoutSelectors:d.PropTypes.object.isRequired,layoutActions:d.PropTypes.object.isRequired,getComponent:d.PropTypes.func.isRequired},t.default=g},function(e,t,n){e.exports={default:n(59),__esModule:!0}},function(e,t,n){n(60),e.exports=n(71).Object.getPrototypeOf},function(e,t,n){var r=n(61),i=n(63);n(69)("getPrototypeOf",function(){return function(e){return i(r(e))}})},function(e,t,n){var r=n(62);e.exports=function(e){return Object(r(e))}},20,function(e,t,n){var r=n(64),i=n(61),o=n(65)("IE_PROTO"),s=Object.prototype;e.exports=Object.getPrototypeOf||function(e){return e=i(e),r(e,o)?e[o]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?s:null}},26,function(e,t,n){var r=n(66)("keys"),i=n(68);e.exports=function(e){return r[e]||(r[e]=i(e))}},function(e,t,n){var r=n(67),i="__core-js_shared__",o=r[i]||(r[i]={});e.exports=function(e){return o[e]||(o[e]={})}},8,9,function(e,t,n){var r=n(70),i=n(71),o=n(80);e.exports=function(e,t){var n=(i.Object||{})[e]||Object[e],s={};s[e]=t(n),r(r.S+r.F*o(function(){n(1)}),"Object",s)}},function(e,t,n){var r=n(67),i=n(71),o=n(72),s=n(74),a="prototype",u=function(e,t,n){var c,l,h,p=e&u.F,f=e&u.G,d=e&u.S,m=e&u.P,g=e&u.B,x=e&u.W,y=f?i:i[t]||(i[t]={}),v=y[a],b=f?r:d?r[t]:(r[t]||{})[a];f&&(n=t);for(c in n)l=!p&&b&&void 0!==b[c],l&&c in y||(h=l?b[c]:n[c],y[c]=f&&"function"!=typeof b[c]?n[c]:g&&l?o(h,r):x&&b[c]==h?function(e){var t=function(t,n,r){if(this instanceof e){switch(arguments.length){case 0:return new e;case 1:return new e(t);case 2:return new e(t,n)}return new e(t,n,r)}return e.apply(this,arguments)};return t[a]=e[a],t}(h):m&&"function"==typeof h?o(Function.call,h):h,m&&((y.virtual||(y.virtual={}))[c]=h,e&u.R&&v&&!v[c]&&s(v,c,h)))};u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,u.U=64,u.R=128,e.exports=u},function(e,t){var n=e.exports={version:"2.4.0"};"number"==typeof __e&&(__e=n)},function(e,t,n){var r=n(73);e.exports=function(e,t,n){if(r(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}}},25,function(e,t,n){var r=n(75),i=n(83);e.exports=n(79)?function(e,t,n){return r.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},function(e,t,n){var r=n(76),i=n(78),o=n(82),s=Object.defineProperty;t.f=n(79)?Object.defineProperty:function(e,t,n){if(r(e),t=o(t,!0),r(n),i)try{return s(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},function(e,t,n){var r=n(77);e.exports=function(e){if(!r(e))throw TypeError(e+" is not an object!");return e}},37,function(e,t,n){e.exports=!n(79)&&!n(80)(function(){return 7!=Object.defineProperty(n(81)("div"),"a",{get:function(){return 7}}).a})},function(e,t,n){e.exports=!n(80)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},15,function(e,t,n){var r=n(77),i=n(67).document,o=r(i)&&r(i.createElement);e.exports=function(e){return o?i.createElement(e):{}}},function(e,t,n){var r=n(77);e.exports=function(e,t){if(!r(e))return e;var n,i;if(t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;if("function"==typeof(n=e.valueOf)&&!r(i=n.call(e)))return i;if(!t&&"function"==typeof(n=e.toString)&&!r(i=n.call(e)))return i;throw TypeError("Can't convert object to primitive value")}},13,function(e,t){"use strict";t.__esModule=!0,t.default=function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var i=n(86),o=r(i);t.default=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),(0,o.default)(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}()},function(e,t,n){e.exports={default:n(87),__esModule:!0}},function(e,t,n){n(88);var r=n(71).Object;e.exports=function(e,t,n){return r.defineProperty(e,t,n)}},function(e,t,n){var r=n(70);r(r.S+r.F*!n(79),"Object",{defineProperty:n(75).f})},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var i=n(90),o=r(i);t.default=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!==("undefined"==typeof t?"undefined":(0,o.default)(t))&&"function"!=typeof t?e:t}},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var i=n(91),o=r(i),s=n(120),a=r(s),u="function"==typeof a.default&&"symbol"==typeof o.default?function(e){return typeof e}:function(e){return e&&"function"==typeof a.default&&e.constructor===a.default&&e!==a.default.prototype?"symbol":typeof e};t.default="function"==typeof a.default&&"symbol"===u(o.default)?function(e){return"undefined"==typeof e?"undefined":u(e)}:function(e){return e&&"function"==typeof a.default&&e.constructor===a.default&&e!==a.default.prototype?"symbol":"undefined"==typeof e?"undefined":u(e)}},function(e,t,n){e.exports={default:n(92),__esModule:!0}},function(e,t,n){n(93),n(115),e.exports=n(119).f("iterator")},function(e,t,n){"use strict";var r=n(94)(!0);n(96)(String,"String",function(e){this._t=String(e),this._i=0},function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=r(t,n),this._i+=e.length,{value:e,done:!1})})},function(e,t,n){var r=n(95),i=n(62);e.exports=function(e){return function(t,n){var o,s,a=String(i(t)),u=r(n),c=a.length;return u<0||u>=c?e?"":void 0:(o=a.charCodeAt(u),o<55296||o>56319||u+1===c||(s=a.charCodeAt(u+1))<56320||s>57343?e?a.charAt(u):o:e?a.slice(u,u+2):(o-55296<<10)+(s-56320)+65536)}}},19,function(e,t,n){"use strict";var r=n(97),i=n(70),o=n(98),s=n(74),a=n(64),u=n(99),c=n(100),l=n(113),h=n(63),p=n(114)("iterator"),f=!([].keys&&"next"in[].keys()),d="@@iterator",m="keys",g="values",x=function(){return this};e.exports=function(e,t,n,y,v,b,D){c(n,t,y);var w,E,k,A=function(e){if(!f&&e in T)return T[e];switch(e){case m:return function(){return new n(this,e)};case g:return function(){return new n(this,e)}}return function(){return new n(this,e)}},C=t+" Iterator",S=v==g,F=!1,T=e.prototype,B=T[p]||T[d]||v&&T[v],N=B||A(v),I=v?S?A("entries"):N:void 0,P="Array"==t?T.entries||B:B;if(P&&(k=h(P.call(new e)),k!==Object.prototype&&(l(k,C,!0),r||a(k,p)||s(k,p,x))),S&&B&&B.name!==g&&(F=!0,N=function(){return B.call(this)}),r&&!D||!f&&!F&&T[p]||s(T,p,N),u[t]=N,u[C]=x,v)if(w={values:S?N:A(g),keys:b?N:A(m),entries:I},D)for(E in w)E in T||o(T,E,w[E]);else i(i.P+i.F*(f||F),t,w);return w}},function(e,t){e.exports=!0},function(e,t,n){e.exports=n(74)},27,function(e,t,n){"use strict";var r=n(101),i=n(83),o=n(113),s={};n(74)(s,n(114)("iterator"),function(){return this}),e.exports=function(e,t,n){e.prototype=r(s,{next:i(1,n)}),o(e,t+" Iterator")}},function(e,t,n){var r=n(76),i=n(102),o=n(111),s=n(65)("IE_PROTO"),a=function(){},u="prototype",c=function(){var e,t=n(81)("iframe"),r=o.length,i="<",s=">";for(t.style.display="none",n(112).appendChild(t),t.src="javascript:",e=t.contentWindow.document,e.open(),e.write(i+"script"+s+"document.F=Object"+i+"/script"+s),e.close(),c=e.F;r--;)delete c[u][o[r]];return c()};e.exports=Object.create||function(e,t){var n;return null!==e?(a[u]=r(e),n=new a,a[u]=null,n[s]=e):n=c(),void 0===t?n:i(n,t)}},function(e,t,n){var r=n(75),i=n(76),o=n(103);e.exports=n(79)?Object.defineProperties:function(e,t){i(e);for(var n,s=o(t),a=s.length,u=0;a>u;)r.f(e,n=s[u++],t[n]);return e}},function(e,t,n){var r=n(104),i=n(111);e.exports=Object.keys||function(e){return r(e,i)}},function(e,t,n){var r=n(64),i=n(105),o=n(108)(!1),s=n(65)("IE_PROTO");e.exports=function(e,t){var n,a=i(e),u=0,c=[];for(n in a)n!=s&&r(a,n)&&c.push(n);for(;t.length>u;)r(a,n=t[u++])&&(~o(c,n)||c.push(n));return c}},function(e,t,n){var r=n(106),i=n(62);e.exports=function(e){return r(i(e))}},function(e,t,n){var r=n(107);e.exports=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==r(e)?e.split(""):Object(e)}},5,function(e,t,n){var r=n(105),i=n(109),o=n(110);e.exports=function(e){return function(t,n,s){var a,u=r(t),c=i(u.length),l=o(s,c);if(e&&n!=n){for(;c>l;)if(a=u[l++],a!=a)return!0}else for(;c>l;l++)if((e||l in u)&&u[l]===n)return e||l||0;return!e&&-1}}},function(e,t,n){var r=n(95),i=Math.min;e.exports=function(e){return e>0?i(r(e),9007199254740991):0}},function(e,t,n){var r=n(95),i=Math.max,o=Math.min;e.exports=function(e,t){return e=r(e),e<0?i(e+t,0):o(e,t)}},function(e,t){e.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(e,t,n){e.exports=n(67).document&&document.documentElement},function(e,t,n){var r=n(75).f,i=n(64),o=n(114)("toStringTag");e.exports=function(e,t,n){e&&!i(e=n?e:e.prototype,o)&&r(e,o,{configurable:!0,value:t})}},function(e,t,n){var r=n(66)("wks"),i=n(68),o=n(67).Symbol,s="function"==typeof o,a=e.exports=function(e){return r[e]||(r[e]=s&&o[e]||(s?o:i)("Symbol."+e))};a.store=r},function(e,t,n){n(116);for(var r=n(67),i=n(74),o=n(99),s=n(114)("toStringTag"),a=["NodeList","DOMTokenList","MediaList","StyleSheetList","CSSRuleList"],u=0;u<5;u++){var c=a[u],l=r[c],h=l&&l.prototype;h&&!h[s]&&i(h,s,c),o[c]=o.Array}},function(e,t,n){"use strict";var r=n(117),i=n(118),o=n(99),s=n(105);e.exports=n(96)(Array,"Array",function(e,t){this._t=s(e),this._i=0,this._k=t},function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,i(1)):"keys"==t?i(0,n):"values"==t?i(0,e[n]):i(0,[n,e[n]])},"values"),o.Arguments=o.Array,r("keys"),r("values"),r("entries")},function(e,t){e.exports=function(){}},33,function(e,t,n){t.f=n(114)},function(e,t,n){e.exports={default:n(121),__esModule:!0}},function(e,t,n){n(122),n(133),n(134),n(135),e.exports=n(71).Symbol},function(e,t,n){"use strict";var r=n(67),i=n(64),o=n(79),s=n(70),a=n(98),u=n(123).KEY,c=n(80),l=n(66),h=n(113),p=n(68),f=n(114),d=n(119),m=n(124),g=n(125),x=n(126),y=n(129),v=n(76),b=n(105),D=n(82),w=n(83),E=n(101),k=n(130),A=n(132),C=n(75),S=n(103),F=A.f,T=C.f,B=k.f,N=r.Symbol,I=r.JSON,P=I&&I.stringify,M="prototype",O=f("_hidden"),_=f("toPrimitive"),L={}.propertyIsEnumerable,R=l("symbol-registry"),U=l("symbols"),j=l("op-symbols"),z=Object[M],J="function"==typeof N,X=r.QObject,Y=!X||!X[M]||!X[M].findChild,K=o&&c(function(){return 7!=E(T({},"a",{get:function(){return T(this,"a",{value:7}).a}})).a})?function(e,t,n){var r=F(z,t);r&&delete z[t],T(e,t,n),r&&e!==z&&T(z,t,r)}:T,W=function(e){var t=U[e]=E(N[M]);return t._k=e,t},H=J&&"symbol"==typeof N.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof N},q=function(e,t,n){return e===z&&q(j,t,n),v(e),t=D(t,!0),v(n),i(U,t)?(n.enumerable?(i(e,O)&&e[O][t]&&(e[O][t]=!1),n=E(n,{enumerable:w(0,!1)})):(i(e,O)||T(e,O,w(1,{})),e[O][t]=!0),K(e,t,n)):T(e,t,n)},G=function(e,t){v(e);for(var n,r=x(t=b(t)),i=0,o=r.length;o>i;)q(e,n=r[i++],t[n]);return e},V=function(e,t){return void 0===t?E(e):G(E(e),t)},$=function(e){var t=L.call(this,e=D(e,!0));return!(this===z&&i(U,e)&&!i(j,e))&&(!(t||!i(this,e)||!i(U,e)||i(this,O)&&this[O][e])||t)},Z=function(e,t){if(e=b(e),t=D(t,!0),e!==z||!i(U,t)||i(j,t)){var n=F(e,t);return!n||!i(U,t)||i(e,O)&&e[O][t]||(n.enumerable=!0),n}},Q=function(e){for(var t,n=B(b(e)),r=[],o=0;n.length>o;)i(U,t=n[o++])||t==O||t==u||r.push(t);return r},ee=function(e){for(var t,n=e===z,r=B(n?j:b(e)),o=[],s=0;r.length>s;)!i(U,t=r[s++])||n&&!i(z,t)||o.push(U[t]);return o};J||(N=function(){if(this instanceof N)throw TypeError("Symbol is not a constructor!");var e=p(arguments.length>0?arguments[0]:void 0),t=function(n){this===z&&t.call(j,n),i(this,O)&&i(this[O],e)&&(this[O][e]=!1),K(this,e,w(1,n))};return o&&Y&&K(z,e,{configurable:!0,set:t}),W(e)},a(N[M],"toString",function(){return this._k}),A.f=Z,C.f=q,n(131).f=k.f=Q,n(128).f=$,n(127).f=ee,o&&!n(97)&&a(z,"propertyIsEnumerable",$,!0),d.f=function(e){return W(f(e))}),s(s.G+s.W+s.F*!J,{Symbol:N});for(var te="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),ne=0;te.length>ne;)f(te[ne++]);for(var te=S(f.store),ne=0;te.length>ne;)m(te[ne++]);s(s.S+s.F*!J,"Symbol",{for:function(e){return i(R,e+="")?R[e]:R[e]=N(e)},keyFor:function(e){if(H(e))return g(R,e);throw TypeError(e+" is not a symbol!")},useSetter:function(){Y=!0},useSimple:function(){Y=!1}}),s(s.S+s.F*!J,"Object",{create:V,defineProperty:q,defineProperties:G,getOwnPropertyDescriptor:Z,getOwnPropertyNames:Q,getOwnPropertySymbols:ee}),I&&s(s.S+s.F*(!J||c(function(){var e=N();return"[null]"!=P([e])||"{}"!=P({a:e})||"{}"!=P(Object(e))})),"JSON",{stringify:function(e){if(void 0!==e&&!H(e)){for(var t,n,r=[e],i=1;arguments.length>i;)r.push(arguments[i++]);return t=r[1],"function"==typeof t&&(n=t),!n&&y(t)||(t=function(e,t){if(n&&(t=n.call(this,e,t)),!H(t))return t}),r[1]=t,P.apply(I,r)}}}),N[M][_]||n(74)(N[M],_,N[M].valueOf),h(N,"Symbol"),h(Math,"Math",!0),h(r.JSON,"JSON",!0)},function(e,t,n){var r=n(68)("meta"),i=n(77),o=n(64),s=n(75).f,a=0,u=Object.isExtensible||function(){return!0},c=!n(80)(function(){return u(Object.preventExtensions({}))}),l=function(e){s(e,r,{value:{i:"O"+ ++a,w:{}}})},h=function(e,t){if(!i(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!o(e,r)){if(!u(e))return"F";if(!t)return"E";l(e)}return e[r].i},p=function(e,t){if(!o(e,r)){if(!u(e))return!0;if(!t)return!1;l(e)}return e[r].w},f=function(e){return c&&d.NEED&&u(e)&&!o(e,r)&&l(e),e},d=e.exports={KEY:r,NEED:!1,fastKey:h,getWeak:p,onFreeze:f}},function(e,t,n){var r=n(67),i=n(71),o=n(97),s=n(119),a=n(75).f;e.exports=function(e){var t=i.Symbol||(i.Symbol=o?{}:r.Symbol||{});"_"==e.charAt(0)||e in t||a(t,e,{value:s.f(e)})}},function(e,t,n){var r=n(103),i=n(105);e.exports=function(e,t){for(var n,o=i(e),s=r(o),a=s.length,u=0;a>u;)if(o[n=s[u++]]===t)return n}},function(e,t,n){var r=n(103),i=n(127),o=n(128);e.exports=function(e){var t=r(e),n=i.f;if(n)for(var s,a=n(e),u=o.f,c=0;a.length>c;)u.call(e,s=a[c++])&&t.push(s);return t}},function(e,t){t.f=Object.getOwnPropertySymbols},function(e,t){t.f={}.propertyIsEnumerable},function(e,t,n){var r=n(107);e.exports=Array.isArray||function(e){return"Array"==r(e)}},function(e,t,n){var r=n(105),i=n(131).f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(e){try{return i(e)}catch(e){return s.slice()}};e.exports.f=function(e){return s&&"[object Window]"==o.call(e)?a(e):i(r(e))}},function(e,t,n){var r=n(104),i=n(111).concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},function(e,t,n){var r=n(128),i=n(83),o=n(105),s=n(82),a=n(64),u=n(78),c=Object.getOwnPropertyDescriptor;t.f=n(79)?c:function(e,t){if(e=o(e),t=s(t,!0),u)try{return c(e,t)}catch(e){}if(a(e,t))return i(!r.f.call(e,t),e[t])}},function(e,t){},function(e,t,n){n(124)("asyncIterator")},function(e,t,n){n(124)("observable")},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}t.__esModule=!0;var i=n(137),o=r(i),s=n(141),a=r(s),u=n(90),c=r(u);t.default=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+("undefined"==typeof t?"undefined":(0,c.default)(t)));e.prototype=(0,a.default)(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0
}}),t&&(o.default?(0,o.default)(e,t):e.__proto__=t)}},function(e,t,n){e.exports={default:n(138),__esModule:!0}},function(e,t,n){n(139),e.exports=n(71).Object.setPrototypeOf},function(e,t,n){var r=n(70);r(r.S,"Object",{setPrototypeOf:n(140).set})},function(e,t,n){var r=n(77),i=n(76),o=function(e,t){if(i(e),!r(t)&&null!==t)throw TypeError(t+": can't set as prototype!")};e.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,r){try{r=n(72)(Function.call,n(132).f(Object.prototype,"__proto__").set,2),r(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,n){return o(e,n),t?e.__proto__=n:r(e,n),e}}({},!1):void 0),check:o}},function(e,t,n){e.exports={default:n(142),__esModule:!0}},function(e,t,n){n(143);var r=n(71).Object;e.exports=function(e,t){return r.create(e,t)}},function(e,t,n){var r=n(70);r(r.S,"Object",{create:n(101)})},function(e,t,n){"use strict";e.exports=n(145)},function(e,t,n){"use strict";var r=n(146),i=n(147),o=n(156),s=n(164),a=n(158),u=n(165),c=n(170),l=n(171),h=n(173),p=a.createElement,f=a.createFactory,d=a.cloneElement,m=r,g=function(e){return e},x={Children:{map:o.map,forEach:o.forEach,count:o.count,toArray:o.toArray,only:h},Component:i.Component,PureComponent:i.PureComponent,createElement:p,cloneElement:d,isValidElement:a.isValidElement,PropTypes:u,createClass:l,createFactory:f,createMixin:g,DOM:s,version:c,__spread:m};e.exports=x},function(e,t){/*
	object-assign
	(c) Sindre Sorhus
	@license MIT
	*/
"use strict";function n(e){if(null===e||void 0===e)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(e)}function r(){try{if(!Object.assign)return!1;var e=new String("abc");if(e[5]="de","5"===Object.getOwnPropertyNames(e)[0])return!1;for(var t={},n=0;n<10;n++)t["_"+String.fromCharCode(n)]=n;var r=Object.getOwnPropertyNames(t).map(function(e){return t[e]});if("**********"!==r.join(""))return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach(function(e){i[e]=e}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join("")}catch(e){return!1}}var i=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,s=Object.prototype.propertyIsEnumerable;e.exports=r()?Object.assign:function(e,t){for(var r,a,u=n(e),c=1;c<arguments.length;c++){r=Object(arguments[c]);for(var l in r)o.call(r,l)&&(u[l]=r[l]);if(i){a=i(r);for(var h=0;h<a.length;h++)s.call(r,a[h])&&(u[a[h]]=r[a[h]])}}return u}},function(e,t,n){"use strict";function r(e,t,n){this.props=e,this.context=t,this.refs=c,this.updater=n||u}function i(e,t,n){this.props=e,this.context=t,this.refs=c,this.updater=n||u}function o(){}var s=n(148),a=n(146),u=n(149),c=(n(152),n(153));n(154),n(155);r.prototype.isReactComponent={},r.prototype.setState=function(e,t){"object"!=typeof e&&"function"!=typeof e&&null!=e?s("85"):void 0,this.updater.enqueueSetState(this,e),t&&this.updater.enqueueCallback(this,t,"setState")},r.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this),e&&this.updater.enqueueCallback(this,e,"forceUpdate")};o.prototype=r.prototype,i.prototype=new o,i.prototype.constructor=i,a(i.prototype,r.prototype),i.prototype.isPureReactComponent=!0,e.exports={Component:r,PureComponent:i}},function(e,t){"use strict";function n(e){for(var t=arguments.length-1,n="Minified React error #"+e+"; visit http://facebook.github.io/react/docs/error-decoder.html?invariant="+e,r=0;r<t;r++)n+="&args[]="+encodeURIComponent(arguments[r+1]);n+=" for the full message or use the non-minified dev environment for full errors and additional helpful warnings.";var i=new Error(n);throw i.name="Invariant Violation",i.framesToPop=1,i}e.exports=n},function(e,t,n){"use strict";function r(e,t){}var i=(n(150),{isMounted:function(e){return!1},enqueueCallback:function(e,t){},enqueueForceUpdate:function(e){r(e,"forceUpdate")},enqueueReplaceState:function(e,t){r(e,"replaceState")},enqueueSetState:function(e,t){r(e,"setState")}});e.exports=i},function(e,t,n){"use strict";var r=n(151),i=r;e.exports=i},function(e,t){"use strict";function n(e){return function(){return e}}var r=function(){};r.thatReturns=n,r.thatReturnsFalse=n(!1),r.thatReturnsTrue=n(!0),r.thatReturnsNull=n(null),r.thatReturnsThis=function(){return this},r.thatReturnsArgument=function(e){return e},e.exports=r},function(e,t,n){"use strict";var r=!1;e.exports=r},function(e,t,n){"use strict";var r={};e.exports=r},function(e,t,n){"use strict";function r(e,t,n,r,o,s,a,u){if(i(t),!e){var c;if(void 0===t)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var l=[n,r,o,s,a,u],h=0;c=new Error(t.replace(/%s/g,function(){return l[h++]})),c.name="Invariant Violation"}throw c.framesToPop=1,c}}var i=function(e){};e.exports=r},function(e,t,n){"use strict";var r=function(){};e.exports=r},function(e,t,n){"use strict";function r(e){return(""+e).replace(b,"$&/")}function i(e,t){this.func=e,this.context=t,this.count=0}function o(e,t,n){var r=e.func,i=e.context;r.call(i,t,e.count++)}function s(e,t,n){if(null==e)return e;var r=i.getPooled(t,n);x(e,o,r),i.release(r)}function a(e,t,n,r){this.result=e,this.keyPrefix=t,this.func=n,this.context=r,this.count=0}function u(e,t,n){var i=e.result,o=e.keyPrefix,s=e.func,a=e.context,u=s.call(a,t,e.count++);Array.isArray(u)?c(u,i,n,g.thatReturnsArgument):null!=u&&(m.isValidElement(u)&&(u=m.cloneAndReplaceKey(u,o+(!u.key||t&&t.key===u.key?"":r(u.key)+"/")+n)),i.push(u))}function c(e,t,n,i,o){var s="";null!=n&&(s=r(n)+"/");var c=a.getPooled(t,s,i,o);x(e,u,c),a.release(c)}function l(e,t,n){if(null==e)return e;var r=[];return c(e,r,null,t,n),r}function h(e,t,n){return null}function p(e,t){return x(e,h,null)}function f(e){var t=[];return c(e,t,null,g.thatReturnsArgument),t}var d=n(157),m=n(158),g=n(151),x=n(161),y=d.twoArgumentPooler,v=d.fourArgumentPooler,b=/\/+/g;i.prototype.destructor=function(){this.func=null,this.context=null,this.count=0},d.addPoolingTo(i,y),a.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},d.addPoolingTo(a,v);var D={forEach:s,map:l,mapIntoWithKeyPrefixInternal:c,count:p,toArray:f};e.exports=D},function(e,t,n){"use strict";var r=n(148),i=(n(154),function(e){var t=this;if(t.instancePool.length){var n=t.instancePool.pop();return t.call(n,e),n}return new t(e)}),o=function(e,t){var n=this;if(n.instancePool.length){var r=n.instancePool.pop();return n.call(r,e,t),r}return new n(e,t)},s=function(e,t,n){var r=this;if(r.instancePool.length){var i=r.instancePool.pop();return r.call(i,e,t,n),i}return new r(e,t,n)},a=function(e,t,n,r){var i=this;if(i.instancePool.length){var o=i.instancePool.pop();return i.call(o,e,t,n,r),o}return new i(e,t,n,r)},u=function(e){var t=this;e instanceof t?void 0:r("25"),e.destructor(),t.instancePool.length<t.poolSize&&t.instancePool.push(e)},c=10,l=i,h=function(e,t){var n=e;return n.instancePool=[],n.getPooled=t||l,n.poolSize||(n.poolSize=c),n.release=u,n},p={addPoolingTo:h,oneArgumentPooler:i,twoArgumentPooler:o,threeArgumentPooler:s,fourArgumentPooler:a};e.exports=p},function(e,t,n){"use strict";function r(e){return void 0!==e.ref}function i(e){return void 0!==e.key}var o=n(146),s=n(159),a=(n(150),n(152),Object.prototype.hasOwnProperty),u=n(160),c={key:!0,ref:!0,__self:!0,__source:!0},l=function(e,t,n,r,i,o,s){var a={$$typeof:u,type:e,key:t,ref:n,props:s,_owner:o};return a};l.createElement=function(e,t,n){var o,u={},h=null,p=null,f=null,d=null;if(null!=t){r(t)&&(p=t.ref),i(t)&&(h=""+t.key),f=void 0===t.__self?null:t.__self,d=void 0===t.__source?null:t.__source;for(o in t)a.call(t,o)&&!c.hasOwnProperty(o)&&(u[o]=t[o])}var m=arguments.length-2;if(1===m)u.children=n;else if(m>1){for(var g=Array(m),x=0;x<m;x++)g[x]=arguments[x+2];u.children=g}if(e&&e.defaultProps){var y=e.defaultProps;for(o in y)void 0===u[o]&&(u[o]=y[o])}return l(e,h,p,f,d,s.current,u)},l.createFactory=function(e){var t=l.createElement.bind(null,e);return t.type=e,t},l.cloneAndReplaceKey=function(e,t){var n=l(e.type,t,e.ref,e._self,e._source,e._owner,e.props);return n},l.cloneElement=function(e,t,n){var u,h=o({},e.props),p=e.key,f=e.ref,d=e._self,m=e._source,g=e._owner;if(null!=t){r(t)&&(f=t.ref,g=s.current),i(t)&&(p=""+t.key);var x;e.type&&e.type.defaultProps&&(x=e.type.defaultProps);for(u in t)a.call(t,u)&&!c.hasOwnProperty(u)&&(void 0===t[u]&&void 0!==x?h[u]=x[u]:h[u]=t[u])}var y=arguments.length-2;if(1===y)h.children=n;else if(y>1){for(var v=Array(y),b=0;b<y;b++)v[b]=arguments[b+2];h.children=v}return l(e.type,p,f,d,m,g,h)},l.isValidElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===u},e.exports=l},function(e,t){"use strict";var n={current:null};e.exports=n},function(e,t){"use strict";var n="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103;e.exports=n},function(e,t,n){"use strict";function r(e,t){return e&&"object"==typeof e&&null!=e.key?c.escape(e.key):t.toString(36)}function i(e,t,n,o){var p=typeof e;if("undefined"!==p&&"boolean"!==p||(e=null),null===e||"string"===p||"number"===p||"object"===p&&e.$$typeof===a)return n(o,e,""===t?l+r(e,0):t),1;var f,d,m=0,g=""===t?l:t+h;if(Array.isArray(e))for(var x=0;x<e.length;x++)f=e[x],d=g+r(f,x),m+=i(f,d,n,o);else{var y=u(e);if(y){var v,b=y.call(e);if(y!==e.entries)for(var D=0;!(v=b.next()).done;)f=v.value,d=g+r(f,D++),m+=i(f,d,n,o);else for(;!(v=b.next()).done;){var w=v.value;w&&(f=w[1],d=g+c.escape(w[0])+h+r(f,0),m+=i(f,d,n,o))}}else if("object"===p){var E="",k=String(e);s("31","[object Object]"===k?"object with keys {"+Object.keys(e).join(", ")+"}":k,E)}}return m}function o(e,t,n){return null==e?0:i(e,"",t,n)}var s=n(148),a=(n(159),n(160)),u=n(162),c=(n(154),n(163)),l=(n(150),"."),h=":";e.exports=o},function(e,t){"use strict";function n(e){var t=e&&(r&&e[r]||e[i]);if("function"==typeof t)return t}var r="function"==typeof Symbol&&Symbol.iterator,i="@@iterator";e.exports=n},function(e,t){"use strict";function n(e){var t=/[=:]/g,n={"=":"=0",":":"=2"},r=(""+e).replace(t,function(e){return n[e]});return"$"+r}function r(e){var t=/(=0|=2)/g,n={"=0":"=","=2":":"},r="."===e[0]&&"$"===e[1]?e.substring(2):e.substring(1);return(""+r).replace(t,function(e){return n[e]})}var i={escape:n,unescape:r};e.exports=i},function(e,t,n){"use strict";var r=n(158),i=r.createFactory,o={a:i("a"),abbr:i("abbr"),address:i("address"),area:i("area"),article:i("article"),aside:i("aside"),audio:i("audio"),b:i("b"),base:i("base"),bdi:i("bdi"),bdo:i("bdo"),big:i("big"),blockquote:i("blockquote"),body:i("body"),br:i("br"),button:i("button"),canvas:i("canvas"),caption:i("caption"),cite:i("cite"),code:i("code"),col:i("col"),colgroup:i("colgroup"),data:i("data"),datalist:i("datalist"),dd:i("dd"),del:i("del"),details:i("details"),dfn:i("dfn"),dialog:i("dialog"),div:i("div"),dl:i("dl"),dt:i("dt"),em:i("em"),embed:i("embed"),fieldset:i("fieldset"),figcaption:i("figcaption"),figure:i("figure"),footer:i("footer"),form:i("form"),h1:i("h1"),h2:i("h2"),h3:i("h3"),h4:i("h4"),h5:i("h5"),h6:i("h6"),head:i("head"),header:i("header"),hgroup:i("hgroup"),hr:i("hr"),html:i("html"),i:i("i"),iframe:i("iframe"),img:i("img"),input:i("input"),ins:i("ins"),kbd:i("kbd"),keygen:i("keygen"),label:i("label"),legend:i("legend"),li:i("li"),link:i("link"),main:i("main"),map:i("map"),mark:i("mark"),menu:i("menu"),menuitem:i("menuitem"),meta:i("meta"),meter:i("meter"),nav:i("nav"),noscript:i("noscript"),object:i("object"),ol:i("ol"),optgroup:i("optgroup"),option:i("option"),output:i("output"),p:i("p"),param:i("param"),picture:i("picture"),pre:i("pre"),progress:i("progress"),q:i("q"),rp:i("rp"),rt:i("rt"),ruby:i("ruby"),s:i("s"),samp:i("samp"),script:i("script"),section:i("section"),select:i("select"),small:i("small"),source:i("source"),span:i("span"),strong:i("strong"),style:i("style"),sub:i("sub"),summary:i("summary"),sup:i("sup"),table:i("table"),tbody:i("tbody"),td:i("td"),textarea:i("textarea"),tfoot:i("tfoot"),th:i("th"),thead:i("thead"),time:i("time"),title:i("title"),tr:i("tr"),track:i("track"),u:i("u"),ul:i("ul"),var:i("var"),video:i("video"),wbr:i("wbr"),circle:i("circle"),clipPath:i("clipPath"),defs:i("defs"),ellipse:i("ellipse"),g:i("g"),image:i("image"),line:i("line"),linearGradient:i("linearGradient"),mask:i("mask"),path:i("path"),pattern:i("pattern"),polygon:i("polygon"),polyline:i("polyline"),radialGradient:i("radialGradient"),rect:i("rect"),stop:i("stop"),svg:i("svg"),text:i("text"),tspan:i("tspan")};e.exports=o},function(e,t,n){"use strict";var r=n(158),i=r.isValidElement,o=n(166);e.exports=o(i)},function(e,t,n){"use strict";var r=n(167);e.exports=function(e){var t=!1;return r(e,t)}},function(e,t,n){"use strict";var r=n(151),i=n(154),o=n(150),s=n(168),a=n(169);e.exports=function(e,t){function n(e){var t=e&&(S&&e[S]||e[F]);if("function"==typeof t)return t}function u(e,t){return e===t?0!==e||1/e===1/t:e!==e&&t!==t}function c(e){this.message=e,this.stack=""}function l(e){function n(n,r,o,a,u,l,h){if(a=a||T,l=l||o,h!==s)if(t)i(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");else;return null==r[o]?n?new c(null===r[o]?"The "+u+" `"+l+"` is marked as required "+("in `"+a+"`, but its value is `null`."):"The "+u+" `"+l+"` is marked as required in "+("`"+a+"`, but its value is `undefined`.")):null:e(r,o,a,u,l)}var r=n.bind(null,!1);return r.isRequired=n.bind(null,!0),r}function h(e){function t(t,n,r,i,o,s){var a=t[n],u=E(a);if(u!==e){var l=k(a);return new c("Invalid "+i+" `"+o+"` of type "+("`"+l+"` supplied to `"+r+"`, expected ")+("`"+e+"`."))}return null}return l(t)}function p(){return l(r.thatReturnsNull)}function f(e){function t(t,n,r,i,o){if("function"!=typeof e)return new c("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var a=t[n];if(!Array.isArray(a)){var u=E(a);return new c("Invalid "+i+" `"+o+"` of type "+("`"+u+"` supplied to `"+r+"`, expected an array."))}for(var l=0;l<a.length;l++){var h=e(a,l,r,i,o+"["+l+"]",s);if(h instanceof Error)return h}return null}return l(t)}function d(){function t(t,n,r,i,o){var s=t[n];if(!e(s)){var a=E(s);return new c("Invalid "+i+" `"+o+"` of type "+("`"+a+"` supplied to `"+r+"`, expected a single ReactElement."))}return null}return l(t)}function m(e){function t(t,n,r,i,o){if(!(t[n]instanceof e)){var s=e.name||T,a=C(t[n]);return new c("Invalid "+i+" `"+o+"` of type "+("`"+a+"` supplied to `"+r+"`, expected ")+("instance of `"+s+"`."))}return null}return l(t)}function g(e){function t(t,n,r,i,o){for(var s=t[n],a=0;a<e.length;a++)if(u(s,e[a]))return null;var l=JSON.stringify(e);return new c("Invalid "+i+" `"+o+"` of value `"+s+"` "+("supplied to `"+r+"`, expected one of "+l+"."))}return Array.isArray(e)?l(t):r.thatReturnsNull}function x(e){function t(t,n,r,i,o){if("function"!=typeof e)return new c("Property `"+o+"` of component `"+r+"` has invalid PropType notation inside objectOf.");var a=t[n],u=E(a);if("object"!==u)return new c("Invalid "+i+" `"+o+"` of type "+("`"+u+"` supplied to `"+r+"`, expected an object."));for(var l in a)if(a.hasOwnProperty(l)){var h=e(a,l,r,i,o+"."+l,s);if(h instanceof Error)return h}return null}return l(t)}function y(e){function t(t,n,r,i,o){for(var a=0;a<e.length;a++){var u=e[a];if(null==u(t,n,r,i,o,s))return null}return new c("Invalid "+i+" `"+o+"` supplied to "+("`"+r+"`."))}if(!Array.isArray(e))return r.thatReturnsNull;for(var n=0;n<e.length;n++){var i=e[n];if("function"!=typeof i)return o(!1,"Invalid argument supplid to oneOfType. Expected an array of check functions, but received %s at index %s.",A(i),n),r.thatReturnsNull}return l(t)}function v(){function e(e,t,n,r,i){return D(e[t])?null:new c("Invalid "+r+" `"+i+"` supplied to "+("`"+n+"`, expected a ReactNode."))}return l(e)}function b(e){function t(t,n,r,i,o){var a=t[n],u=E(a);if("object"!==u)return new c("Invalid "+i+" `"+o+"` of type `"+u+"` "+("supplied to `"+r+"`, expected `object`."));for(var l in e){var h=e[l];if(h){var p=h(a,l,r,i,o+"."+l,s);if(p)return p}}return null}return l(t)}function D(t){switch(typeof t){case"number":case"string":case"undefined":return!0;case"boolean":return!t;case"object":if(Array.isArray(t))return t.every(D);if(null===t||e(t))return!0;var r=n(t);if(!r)return!1;var i,o=r.call(t);if(r!==t.entries){for(;!(i=o.next()).done;)if(!D(i.value))return!1}else for(;!(i=o.next()).done;){var s=i.value;if(s&&!D(s[1]))return!1}return!0;default:return!1}}function w(e,t){return"symbol"===e||("Symbol"===t["@@toStringTag"]||"function"==typeof Symbol&&t instanceof Symbol)}function E(e){var t=typeof e;return Array.isArray(e)?"array":e instanceof RegExp?"object":w(t,e)?"symbol":t}function k(e){if("undefined"==typeof e||null===e)return""+e;var t=E(e);if("object"===t){if(e instanceof Date)return"date";if(e instanceof RegExp)return"regexp"}return t}function A(e){var t=k(e);switch(t){case"array":case"object":return"an "+t;case"boolean":case"date":case"regexp":return"a "+t;default:return t}}function C(e){return e.constructor&&e.constructor.name?e.constructor.name:T}var S="function"==typeof Symbol&&Symbol.iterator,F="@@iterator",T="<<anonymous>>",B={array:h("array"),bool:h("boolean"),func:h("function"),number:h("number"),object:h("object"),string:h("string"),symbol:h("symbol"),any:p(),arrayOf:f,element:d(),instanceOf:m,node:v(),objectOf:x,oneOf:g,oneOfType:y,shape:b};return c.prototype=Error.prototype,B.checkPropTypes=a,B.PropTypes=B,B}},function(e,t){"use strict";var n="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";e.exports=n},function(e,t,n){"use strict";function r(e,t,n,r,i){}e.exports=r},function(e,t){"use strict";e.exports="15.6.1"},function(e,t,n){"use strict";var r=n(147),i=r.Component,o=n(158),s=o.isValidElement,a=n(149),u=n(172);e.exports=u(i,s,a)},function(e,t,n){"use strict";function r(e){return e}function i(e,t,n){function i(e,t){var n=y.hasOwnProperty(t)?y[t]:null;w.hasOwnProperty(t)&&u("OVERRIDE_BASE"===n,"ReactClassInterface: You are attempting to override `%s` from your class specification. Ensure that your method names do not overlap with React methods.",t),e&&u("DEFINE_MANY"===n||"DEFINE_MANY_MERGED"===n,"ReactClassInterface: You are attempting to define `%s` on your component more than once. This conflict may be due to a mixin.",t)}function o(e,n){if(n){u("function"!=typeof n,"ReactClass: You're attempting to use a component class or function as a mixin. Instead, just use a regular object."),u(!t(n),"ReactClass: You're attempting to use a component as a mixin. Instead, just use a regular object.");var r=e.prototype,o=r.__reactAutoBindPairs;n.hasOwnProperty(c)&&v.mixins(e,n.mixins);for(var s in n)if(n.hasOwnProperty(s)&&s!==c){var a=n[s],l=r.hasOwnProperty(s);if(i(l,s),v.hasOwnProperty(s))v[s](e,a);else{var h=y.hasOwnProperty(s),d="function"==typeof a,m=d&&!h&&!l&&n.autobind!==!1;if(m)o.push(s,a),r[s]=a;else if(l){var g=y[s];u(h&&("DEFINE_MANY_MERGED"===g||"DEFINE_MANY"===g),"ReactClass: Unexpected spec policy %s for key %s when mixing in component specs.",g,s),"DEFINE_MANY_MERGED"===g?r[s]=p(r[s],a):"DEFINE_MANY"===g&&(r[s]=f(r[s],a))}else r[s]=a}}}else;}function l(e,t){if(t)for(var n in t){var r=t[n];if(t.hasOwnProperty(n)){var i=n in v;u(!i,'ReactClass: You are attempting to define a reserved property, `%s`, that shouldn\'t be on the "statics" key. Define it as an instance property instead; it will still be accessible on the constructor.',n);var o=n in e;u(!o,"ReactClass: You are attempting to define `%s` on your component more than once. This conflict may be due to a mixin.",n),e[n]=r}}}function h(e,t){u(e&&t&&"object"==typeof e&&"object"==typeof t,"mergeIntoWithNoDuplicateKeys(): Cannot merge non-objects.");for(var n in t)t.hasOwnProperty(n)&&(u(void 0===e[n],"mergeIntoWithNoDuplicateKeys(): Tried to merge two objects with the same key: `%s`. This conflict may be due to a mixin; in particular, this may be caused by two getInitialState() or getDefaultProps() methods returning objects with clashing keys.",n),e[n]=t[n]);return e}function p(e,t){return function(){var n=e.apply(this,arguments),r=t.apply(this,arguments);if(null==n)return r;if(null==r)return n;var i={};return h(i,n),h(i,r),i}}function f(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}function d(e,t){var n=t.bind(e);return n}function m(e){for(var t=e.__reactAutoBindPairs,n=0;n<t.length;n+=2){var r=t[n],i=t[n+1];e[r]=d(e,i)}}function g(e){var t=r(function(e,r,i){this.__reactAutoBindPairs.length&&m(this),this.props=e,this.context=r,this.refs=a,this.updater=i||n,this.state=null;var o=this.getInitialState?this.getInitialState():null;u("object"==typeof o&&!Array.isArray(o),"%s.getInitialState(): must return an object or null",t.displayName||"ReactCompositeComponent"),this.state=o});t.prototype=new E,t.prototype.constructor=t,t.prototype.__reactAutoBindPairs=[],x.forEach(o.bind(null,t)),o(t,b),o(t,e),o(t,D),t.getDefaultProps&&(t.defaultProps=t.getDefaultProps()),u(t.prototype.render,"createClass(...): Class specification must implement a `render` method.");for(var i in y)t.prototype[i]||(t.prototype[i]=null);return t}var x=[],y={mixins:"DEFINE_MANY",statics:"DEFINE_MANY",propTypes:"DEFINE_MANY",contextTypes:"DEFINE_MANY",childContextTypes:"DEFINE_MANY",getDefaultProps:"DEFINE_MANY_MERGED",getInitialState:"DEFINE_MANY_MERGED",getChildContext:"DEFINE_MANY_MERGED",render:"DEFINE_ONCE",componentWillMount:"DEFINE_MANY",componentDidMount:"DEFINE_MANY",componentWillReceiveProps:"DEFINE_MANY",shouldComponentUpdate:"DEFINE_ONCE",componentWillUpdate:"DEFINE_MANY",componentDidUpdate:"DEFINE_MANY",componentWillUnmount:"DEFINE_MANY",updateComponent:"OVERRIDE_BASE"},v={displayName:function(e,t){e.displayName=t},mixins:function(e,t){if(t)for(var n=0;n<t.length;n++)o(e,t[n])},childContextTypes:function(e,t){e.childContextTypes=s({},e.childContextTypes,t)},contextTypes:function(e,t){e.contextTypes=s({},e.contextTypes,t)},getDefaultProps:function(e,t){e.getDefaultProps?e.getDefaultProps=p(e.getDefaultProps,t):e.getDefaultProps=t},propTypes:function(e,t){e.propTypes=s({},e.propTypes,t)},statics:function(e,t){l(e,t)},autobind:function(){}},b={componentDidMount:function(){this.__isMounted=!0}},D={componentWillUnmount:function(){this.__isMounted=!1}},w={replaceState:function(e,t){this.updater.enqueueReplaceState(this,e,t)},isMounted:function(){return!!this.__isMounted}},E=function(){};return s(E.prototype,e.prototype,w),g}var o,s=n(146),a=n(153),u=n(154),c="mixins";o={},e.exports=i},function(e,t,n){"use strict";function r(e){return o.isValidElement(e)?void 0:i("143"),e}var i=n(148),o=n(158);n(154);e.exports=r},function(e,t,n){var r=n(175);"string"==typeof r&&(r=[[e.id,r,""]]);n(177)(r,{});r.locals&&(e.exports=r.locals)},function(e,t,n){t=e.exports=n(176)(),t.push([e.id,"@charset \"UTF-8\";.swagger-ui html{box-sizing:border-box}.swagger-ui *,.swagger-ui :after,.swagger-ui :before{box-sizing:inherit}.swagger-ui body{margin:0;background:#fafafa}.swagger-ui .wrapper{width:100%;max-width:1460px;margin:0 auto;padding:0 20px}.swagger-ui .opblock-tag-section{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.swagger-ui .opblock-tag{display:-webkit-box;display:-ms-flexbox;display:flex;padding:10px 20px 10px 10px;cursor:pointer;transition:all .2s;border-bottom:1px solid rgba(59,65,81,.3);-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .opblock-tag:hover{background:rgba(0,0,0,.02)}.swagger-ui .opblock-tag{font-size:24px;margin:0 0 5px;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .opblock-tag.no-desc span{-webkit-box-flex:1;-ms-flex:1;flex:1}.swagger-ui .opblock-tag svg{transition:all .4s}.swagger-ui .opblock-tag small{font-size:14px;font-weight:400;padding:0 10px;-webkit-box-flex:1;-ms-flex:1;flex:1;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .parаmeter__type{font-size:12px;padding:5px 0;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui .view-line-link{position:relative;top:3px;width:20px;margin:0 5px;cursor:pointer;transition:all .5s}.swagger-ui .opblock{margin:0 0 15px;border:1px solid #000;border-radius:4px;box-shadow:0 0 3px rgba(0,0,0,.19)}.swagger-ui .opblock.is-open .opblock-summary{border-bottom:1px solid #000}.swagger-ui .opblock .opblock-section-header{padding:8px 20px;background:hsla(0,0%,100%,.8);box-shadow:0 1px 2px rgba(0,0,0,.1)}.swagger-ui .opblock .opblock-section-header,.swagger-ui .opblock .opblock-section-header label{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .opblock .opblock-section-header label{font-size:12px;font-weight:700;margin:0;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .opblock .opblock-section-header label span{padding:0 10px 0 0}.swagger-ui .opblock .opblock-section-header h4{font-size:14px;margin:0;-webkit-box-flex:1;-ms-flex:1;flex:1;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .opblock .opblock-summary-method{font-size:14px;font-weight:700;min-width:80px;padding:6px 15px;text-align:center;border-radius:3px;background:#000;text-shadow:0 1px 0 rgba(0,0,0,.1);font-family:Titillium Web,sans-serif;color:#fff}.swagger-ui .opblock .opblock-summary-operation-id,.swagger-ui .opblock .opblock-summary-path,.swagger-ui .opblock .opblock-summary-path__deprecated{font-size:16px;display:-webkit-box;display:-ms-flexbox;display:flex;padding:0 10px;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .opblock .opblock-summary-operation-id .view-line-link,.swagger-ui .opblock .opblock-summary-path .view-line-link,.swagger-ui .opblock .opblock-summary-path__deprecated .view-line-link{position:relative;top:2px;width:0;margin:0;cursor:pointer;transition:all .5s}.swagger-ui .opblock .opblock-summary-operation-id:hover .view-line-link,.swagger-ui .opblock .opblock-summary-path:hover .view-line-link,.swagger-ui .opblock .opblock-summary-path__deprecated:hover .view-line-link{width:18px;margin:0 5px}.swagger-ui .opblock .opblock-summary-path__deprecated{text-decoration:line-through}.swagger-ui .opblock .opblock-summary-operation-id{font-size:14px}.swagger-ui .opblock .opblock-summary-description{font-size:13px;-webkit-box-flex:1;-ms-flex:1;flex:1;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .opblock .opblock-summary{display:-webkit-box;display:-ms-flexbox;display:flex;padding:5px;cursor:pointer;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .opblock.opblock-post{border-color:#49cc90;background:rgba(73,204,144,.1)}.swagger-ui .opblock.opblock-post .opblock-summary-method{background:#49cc90}.swagger-ui .opblock.opblock-post .opblock-summary{border-color:#49cc90}.swagger-ui .opblock.opblock-put{border-color:#fca130;background:rgba(252,161,48,.1)}.swagger-ui .opblock.opblock-put .opblock-summary-method{background:#fca130}.swagger-ui .opblock.opblock-put .opblock-summary{border-color:#fca130}.swagger-ui .opblock.opblock-delete{border-color:#f93e3e;background:rgba(249,62,62,.1)}.swagger-ui .opblock.opblock-delete .opblock-summary-method{background:#f93e3e}.swagger-ui .opblock.opblock-delete .opblock-summary{border-color:#f93e3e}.swagger-ui .opblock.opblock-get{border-color:#61affe;background:rgba(97,175,254,.1)}.swagger-ui .opblock.opblock-get .opblock-summary-method{background:#61affe}.swagger-ui .opblock.opblock-get .opblock-summary{border-color:#61affe}.swagger-ui .opblock.opblock-patch{border-color:#50e3c2;background:rgba(80,227,194,.1)}.swagger-ui .opblock.opblock-patch .opblock-summary-method{background:#50e3c2}.swagger-ui .opblock.opblock-patch .opblock-summary{border-color:#50e3c2}.swagger-ui .opblock.opblock-head{border-color:#9012fe;background:rgba(144,18,254,.1)}.swagger-ui .opblock.opblock-head .opblock-summary-method{background:#9012fe}.swagger-ui .opblock.opblock-head .opblock-summary{border-color:#9012fe}.swagger-ui .opblock.opblock-options{border-color:#0d5aa7;background:rgba(13,90,167,.1)}.swagger-ui .opblock.opblock-options .opblock-summary-method{background:#0d5aa7}.swagger-ui .opblock.opblock-options .opblock-summary{border-color:#0d5aa7}.swagger-ui .opblock.opblock-deprecated{opacity:.6;border-color:#ebebeb;background:hsla(0,0%,92%,.1)}.swagger-ui .opblock.opblock-deprecated .opblock-summary-method{background:#ebebeb}.swagger-ui .opblock.opblock-deprecated .opblock-summary{border-color:#ebebeb}.swagger-ui .opblock .opblock-schemes{padding:8px 20px}.swagger-ui .opblock .opblock-schemes .schemes-title{padding:0 10px 0 0}.swagger-ui .tab{display:-webkit-box;display:-ms-flexbox;display:flex;margin:20px 0 10px;padding:0;list-style:none}.swagger-ui .tab li{font-size:12px;min-width:100px;min-width:90px;padding:0;cursor:pointer;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .tab li:first-of-type{position:relative;padding-left:0}.swagger-ui .tab li:first-of-type:after{position:absolute;top:0;right:6px;width:1px;height:100%;content:\"\";background:rgba(0,0,0,.2)}.swagger-ui .tab li.active{font-weight:700}.swagger-ui .opblock-description-wrapper,.swagger-ui .opblock-title_normal{padding:15px 20px}.swagger-ui .opblock-description-wrapper,.swagger-ui .opblock-description-wrapper h4,.swagger-ui .opblock-title_normal,.swagger-ui .opblock-title_normal h4{font-size:12px;margin:0 0 5px;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .opblock-description-wrapper p,.swagger-ui .opblock-title_normal p{font-size:14px;margin:0;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .execute-wrapper{padding:20px;text-align:right}.swagger-ui .execute-wrapper .btn{width:100%;padding:8px 40px}.swagger-ui .body-param-options{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column}.swagger-ui .body-param-options .body-param-edit{padding:10px 0}.swagger-ui .body-param-options label{padding:8px 0}.swagger-ui .body-param-options label select{margin:3px 0 0}.swagger-ui .responses-inner{padding:20px}.swagger-ui .responses-inner h4,.swagger-ui .responses-inner h5{font-size:12px;margin:10px 0 5px;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .response-col_status{font-size:14px;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .response-col_status .response-undocumented{font-size:11px;font-family:Source Code Pro,monospace;font-weight:600;color:#999}.swagger-ui .response-col_description__inner span{font-size:12px;font-style:italic;display:block;margin:10px 0;padding:10px;border-radius:4px;background:#41444e;font-family:Source Code Pro,monospace;font-weight:600;color:#fff}.swagger-ui .response-col_description__inner span p{margin:0}.swagger-ui .opblock-body pre{font-size:12px;margin:0;padding:10px;word-wrap:break-word;word-break:break-all;word-break:break-word;-webkit-hyphens:auto;-ms-hyphens:auto;hyphens:auto;white-space:pre-wrap;border-radius:4px;background:#41444e;overflow-wrap:break-word;font-family:Source Code Pro,monospace;font-weight:600;color:#fff}.swagger-ui .opblock-body pre span{color:#fff!important}.swagger-ui .opblock-body pre .headerline{display:block}.swagger-ui .scheme-container{margin:0 0 20px;padding:30px 0;background:#fff;box-shadow:0 1px 2px 0 rgba(0,0,0,.15)}.swagger-ui .scheme-container .schemes{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .scheme-container .schemes>label{font-size:12px;font-weight:700;display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-orient:vertical;-webkit-box-direction:normal;-ms-flex-direction:column;flex-direction:column;margin:-20px 15px 0 0;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .scheme-container .schemes>label select{min-width:130px;text-transform:uppercase}.swagger-ui .loading-container{padding:40px 0 60px}.swagger-ui .loading-container .loading{position:relative}.swagger-ui .loading-container .loading:after{font-size:10px;font-weight:700;position:absolute;top:50%;left:50%;content:\"loading\";-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);text-transform:uppercase;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .loading-container .loading:before{position:absolute;top:50%;left:50%;display:block;width:60px;height:60px;margin:-30px;content:\"\";-webkit-animation:rotation 1s infinite linear,opacity .5s;animation:rotation 1s infinite linear,opacity .5s;opacity:1;border:2px solid rgba(85,85,85,.1);border-top-color:rgba(0,0,0,.6);border-radius:100%;-webkit-backface-visibility:hidden;backface-visibility:hidden}@-webkit-keyframes rotation{to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@keyframes rotation{to{-webkit-transform:rotate(1turn);transform:rotate(1turn)}}@-webkit-keyframes blinker{50%{opacity:0}}@keyframes blinker{50%{opacity:0}}.swagger-ui .btn{font-size:14px;font-weight:700;padding:5px 23px;transition:all .3s;border:2px solid #888;border-radius:4px;background:transparent;box-shadow:0 1px 2px rgba(0,0,0,.1);font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .btn[disabled]{cursor:not-allowed;opacity:.3}.swagger-ui .btn:hover{box-shadow:0 0 5px rgba(0,0,0,.3)}.swagger-ui .btn.cancel{border-color:#ff6060;font-family:Titillium Web,sans-serif;color:#ff6060}.swagger-ui .btn.authorize{line-height:1;display:inline;color:#49cc90;border-color:#49cc90}.swagger-ui .btn.authorize span{float:left;padding:4px 20px 0 0}.swagger-ui .btn.authorize svg{fill:#49cc90}.swagger-ui .btn.execute{-webkit-animation:swagger-ui-pulse 2s infinite;animation:swagger-ui-pulse 2s infinite;color:#fff;border-color:#4990e2}@-webkit-keyframes swagger-ui-pulse{0%{color:#fff;background:#4990e2;box-shadow:0 0 0 0 rgba(73,144,226,.8)}70%{box-shadow:0 0 0 5px rgba(73,144,226,0)}to{color:#fff;background:#4990e2;box-shadow:0 0 0 0 rgba(73,144,226,0)}}@keyframes swagger-ui-pulse{0%{color:#fff;background:#4990e2;box-shadow:0 0 0 0 rgba(73,144,226,.8)}70%{box-shadow:0 0 0 5px rgba(73,144,226,0)}to{color:#fff;background:#4990e2;box-shadow:0 0 0 0 rgba(73,144,226,0)}}.swagger-ui .btn-group{display:-webkit-box;display:-ms-flexbox;display:flex;padding:30px}.swagger-ui .btn-group .btn{-webkit-box-flex:1;-ms-flex:1;flex:1}.swagger-ui .btn-group .btn:first-child{border-radius:4px 0 0 4px}.swagger-ui .btn-group .btn:last-child{border-radius:0 4px 4px 0}.swagger-ui .authorization__btn{padding:0 10px;border:none;background:none}.swagger-ui .authorization__btn.locked{opacity:1}.swagger-ui .authorization__btn.unlocked{opacity:.4}.swagger-ui .expand-methods,.swagger-ui .expand-operation{border:none;background:none}.swagger-ui .expand-methods svg,.swagger-ui .expand-operation svg{width:20px;height:20px}.swagger-ui .expand-methods{padding:0 10px}.swagger-ui .expand-methods:hover svg{fill:#444}.swagger-ui .expand-methods svg{transition:all .3s;fill:#777}.swagger-ui button{cursor:pointer;outline:none}.swagger-ui select{font-size:14px;font-weight:700;padding:5px 40px 5px 10px;border:2px solid #41444e;border-radius:4px;background:#f7f7f7 url(data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAyMCI+ICAgIDxwYXRoIGQ9Ik0xMy40MTggNy44NTljLjI3MS0uMjY4LjcwOS0uMjY4Ljk3OCAwIC4yNy4yNjguMjcyLjcwMSAwIC45NjlsLTMuOTA4IDMuODNjLS4yNy4yNjgtLjcwNy4yNjgtLjk3OSAwbC0zLjkwOC0zLjgzYy0uMjctLjI2Ny0uMjctLjcwMSAwLS45NjkuMjcxLS4yNjguNzA5LS4yNjguOTc4IDBMMTAgMTFsMy40MTgtMy4xNDF6Ii8+PC9zdmc+) right 10px center no-repeat;background-size:20px;box-shadow:0 1px 2px 0 rgba(0,0,0,.25);font-family:Titillium Web,sans-serif;color:#3b4151;-webkit-appearance:none;-moz-appearance:none;appearance:none}.swagger-ui select[multiple]{margin:5px 0;padding:5px;background:#f7f7f7}.swagger-ui .opblock-body select{min-width:230px}.swagger-ui label{font-size:12px;font-weight:700;margin:0 0 5px;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui input[type=email],.swagger-ui input[type=password],.swagger-ui input[type=search],.swagger-ui input[type=text]{min-width:100px;margin:5px 0;padding:8px 10px;border:1px solid #d9d9d9;border-radius:4px;background:#fff}.swagger-ui input[type=email].invalid,.swagger-ui input[type=password].invalid,.swagger-ui input[type=search].invalid,.swagger-ui input[type=text].invalid{-webkit-animation:shake .4s 1;animation:shake .4s 1;border-color:#f93e3e;background:#feebeb}@-webkit-keyframes shake{10%,90%{-webkit-transform:translate3d(-1px,0,0);transform:translate3d(-1px,0,0)}20%,80%{-webkit-transform:translate3d(2px,0,0);transform:translate3d(2px,0,0)}30%,50%,70%{-webkit-transform:translate3d(-4px,0,0);transform:translate3d(-4px,0,0)}40%,60%{-webkit-transform:translate3d(4px,0,0);transform:translate3d(4px,0,0)}}@keyframes shake{10%,90%{-webkit-transform:translate3d(-1px,0,0);transform:translate3d(-1px,0,0)}20%,80%{-webkit-transform:translate3d(2px,0,0);transform:translate3d(2px,0,0)}30%,50%,70%{-webkit-transform:translate3d(-4px,0,0);transform:translate3d(-4px,0,0)}40%,60%{-webkit-transform:translate3d(4px,0,0);transform:translate3d(4px,0,0)}}.swagger-ui textarea{font-size:12px;width:100%;min-height:280px;padding:10px;border:none;border-radius:4px;outline:none;background:hsla(0,0%,100%,.8);font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui textarea:focus{border:2px solid #61affe}.swagger-ui textarea.curl{font-size:12px;min-height:100px;margin:0;padding:10px;resize:none;border-radius:4px;background:#41444e;font-family:Source Code Pro,monospace;font-weight:600;color:#fff}.swagger-ui .checkbox{padding:5px 0 10px;transition:opacity .5s;color:#333}.swagger-ui .checkbox label{display:-webkit-box;display:-ms-flexbox;display:flex}.swagger-ui .checkbox p{font-weight:400!important;font-style:italic;margin:0!important;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui .checkbox input[type=checkbox]{display:none}.swagger-ui .checkbox input[type=checkbox]+label>.item{position:relative;top:3px;display:inline-block;width:16px;height:16px;margin:0 8px 0 0;padding:5px;cursor:pointer;border-radius:1px;background:#e8e8e8;box-shadow:0 0 0 2px #e8e8e8;-webkit-box-flex:0;-ms-flex:none;flex:none}.swagger-ui .checkbox input[type=checkbox]+label>.item:active{-webkit-transform:scale(.9);transform:scale(.9)}.swagger-ui .checkbox input[type=checkbox]:checked+label>.item{background:#e8e8e8 url(\"data:image/svg+xml;charset=utf-8,%3Csvg width='10' height='8' viewBox='3 7 10 8' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='%2341474E' fill-rule='evenodd' d='M6.333 15L3 11.667l1.333-1.334 2 2L11.667 7 13 8.333z'/%3E%3C/svg%3E\") 50% no-repeat}.swagger-ui .dialog-ux{position:fixed;z-index:9999;top:0;right:0;bottom:0;left:0}.swagger-ui .dialog-ux .backdrop-ux{position:fixed;top:0;right:0;bottom:0;left:0;background:rgba(0,0,0,.8)}.swagger-ui .dialog-ux .modal-ux{position:absolute;z-index:9999;top:50%;left:50%;width:100%;min-width:300px;max-width:650px;-webkit-transform:translate(-50%,-50%);transform:translate(-50%,-50%);border:1px solid #ebebeb;border-radius:4px;background:#fff;box-shadow:0 10px 30px 0 rgba(0,0,0,.2)}.swagger-ui .dialog-ux .modal-ux-content{overflow-y:auto;max-height:540px;padding:20px}.swagger-ui .dialog-ux .modal-ux-content p{font-size:12px;margin:0 0 5px;color:#41444e;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .dialog-ux .modal-ux-content h4{font-size:18px;font-weight:600;margin:15px 0 0;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .dialog-ux .modal-ux-header{display:-webkit-box;display:-ms-flexbox;display:flex;padding:12px 0;border-bottom:1px solid #ebebeb;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .dialog-ux .modal-ux-header .close-modal{padding:0 10px;border:none;background:none;-webkit-appearance:none;-moz-appearance:none;appearance:none}.swagger-ui .dialog-ux .modal-ux-header h3{font-size:20px;font-weight:600;margin:0;padding:0 20px;-webkit-box-flex:1;-ms-flex:1;flex:1;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .model{font-size:12px;font-weight:300;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui .model-toggle{font-size:10px;position:relative;top:6px;display:inline-block;margin:auto .3em;cursor:pointer;transition:-webkit-transform .15s ease-in;transition:transform .15s ease-in;transition:transform .15s ease-in,-webkit-transform .15s ease-in;-webkit-transform:rotate(90deg);transform:rotate(90deg);-webkit-transform-origin:50% 50%;transform-origin:50% 50%}.swagger-ui .model-toggle.collapsed{-webkit-transform:rotate(0deg);transform:rotate(0deg)}.swagger-ui .model-toggle:after{display:block;width:20px;height:20px;content:\"\";background:url(\"data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z'/%3E%3C/svg%3E\") 50% no-repeat;background-size:100%}.swagger-ui .model-jump-to-path{position:relative;cursor:pointer}.swagger-ui .model-jump-to-path .view-line-link{position:absolute;top:-.4em;cursor:pointer}.swagger-ui .model-title{position:relative}.swagger-ui .model-title:hover .model-hint{visibility:visible}.swagger-ui .model-hint{position:absolute;top:-1.8em;visibility:hidden;padding:.1em .5em;white-space:nowrap;color:#ebebeb;border-radius:4px;background:rgba(0,0,0,.7)}.swagger-ui section.models{margin:30px 0;border:1px solid rgba(59,65,81,.3);border-radius:4px}.swagger-ui section.models.is-open{padding:0 0 20px}.swagger-ui section.models.is-open h4{margin:0 0 5px;border-bottom:1px solid rgba(59,65,81,.3)}.swagger-ui section.models.is-open h4 svg{-webkit-transform:rotate(90deg);transform:rotate(90deg)}.swagger-ui section.models h4{font-size:16px;display:-webkit-box;display:-ms-flexbox;display:flex;margin:0;padding:10px 20px 10px 10px;cursor:pointer;transition:all .2s;font-family:Titillium Web,sans-serif;color:#777;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui section.models h4 svg{transition:all .4s}.swagger-ui section.models h4 span{-webkit-box-flex:1;-ms-flex:1;flex:1}.swagger-ui section.models h4:hover{background:rgba(0,0,0,.02)}.swagger-ui section.models h5{font-size:16px;margin:0 0 10px;font-family:Titillium Web,sans-serif;color:#777}.swagger-ui section.models .model-jump-to-path{position:relative;top:5px}.swagger-ui section.models .model-container{margin:0 20px 15px;transition:all .5s;border-radius:4px;background:rgba(0,0,0,.05)}.swagger-ui section.models .model-container:hover{background:rgba(0,0,0,.07)}.swagger-ui section.models .model-container:first-of-type{margin:20px}.swagger-ui section.models .model-container:last-of-type{margin:0 20px}.swagger-ui section.models .model-box{background:none}.swagger-ui .model-box{padding:10px;border-radius:4px;background:rgba(0,0,0,.1)}.swagger-ui .model-box .model-jump-to-path{position:relative;top:4px}.swagger-ui .model-title{font-size:16px;font-family:Titillium Web,sans-serif;color:#555}.swagger-ui span>span.model,.swagger-ui span>span.model .brace-close{padding:0 0 0 10px}.swagger-ui .prop-type{color:#55a}.swagger-ui .prop-enum{display:block}.swagger-ui .prop-format{color:#999}.swagger-ui table{width:100%;padding:0 10px;border-collapse:collapse}.swagger-ui table.model tbody tr td{padding:0;vertical-align:top}.swagger-ui table.model tbody tr td:first-of-type{width:100px;padding:0}.swagger-ui table.headers td{font-size:12px;font-weight:300;vertical-align:middle;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui table tbody tr td{padding:10px 0 0;vertical-align:top}.swagger-ui table tbody tr td:first-of-type{width:20%;padding:10px 0}.swagger-ui table thead tr td,.swagger-ui table thead tr th{font-size:12px;font-weight:700;padding:12px 0;text-align:left;border-bottom:1px solid rgba(59,65,81,.2);font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .parameters-col_description p{font-size:14px;margin:0;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .parameters-col_description input[type=text]{width:100%;max-width:340px}.swagger-ui .parameter__name{font-size:16px;font-weight:400;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .parameter__name.required{font-weight:700}.swagger-ui .parameter__name.required:after{font-size:10px;position:relative;top:-6px;padding:5px;content:\"required\";color:rgba(255,0,0,.6)}.swagger-ui .parameter__in{font-size:12px;font-style:italic;font-family:Source Code Pro,monospace;font-weight:600;color:#888}.swagger-ui .table-container{padding:20px}.swagger-ui .topbar{padding:8px 30px;background-color:#89bf04}.swagger-ui .topbar .topbar-wrapper{-ms-flex-align:center}.swagger-ui .topbar .topbar-wrapper,.swagger-ui .topbar a{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;align-items:center}.swagger-ui .topbar a{font-size:1.5em;font-weight:700;max-width:300px;text-decoration:none;-webkit-box-flex:1;-ms-flex:1;flex:1;-ms-flex-align:center;font-family:Titillium Web,sans-serif;color:#fff}.swagger-ui .topbar a span{margin:0;padding:0 10px}.swagger-ui .topbar .download-url-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-flex:3;-ms-flex:3;flex:3}.swagger-ui .topbar .download-url-wrapper input[type=text]{width:100%;min-width:350px;margin:0;border:2px solid #547f00;border-radius:4px 0 0 4px;outline:none}.swagger-ui .topbar .download-url-wrapper .download-url-button{font-size:16px;font-weight:700;padding:4px 40px;border:none;border-radius:0 4px 4px 0;background:#547f00;font-family:Titillium Web,sans-serif;color:#fff}.swagger-ui .info{margin:50px 0}.swagger-ui .info hgroup.main{margin:0 0 20px}.swagger-ui .info hgroup.main a{font-size:12px}.swagger-ui .info li,.swagger-ui .info p,.swagger-ui .info table{font-size:14px;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .info h1,.swagger-ui .info h2,.swagger-ui .info h3,.swagger-ui .info h4,.swagger-ui .info h5{font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .info code{padding:3px 5px;border-radius:4px;background:rgba(0,0,0,.05);font-family:Source Code Pro,monospace;font-weight:600;color:#9012fe}.swagger-ui .info a{font-size:14px;transition:all .4s;font-family:Open Sans,sans-serif;color:#4990e2}.swagger-ui .info a:hover{color:#1f69c0}.swagger-ui .info>div{margin:0 0 5px}.swagger-ui .info .base-url{font-size:12px;font-weight:300!important;margin:0;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui .info .title{font-size:36px;margin:0;font-family:Open Sans,sans-serif;color:#3b4151}.swagger-ui .info .title small{font-size:10px;position:relative;top:-5px;display:inline-block;margin:0 0 0 5px;padding:2px 4px;vertical-align:super;border-radius:57px;background:#7d8492}.swagger-ui .info .title small pre{margin:0;font-family:Titillium Web,sans-serif;color:#fff}.swagger-ui .auth-btn-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;padding:10px 0;-webkit-box-pack:center;-ms-flex-pack:center;justify-content:center}.swagger-ui .auth-wrapper{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-ms-flex:1;flex:1;-webkit-box-pack:end;-ms-flex-pack:end;justify-content:flex-end}.swagger-ui .auth-wrapper .authorize{padding-right:20px}.swagger-ui .auth-container{margin:0 0 10px;padding:10px 20px;border-bottom:1px solid #ebebeb}.swagger-ui .auth-container:last-of-type{margin:0;padding:10px 20px;border:0}.swagger-ui .auth-container h4{margin:5px 0 15px!important}.swagger-ui .auth-container .wrapper{margin:0;padding:0}.swagger-ui .auth-container input[type=password],.swagger-ui .auth-container input[type=text]{min-width:230px}.swagger-ui .auth-container .errors{font-size:12px;padding:10px;border-radius:4px;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui .scopes h2{font-size:14px;font-family:Titillium Web,sans-serif;color:#3b4151}.swagger-ui .scope-def{padding:0 0 20px}.swagger-ui .errors-wrapper{margin:20px;padding:10px 20px;-webkit-animation:scaleUp .5s;animation:scaleUp .5s;border:2px solid #f93e3e;border-radius:4px;background:rgba(249,62,62,.1)}.swagger-ui .errors-wrapper .error-wrapper{margin:0 0 10px}.swagger-ui .errors-wrapper .errors h4{font-size:14px;margin:0;font-family:Source Code Pro,monospace;font-weight:600;color:#3b4151}.swagger-ui .errors-wrapper .errors small{color:#666}.swagger-ui .errors-wrapper hgroup{display:-webkit-box;display:-ms-flexbox;display:flex;-webkit-box-align:center;-ms-flex-align:center;align-items:center}.swagger-ui .errors-wrapper hgroup h4{font-size:20px;margin:0;-webkit-box-flex:1;-ms-flex:1;flex:1;font-family:Titillium Web,sans-serif;color:#3b4151}@-webkit-keyframes scaleUp{0%{-webkit-transform:scale(.8);transform:scale(.8);opacity:0}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}@keyframes scaleUp{0%{-webkit-transform:scale(.8);transform:scale(.8);opacity:0}to{-webkit-transform:scale(1);transform:scale(1);opacity:1}}",""]);
},function(e,t){e.exports=function(){var e=[];return e.toString=function(){for(var e=[],t=0;t<this.length;t++){var n=this[t];n[2]?e.push("@media "+n[2]+"{"+n[1]+"}"):e.push(n[1])}return e.join("")},e.i=function(t,n){"string"==typeof t&&(t=[[null,t,""]]);for(var r={},i=0;i<this.length;i++){var o=this[i][0];"number"==typeof o&&(r[o]=!0)}for(i=0;i<t.length;i++){var s=t[i];"number"==typeof s[0]&&r[s[0]]||(n&&!s[2]?s[2]=n:n&&(s[2]="("+s[2]+") and ("+n+")"),e.push(s))}},e}},function(e,t,n){function r(e,t){for(var n=0;n<e.length;n++){var r=e[n],i=f[r.id];if(i){i.refs++;for(var o=0;o<i.parts.length;o++)i.parts[o](r.parts[o]);for(;o<r.parts.length;o++)i.parts.push(c(r.parts[o],t))}else{for(var s=[],o=0;o<r.parts.length;o++)s.push(c(r.parts[o],t));f[r.id]={id:r.id,refs:1,parts:s}}}}function i(e){for(var t=[],n={},r=0;r<e.length;r++){var i=e[r],o=i[0],s=i[1],a=i[2],u=i[3],c={css:s,media:a,sourceMap:u};n[o]?n[o].parts.push(c):t.push(n[o]={id:o,parts:[c]})}return t}function o(e,t){var n=g(),r=v[v.length-1];if("top"===e.insertAt)r?r.nextSibling?n.insertBefore(t,r.nextSibling):n.appendChild(t):n.insertBefore(t,n.firstChild),v.push(t);else{if("bottom"!==e.insertAt)throw new Error("Invalid value for parameter 'insertAt'. Must be 'top' or 'bottom'.");n.appendChild(t)}}function s(e){e.parentNode.removeChild(e);var t=v.indexOf(e);t>=0&&v.splice(t,1)}function a(e){var t=document.createElement("style");return t.type="text/css",o(e,t),t}function u(e){var t=document.createElement("link");return t.rel="stylesheet",o(e,t),t}function c(e,t){var n,r,i;if(t.singleton){var o=y++;n=x||(x=a(t)),r=l.bind(null,n,o,!1),i=l.bind(null,n,o,!0)}else e.sourceMap&&"function"==typeof URL&&"function"==typeof URL.createObjectURL&&"function"==typeof URL.revokeObjectURL&&"function"==typeof Blob&&"function"==typeof btoa?(n=u(t),r=p.bind(null,n),i=function(){s(n),n.href&&URL.revokeObjectURL(n.href)}):(n=a(t),r=h.bind(null,n),i=function(){s(n)});return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else i()}}function l(e,t,n,r){var i=n?"":r.css;if(e.styleSheet)e.styleSheet.cssText=b(t,i);else{var o=document.createTextNode(i),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(o,s[t]):e.appendChild(o)}}function h(e,t){var n=t.css,r=t.media;t.sourceMap;if(r&&e.setAttribute("media",r),e.styleSheet)e.styleSheet.cssText=n;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(n))}}function p(e,t){var n=t.css,r=(t.media,t.sourceMap);r&&(n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(r))))+" */");var i=new Blob([n],{type:"text/css"}),o=e.href;e.href=URL.createObjectURL(i),o&&URL.revokeObjectURL(o)}var f={},d=function(e){var t;return function(){return"undefined"==typeof t&&(t=e.apply(this,arguments)),t}},m=d(function(){return/msie [6-9]\b/.test(window.navigator.userAgent.toLowerCase())}),g=d(function(){return document.head||document.getElementsByTagName("head")[0]}),x=null,y=0,v=[];e.exports=function(e,t){t=t||{},"undefined"==typeof t.singleton&&(t.singleton=m()),"undefined"==typeof t.insertAt&&(t.insertAt="bottom");var n=i(e);return r(n,t),function(e){for(var o=[],s=0;s<n.length;s++){var a=n[s],u=f[a.id];u.refs--,o.push(u)}if(e){var c=i(e);r(c,t)}for(var s=0;s<o.length;s++){var u=o[s];if(0===u.refs){for(var l=0;l<u.parts.length;l++)u.parts[l]();delete f[u.id]}}}};var b=function(){var e=[];return function(t,n){return e[t]=n,e.filter(Boolean).join("\n")}}()},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return{components:{Topbar:o.default}}};var i=n(179),o=r(i)},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}Object.defineProperty(t,"__esModule",{value:!0});var i=n(58),o=r(i),s=n(84),a=r(s),u=n(85),c=r(u),l=n(89),h=r(l),p=n(136),f=r(p),d=n(144),m=r(d),g=n(180),x=r(g),y=function(e){function t(e,n){(0,a.default)(this,t);var r=(0,h.default)(this,(t.__proto__||(0,o.default)(t)).call(this,e,n));return r.onUrlChange=function(e){var t=e.target.value;r.setState({url:t})},r.downloadUrl=function(e){r.props.specActions.updateUrl(r.state.url),r.props.specActions.download(r.state.url),e.preventDefault()},r.state={url:e.specSelectors.url()},r}return(0,f.default)(t,e),(0,c.default)(t,[{key:"componentWillReceiveProps",value:function(e){this.setState({url:e.specSelectors.url()})}},{key:"render",value:function(){var e=this.props,t=e.getComponent,n=e.specSelectors,r=t("Button"),i=t("Link"),o="loading"===n.loadingStatus(),s="failed"===n.loadingStatus(),a={};return s&&(a.color="red"),o&&(a.color="#aaa"),m.default.createElement("div",{className:"topbar"},m.default.createElement("div",{className:"wrapper"},m.default.createElement("div",{className:"topbar-wrapper"},m.default.createElement(i,{href:"#",title:"Swagger UX"},m.default.createElement("img",{height:"30",width:"30",src:x.default,alt:"Swagger UX"}),m.default.createElement("span",null,"swagger")),m.default.createElement("form",{className:"download-url-wrapper",onSubmit:this.downloadUrl},m.default.createElement("input",{className:"download-url-input",type:"text",onChange:this.onUrlChange,value:this.state.url,disabled:o,style:a}),m.default.createElement(r,{className:"download-url-button",onClick:this.downloadUrl},"Explore")))))}}]),t}(m.default.Component);t.default=y,y.propTypes={specSelectors:d.PropTypes.object.isRequired,specActions:d.PropTypes.object.isRequired,getComponent:d.PropTypes.func.isRequired}},function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAqRJREFUeNrEVz1s00AUfnGXii5maMXoEUEHVwIpEkPNgkBdMnQoU5ytiKHJwpp2Q2JIO8DCUDOxIJFIVOoWZyJSh3pp1Q2PVVlcCVBH3ufeVZZ9Zye1Ay86nXV+ue/9fO/lheg/Se02X1rvksmbnTiKvuxQMBNgBnN4a/LCbmnUAP6JV58NCUsBC8CuAJxGPF47OgNqBaA93tolUhnx6jC4NxGwyOEwlccyAs+3kwdzKq0HDn2vEBTi8J2XpyMaywNDE157BhXUE3zJhlq8GKq+Zd2zaWHepPA8oN9XkfLmRdOiJV4XUUg/IyWncLjCYY/SHndV2u7zHr3bPKZtdxgboJOnthvrfGj/oMf3G0r7JVmNlLfKklmrt2MvvcNO7LFOhoFHfuAJI5o6ta10jpt5CQLgwXhXG2YIwvu+34qf78ybOjWTnWwkgR36d7JqJOrW0hHmNrKg9xhiS4+1jFmrxymh03B0w+6kURIAu3yHtOD5oaUNojMnGgbcctNvwdAnyxvxRR+/vaJnjzbpzcZX+nN1SdGv85i9eH8w3qPO+mdm/y4dnQ1iI8Fq6Nf4cxL6GWSjiFDSs0VRnxC5g0xSB2cgHpaseTxfqOv5uoHkNQ6Ha/N1Yz9mNMppEkEkYKj79q6uCq4bCHcSX3fJ0Vk/k9siASjCm1N6gZH6Ec9IXt2WkFES2K/ixoIyktJPAu/ptOA1SgO5zqtr6KASJPF0nMV8dgMsRhRPOcMwqQAOoi0VAIMLAEWJ6YYC1c8ibj1GP51RqwzYwZVMHQuvOzMCBUtb2tGHx5NAdLKqp5AX7Ng4d+Zi8AGDI9z1ijx9yaCH04y3GCP2S+QcvaGl+pcxyUBvinFlawoDQjHSelX8hQEoIrAq8p/mgC88HOS1YCl/BRgAmiD/1gn6Nu8AAAAASUVORK5CYII="},function(e,t,n){"use strict";function r(e){return e&&e.__esModule?e:{default:e}}function i(e){var t=e.fn,n={downloadConfig:function(e){return function(){var n=t.fetch;return n(e)}},getConfigByUrl:function(e,t){return function(n){function r(n){n instanceof Error||n.status>=400?(i.updateLoadingStatus("failedConfig"),i.updateLoadingStatus("failedConfig"),i.updateUrl(""),console.error(n.statusText+" "+e),t(null)):t(c(n.text))}var i=n.specActions;if(e)return i.downloadConfig(e).then(r,r)}}},r={getLocalConfig:function(){return c(u.default)}};return{statePlugins:{spec:{actions:n,selectors:r}}}}Object.defineProperty(t,"__esModule",{value:!0}),t.default=i;var o=n(182),s=r(o),a=n(217),u=r(a),c=function(e,t){try{return s.default.safeLoad(e)}catch(e){return t&&t.errActions.newThrownErr(new Error(e)),{}}}},function(e,t,n){"use strict";var r=n(183);e.exports=r},function(e,t,n){"use strict";function r(e){return function(){throw new Error("Function "+e+" is deprecated and cannot be used.")}}var i=n(184),o=n(216);e.exports.Type=n(190),e.exports.Schema=n(189),e.exports.FAILSAFE_SCHEMA=n(193),e.exports.JSON_SCHEMA=n(192),e.exports.CORE_SCHEMA=n(191),e.exports.DEFAULT_SAFE_SCHEMA=n(188),e.exports.DEFAULT_FULL_SCHEMA=n(211),e.exports.load=i.load,e.exports.loadAll=i.loadAll,e.exports.safeLoad=i.safeLoad,e.exports.safeLoadAll=i.safeLoadAll,e.exports.dump=o.dump,e.exports.safeDump=o.safeDump,e.exports.YAMLException=n(186),e.exports.MINIMAL_SCHEMA=n(193),e.exports.SAFE_SCHEMA=n(188),e.exports.DEFAULT_SCHEMA=n(211),e.exports.scan=r("scan"),e.exports.parse=r("parse"),e.exports.compose=r("compose"),e.exports.addConstructor=r("addConstructor")},function(e,t,n){"use strict";function r(e){return 10===e||13===e}function i(e){return 9===e||32===e}function o(e){return 9===e||32===e||10===e||13===e}function s(e){return 44===e||91===e||93===e||123===e||125===e}function a(e){var t;return 48<=e&&e<=57?e-48:(t=32|e,97<=t&&t<=102?t-97+10:-1)}function u(e){return 120===e?2:117===e?4:85===e?8:0}function c(e){return 48<=e&&e<=57?e-48:-1}function l(e){return 48===e?"\0":97===e?"":98===e?"\b":116===e?"\t":9===e?"\t":110===e?"\n":118===e?"\v":102===e?"\f":114===e?"\r":101===e?"":32===e?" ":34===e?'"':47===e?"/":92===e?"\\":78===e?"":95===e?" ":76===e?"\u2028":80===e?"\u2029":""}function h(e){return e<=65535?String.fromCharCode(e):String.fromCharCode((e-65536>>10)+55296,(e-65536&1023)+56320)}function p(e,t){this.input=e,this.filename=t.filename||null,this.schema=t.schema||Y,this.onWarning=t.onWarning||null,this.legacy=t.legacy||!1,this.json=t.json||!1,this.listener=t.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=e.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function f(e,t){return new z(t,new J(e.filename,e.input,e.position,e.line,e.position-e.lineStart))}function d(e,t){throw f(e,t)}function m(e,t){e.onWarning&&e.onWarning.call(null,f(e,t))}function g(e,t,n,r){var i,o,s,a;if(t<n){if(a=e.input.slice(t,n),r)for(i=0,o=a.length;i<o;i+=1)s=a.charCodeAt(i),9===s||32<=s&&s<=1114111||d(e,"expected valid JSON character");else Q.test(a)&&d(e,"the stream contains non-printable characters");e.result+=a}}function x(e,t,n,r){var i,o,s,a;for(j.isObject(n)||d(e,"cannot merge mappings; the provided source object is unacceptable"),i=Object.keys(n),s=0,a=i.length;s<a;s+=1)o=i[s],K.call(t,o)||(t[o]=n[o],r[o]=!0)}function y(e,t,n,r,i,o,s,a){var u,c;if(i=String(i),null===t&&(t={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(o))for(u=0,c=o.length;u<c;u+=1)x(e,t,o[u],n);else x(e,t,o,n);else e.json||K.call(n,i)||!K.call(t,i)||(e.line=s||e.line,e.position=a||e.position,d(e,"duplicated mapping key")),t[i]=o,delete n[i];return t}function v(e){var t;t=e.input.charCodeAt(e.position),10===t?e.position++:13===t?(e.position++,10===e.input.charCodeAt(e.position)&&e.position++):d(e,"a line break is expected"),e.line+=1,e.lineStart=e.position}function b(e,t,n){for(var o=0,s=e.input.charCodeAt(e.position);0!==s;){for(;i(s);)s=e.input.charCodeAt(++e.position);if(t&&35===s)do s=e.input.charCodeAt(++e.position);while(10!==s&&13!==s&&0!==s);if(!r(s))break;for(v(e),s=e.input.charCodeAt(e.position),o++,e.lineIndent=0;32===s;)e.lineIndent++,s=e.input.charCodeAt(++e.position)}return n!==-1&&0!==o&&e.lineIndent<n&&m(e,"deficient indentation"),o}function D(e){var t,n=e.position;return t=e.input.charCodeAt(n),!(45!==t&&46!==t||t!==e.input.charCodeAt(n+1)||t!==e.input.charCodeAt(n+2)||(n+=3,t=e.input.charCodeAt(n),0!==t&&!o(t)))}function w(e,t){1===t?e.result+=" ":t>1&&(e.result+=j.repeat("\n",t-1))}function E(e,t,n){var a,u,c,l,h,p,f,d,m,x=e.kind,y=e.result;if(m=e.input.charCodeAt(e.position),o(m)||s(m)||35===m||38===m||42===m||33===m||124===m||62===m||39===m||34===m||37===m||64===m||96===m)return!1;if((63===m||45===m)&&(u=e.input.charCodeAt(e.position+1),o(u)||n&&s(u)))return!1;for(e.kind="scalar",e.result="",c=l=e.position,h=!1;0!==m;){if(58===m){if(u=e.input.charCodeAt(e.position+1),o(u)||n&&s(u))break}else if(35===m){if(a=e.input.charCodeAt(e.position-1),o(a))break}else{if(e.position===e.lineStart&&D(e)||n&&s(m))break;if(r(m)){if(p=e.line,f=e.lineStart,d=e.lineIndent,b(e,!1,-1),e.lineIndent>=t){h=!0,m=e.input.charCodeAt(e.position);continue}e.position=l,e.line=p,e.lineStart=f,e.lineIndent=d;break}}h&&(g(e,c,l,!1),w(e,e.line-p),c=l=e.position,h=!1),i(m)||(l=e.position+1),m=e.input.charCodeAt(++e.position)}return g(e,c,l,!1),!!e.result||(e.kind=x,e.result=y,!1)}function k(e,t){var n,i,o;if(n=e.input.charCodeAt(e.position),39!==n)return!1;for(e.kind="scalar",e.result="",e.position++,i=o=e.position;0!==(n=e.input.charCodeAt(e.position));)if(39===n){if(g(e,i,e.position,!0),n=e.input.charCodeAt(++e.position),39!==n)return!0;i=e.position,e.position++,o=e.position}else r(n)?(g(e,i,o,!0),w(e,b(e,!1,t)),i=o=e.position):e.position===e.lineStart&&D(e)?d(e,"unexpected end of the document within a single quoted scalar"):(e.position++,o=e.position);d(e,"unexpected end of the stream within a single quoted scalar")}function A(e,t){var n,i,o,s,c,l;if(l=e.input.charCodeAt(e.position),34!==l)return!1;for(e.kind="scalar",e.result="",e.position++,n=i=e.position;0!==(l=e.input.charCodeAt(e.position));){if(34===l)return g(e,n,e.position,!0),e.position++,!0;if(92===l){if(g(e,n,e.position,!0),l=e.input.charCodeAt(++e.position),r(l))b(e,!1,t);else if(l<256&&ie[l])e.result+=oe[l],e.position++;else if((c=u(l))>0){for(o=c,s=0;o>0;o--)l=e.input.charCodeAt(++e.position),(c=a(l))>=0?s=(s<<4)+c:d(e,"expected hexadecimal character");e.result+=h(s),e.position++}else d(e,"unknown escape sequence");n=i=e.position}else r(l)?(g(e,n,i,!0),w(e,b(e,!1,t)),n=i=e.position):e.position===e.lineStart&&D(e)?d(e,"unexpected end of the document within a double quoted scalar"):(e.position++,i=e.position)}d(e,"unexpected end of the stream within a double quoted scalar")}function C(e,t){var n,r,i,s,a,u,c,l,h,p,f,m=!0,g=e.tag,x=e.anchor,v={};if(f=e.input.charCodeAt(e.position),91===f)s=93,c=!1,r=[];else{if(123!==f)return!1;s=125,c=!0,r={}}for(null!==e.anchor&&(e.anchorMap[e.anchor]=r),f=e.input.charCodeAt(++e.position);0!==f;){if(b(e,!0,t),f=e.input.charCodeAt(e.position),f===s)return e.position++,e.tag=g,e.anchor=x,e.kind=c?"mapping":"sequence",e.result=r,!0;m||d(e,"missed comma between flow collection entries"),h=l=p=null,a=u=!1,63===f&&(i=e.input.charCodeAt(e.position+1),o(i)&&(a=u=!0,e.position++,b(e,!0,t))),n=e.line,P(e,t,W,!1,!0),h=e.tag,l=e.result,b(e,!0,t),f=e.input.charCodeAt(e.position),!u&&e.line!==n||58!==f||(a=!0,f=e.input.charCodeAt(++e.position),b(e,!0,t),P(e,t,W,!1,!0),p=e.result),c?y(e,r,v,h,l,p):a?r.push(y(e,null,v,h,l,p)):r.push(l),b(e,!0,t),f=e.input.charCodeAt(e.position),44===f?(m=!0,f=e.input.charCodeAt(++e.position)):m=!1}d(e,"unexpected end of the stream within a flow collection")}function S(e,t){var n,o,s,a,u=V,l=!1,h=!1,p=t,f=0,m=!1;if(a=e.input.charCodeAt(e.position),124===a)o=!1;else{if(62!==a)return!1;o=!0}for(e.kind="scalar",e.result="";0!==a;)if(a=e.input.charCodeAt(++e.position),43===a||45===a)V===u?u=43===a?Z:$:d(e,"repeat of a chomping mode identifier");else{if(!((s=c(a))>=0))break;0===s?d(e,"bad explicit indentation width of a block scalar; it cannot be less than one"):h?d(e,"repeat of an indentation width identifier"):(p=t+s-1,h=!0)}if(i(a)){do a=e.input.charCodeAt(++e.position);while(i(a));if(35===a)do a=e.input.charCodeAt(++e.position);while(!r(a)&&0!==a)}for(;0!==a;){for(v(e),e.lineIndent=0,a=e.input.charCodeAt(e.position);(!h||e.lineIndent<p)&&32===a;)e.lineIndent++,a=e.input.charCodeAt(++e.position);if(!h&&e.lineIndent>p&&(p=e.lineIndent),r(a))f++;else{if(e.lineIndent<p){u===Z?e.result+=j.repeat("\n",l?1+f:f):u===V&&l&&(e.result+="\n");break}for(o?i(a)?(m=!0,e.result+=j.repeat("\n",l?1+f:f)):m?(m=!1,e.result+=j.repeat("\n",f+1)):0===f?l&&(e.result+=" "):e.result+=j.repeat("\n",f):e.result+=j.repeat("\n",l?1+f:f),l=!0,h=!0,f=0,n=e.position;!r(a)&&0!==a;)a=e.input.charCodeAt(++e.position);g(e,n,e.position,!1)}}return!0}function F(e,t){var n,r,i,s=e.tag,a=e.anchor,u=[],c=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=u),i=e.input.charCodeAt(e.position);0!==i&&45===i&&(r=e.input.charCodeAt(e.position+1),o(r));)if(c=!0,e.position++,b(e,!0,-1)&&e.lineIndent<=t)u.push(null),i=e.input.charCodeAt(e.position);else if(n=e.line,P(e,t,q,!1,!0),u.push(e.result),b(e,!0,-1),i=e.input.charCodeAt(e.position),(e.line===n||e.lineIndent>t)&&0!==i)d(e,"bad indentation of a sequence entry");else if(e.lineIndent<t)break;return!!c&&(e.tag=s,e.anchor=a,e.kind="sequence",e.result=u,!0)}function T(e,t,n){var r,s,a,u,c,l=e.tag,h=e.anchor,p={},f={},m=null,g=null,x=null,v=!1,D=!1;for(null!==e.anchor&&(e.anchorMap[e.anchor]=p),c=e.input.charCodeAt(e.position);0!==c;){if(r=e.input.charCodeAt(e.position+1),a=e.line,u=e.position,63!==c&&58!==c||!o(r)){if(!P(e,n,H,!1,!0))break;if(e.line===a){for(c=e.input.charCodeAt(e.position);i(c);)c=e.input.charCodeAt(++e.position);if(58===c)c=e.input.charCodeAt(++e.position),o(c)||d(e,"a whitespace character is expected after the key-value separator within a block mapping"),v&&(y(e,p,f,m,g,null),m=g=x=null),D=!0,v=!1,s=!1,m=e.tag,g=e.result;else{if(!D)return e.tag=l,e.anchor=h,!0;d(e,"can not read an implicit mapping pair; a colon is missed")}}else{if(!D)return e.tag=l,e.anchor=h,!0;d(e,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===c?(v&&(y(e,p,f,m,g,null),m=g=x=null),D=!0,v=!0,s=!0):v?(v=!1,s=!0):d(e,"incomplete explicit mapping pair; a key node is missed"),e.position+=1,c=r;if((e.line===a||e.lineIndent>t)&&(P(e,t,G,!0,s)&&(v?g=e.result:x=e.result),v||(y(e,p,f,m,g,x,a,u),m=g=x=null),b(e,!0,-1),c=e.input.charCodeAt(e.position)),e.lineIndent>t&&0!==c)d(e,"bad indentation of a mapping entry");else if(e.lineIndent<t)break}return v&&y(e,p,f,m,g,null),D&&(e.tag=l,e.anchor=h,e.kind="mapping",e.result=p),D}function B(e){var t,n,r,i,s=!1,a=!1;if(i=e.input.charCodeAt(e.position),33!==i)return!1;if(null!==e.tag&&d(e,"duplication of a tag property"),i=e.input.charCodeAt(++e.position),60===i?(s=!0,i=e.input.charCodeAt(++e.position)):33===i?(a=!0,n="!!",i=e.input.charCodeAt(++e.position)):n="!",t=e.position,s){do i=e.input.charCodeAt(++e.position);while(0!==i&&62!==i);e.position<e.length?(r=e.input.slice(t,e.position),i=e.input.charCodeAt(++e.position)):d(e,"unexpected end of the stream within a verbatim tag")}else{for(;0!==i&&!o(i);)33===i&&(a?d(e,"tag suffix cannot contain exclamation marks"):(n=e.input.slice(t-1,e.position+1),ne.test(n)||d(e,"named tag handle cannot contain such characters"),a=!0,t=e.position+1)),i=e.input.charCodeAt(++e.position);r=e.input.slice(t,e.position),te.test(r)&&d(e,"tag suffix cannot contain flow indicator characters")}return r&&!re.test(r)&&d(e,"tag name cannot contain such characters: "+r),s?e.tag=r:K.call(e.tagMap,n)?e.tag=e.tagMap[n]+r:"!"===n?e.tag="!"+r:"!!"===n?e.tag="tag:yaml.org,2002:"+r:d(e,'undeclared tag handle "'+n+'"'),!0}function N(e){var t,n;if(n=e.input.charCodeAt(e.position),38!==n)return!1;for(null!==e.anchor&&d(e,"duplication of an anchor property"),n=e.input.charCodeAt(++e.position),t=e.position;0!==n&&!o(n)&&!s(n);)n=e.input.charCodeAt(++e.position);return e.position===t&&d(e,"name of an anchor node must contain at least one character"),e.anchor=e.input.slice(t,e.position),!0}function I(e){var t,n,r;if(r=e.input.charCodeAt(e.position),42!==r)return!1;for(r=e.input.charCodeAt(++e.position),t=e.position;0!==r&&!o(r)&&!s(r);)r=e.input.charCodeAt(++e.position);return e.position===t&&d(e,"name of an alias node must contain at least one character"),n=e.input.slice(t,e.position),e.anchorMap.hasOwnProperty(n)||d(e,'unidentified alias "'+n+'"'),e.result=e.anchorMap[n],b(e,!0,-1),!0}function P(e,t,n,r,i){var o,s,a,u,c,l,h,p,f=1,m=!1,g=!1;if(null!==e.listener&&e.listener("open",e),e.tag=null,e.anchor=null,e.kind=null,e.result=null,o=s=a=G===n||q===n,r&&b(e,!0,-1)&&(m=!0,e.lineIndent>t?f=1:e.lineIndent===t?f=0:e.lineIndent<t&&(f=-1)),1===f)for(;B(e)||N(e);)b(e,!0,-1)?(m=!0,a=o,e.lineIndent>t?f=1:e.lineIndent===t?f=0:e.lineIndent<t&&(f=-1)):a=!1;if(a&&(a=m||i),1!==f&&G!==n||(h=W===n||H===n?t:t+1,p=e.position-e.lineStart,1===f?a&&(F(e,p)||T(e,p,h))||C(e,h)?g=!0:(s&&S(e,h)||k(e,h)||A(e,h)?g=!0:I(e)?(g=!0,null===e.tag&&null===e.anchor||d(e,"alias node should not have any properties")):E(e,h,W===n)&&(g=!0,null===e.tag&&(e.tag="?")),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):0===f&&(g=a&&F(e,p))),null!==e.tag&&"!"!==e.tag)if("?"===e.tag){for(u=0,c=e.implicitTypes.length;u<c;u+=1)if(l=e.implicitTypes[u],l.resolve(e.result)){e.result=l.construct(e.result),e.tag=l.tag,null!==e.anchor&&(e.anchorMap[e.anchor]=e.result);break}}else K.call(e.typeMap[e.kind||"fallback"],e.tag)?(l=e.typeMap[e.kind||"fallback"][e.tag],null!==e.result&&l.kind!==e.kind&&d(e,"unacceptable node kind for !<"+e.tag+'> tag; it should be "'+l.kind+'", not "'+e.kind+'"'),l.resolve(e.result)?(e.result=l.construct(e.result),null!==e.anchor&&(e.anchorMap[e.anchor]=e.result)):d(e,"cannot resolve a node with !<"+e.tag+"> explicit tag")):d(e,"unknown tag !<"+e.tag+">");return null!==e.listener&&e.listener("close",e),null!==e.tag||null!==e.anchor||g}function M(e){var t,n,s,a,u=e.position,c=!1;for(e.version=null,e.checkLineBreaks=e.legacy,e.tagMap={},e.anchorMap={};0!==(a=e.input.charCodeAt(e.position))&&(b(e,!0,-1),a=e.input.charCodeAt(e.position),!(e.lineIndent>0||37!==a));){for(c=!0,a=e.input.charCodeAt(++e.position),t=e.position;0!==a&&!o(a);)a=e.input.charCodeAt(++e.position);for(n=e.input.slice(t,e.position),s=[],n.length<1&&d(e,"directive name must not be less than one character in length");0!==a;){for(;i(a);)a=e.input.charCodeAt(++e.position);if(35===a){do a=e.input.charCodeAt(++e.position);while(0!==a&&!r(a));break}if(r(a))break;for(t=e.position;0!==a&&!o(a);)a=e.input.charCodeAt(++e.position);s.push(e.input.slice(t,e.position))}0!==a&&v(e),K.call(ae,n)?ae[n](e,n,s):m(e,'unknown document directive "'+n+'"')}return b(e,!0,-1),0===e.lineIndent&&45===e.input.charCodeAt(e.position)&&45===e.input.charCodeAt(e.position+1)&&45===e.input.charCodeAt(e.position+2)?(e.position+=3,b(e,!0,-1)):c&&d(e,"directives end mark is expected"),P(e,e.lineIndent-1,G,!1,!0),b(e,!0,-1),e.checkLineBreaks&&ee.test(e.input.slice(u,e.position))&&m(e,"non-ASCII line breaks are interpreted as content"),e.documents.push(e.result),e.position===e.lineStart&&D(e)?void(46===e.input.charCodeAt(e.position)&&(e.position+=3,b(e,!0,-1))):void(e.position<e.length-1&&d(e,"end of the stream or a document separator is expected"))}function O(e,t){e=String(e),t=t||{},0!==e.length&&(10!==e.charCodeAt(e.length-1)&&13!==e.charCodeAt(e.length-1)&&(e+="\n"),65279===e.charCodeAt(0)&&(e=e.slice(1)));var n=new p(e,t);for(n.input+="\0";32===n.input.charCodeAt(n.position);)n.lineIndent+=1,n.position+=1;for(;n.position<n.length-1;)M(n);return n.documents}function _(e,t,n){var r,i,o=O(e,n);for(r=0,i=o.length;r<i;r+=1)t(o[r])}function L(e,t){var n=O(e,t);if(0!==n.length){if(1===n.length)return n[0];throw new z("expected a single document in the stream, but found more")}}function R(e,t,n){_(e,t,j.extend({schema:X},n))}function U(e,t){return L(e,j.extend({schema:X},t))}for(var j=n(185),z=n(186),J=n(187),X=n(188),Y=n(211),K=Object.prototype.hasOwnProperty,W=1,H=2,q=3,G=4,V=1,$=2,Z=3,Q=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,ee=/[\x85\u2028\u2029]/,te=/[,\[\]\{\}]/,ne=/^(?:!|!!|![a-z\-]+!)$/i,re=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i,ie=new Array(256),oe=new Array(256),se=0;se<256;se++)ie[se]=l(se)?1:0,oe[se]=l(se);var ae={YAML:function(e,t,n){var r,i,o;null!==e.version&&d(e,"duplication of %YAML directive"),1!==n.length&&d(e,"YAML directive accepts exactly one argument"),r=/^([0-9]+)\.([0-9]+)$/.exec(n[0]),null===r&&d(e,"ill-formed argument of the YAML directive"),i=parseInt(r[1],10),o=parseInt(r[2],10),1!==i&&d(e,"unacceptable YAML version of the document"),e.version=n[0],e.checkLineBreaks=o<2,1!==o&&2!==o&&m(e,"unsupported YAML version of the document")},TAG:function(e,t,n){var r,i;2!==n.length&&d(e,"TAG directive accepts exactly two arguments"),r=n[0],i=n[1],ne.test(r)||d(e,"ill-formed tag handle (first argument) of the TAG directive"),K.call(e.tagMap,r)&&d(e,'there is a previously declared suffix for "'+r+'" tag handle'),re.test(i)||d(e,"ill-formed tag prefix (second argument) of the TAG directive"),e.tagMap[r]=i}};e.exports.loadAll=_,e.exports.load=L,e.exports.safeLoadAll=R,e.exports.safeLoad=U},function(e,t){"use strict";function n(e){return"undefined"==typeof e||null===e}function r(e){return"object"==typeof e&&null!==e}function i(e){return Array.isArray(e)?e:n(e)?[]:[e]}function o(e,t){var n,r,i,o;if(t)for(o=Object.keys(t),n=0,r=o.length;n<r;n+=1)i=o[n],e[i]=t[i];return e}function s(e,t){var n,r="";for(n=0;n<t;n+=1)r+=e;return r}function a(e){return 0===e&&Number.NEGATIVE_INFINITY===1/e}e.exports.isNothing=n,e.exports.isObject=r,e.exports.toArray=i,e.exports.repeat=s,e.exports.isNegativeZero=a,e.exports.extend=o},function(e,t){"use strict";function n(e,t){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||"",this.name="YAMLException",this.reason=e,this.mark=t,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():"")}n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n.prototype.toString=function(e){var t=this.name+": ";return t+=this.reason||"(unknown reason)",!e&&this.mark&&(t+=" "+this.mark.toString()),t},e.exports=n},function(e,t,n){"use strict";function r(e,t,n,r,i){this.name=e,this.buffer=t,this.position=n,this.line=r,this.column=i}var i=n(185);r.prototype.getSnippet=function(e,t){var n,r,o,s,a;if(!this.buffer)return null;for(e=e||4,t=t||75,n="",r=this.position;r>0&&"\0\r\n\u2028\u2029".indexOf(this.buffer.charAt(r-1))===-1;)if(r-=1,this.position-r>t/2-1){n=" ... ",r+=5;break}for(o="",s=this.position;s<this.buffer.length&&"\0\r\n\u2028\u2029".indexOf(this.buffer.charAt(s))===-1;)if(s+=1,s-this.position>t/2-1){o=" ... ",s-=5;break}return a=this.buffer.slice(r,s),i.repeat(" ",e)+n+a+o+"\n"+i.repeat(" ",e+this.position-r+n.length)+"^"},r.prototype.toString=function(e){var t,n="";return this.name&&(n+='in "'+this.name+'" '),n+="at line "+(this.line+1)+", column "+(this.column+1),e||(t=this.getSnippet(),t&&(n+=":\n"+t)),n},e.exports=r},function(e,t,n){"use strict";var r=n(189);e.exports=new r({include:[n(191)],implicit:[n(201),n(202)],explicit:[n(203),n(208),n(209),n(210)]})},function(e,t,n){"use strict";function r(e,t,n){var i=[];return e.include.forEach(function(e){n=r(e,t,n)}),e[t].forEach(function(e){n.forEach(function(t,n){t.tag===e.tag&&t.kind===e.kind&&i.push(n)}),n.push(e)}),n.filter(function(e,t){return i.indexOf(t)===-1})}function i(){function e(e){r[e.kind][e.tag]=r.fallback[e.tag]=e}var t,n,r={scalar:{},sequence:{},mapping:{},fallback:{}};for(t=0,n=arguments.length;t<n;t+=1)arguments[t].forEach(e);return r}function o(e){this.include=e.include||[],this.implicit=e.implicit||[],this.explicit=e.explicit||[],this.implicit.forEach(function(e){if(e.loadKind&&"scalar"!==e.loadKind)throw new a("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=r(this,"implicit",[]),this.compiledExplicit=r(this,"explicit",[]),this.compiledTypeMap=i(this.compiledImplicit,this.compiledExplicit)}var s=n(185),a=n(186),u=n(190);o.DEFAULT=null,o.create=function(){var e,t;switch(arguments.length){case 1:e=o.DEFAULT,t=arguments[0];break;case 2:e=arguments[0],t=arguments[1];break;default:throw new a("Wrong number of arguments for Schema.create function")}if(e=s.toArray(e),t=s.toArray(t),!e.every(function(e){return e instanceof o}))throw new a("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!t.every(function(e){return e instanceof u}))throw new a("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new o({include:e,explicit:t})},e.exports=o},function(e,t,n){"use strict";function r(e){var t={};return null!==e&&Object.keys(e).forEach(function(n){e[n].forEach(function(e){t[String(e)]=n})}),t}function i(e,t){if(t=t||{},Object.keys(t).forEach(function(t){if(s.indexOf(t)===-1)throw new o('Unknown option "'+t+'" is met in definition of "'+e+'" YAML type.')}),this.tag=e,this.kind=t.kind||null,this.resolve=t.resolve||function(){return!0},this.construct=t.construct||function(e){return e},this.instanceOf=t.instanceOf||null,this.predicate=t.predicate||null,this.represent=t.represent||null,this.defaultStyle=t.defaultStyle||null,this.styleAliases=r(t.styleAliases||null),a.indexOf(this.kind)===-1)throw new o('Unknown kind "'+this.kind+'" is specified for "'+e+'" YAML type.')}var o=n(186),s=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],a=["scalar","sequence","mapping"];e.exports=i},function(e,t,n){"use strict";var r=n(189);e.exports=new r({include:[n(192)]})},function(e,t,n){"use strict";var r=n(189);e.exports=new r({include:[n(193)],implicit:[n(197),n(198),n(199),n(200)]})},function(e,t,n){"use strict";var r=n(189);e.exports=new r({explicit:[n(194),n(195),n(196)]})},function(e,t,n){"use strict";var r=n(190);e.exports=new r("tag:yaml.org,2002:str",{kind:"scalar",construct:function(e){return null!==e?e:""}})},function(e,t,n){"use strict";var r=n(190);e.exports=new r("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(e){return null!==e?e:[]}})},function(e,t,n){"use strict";var r=n(190);e.exports=new r("tag:yaml.org,2002:map",{kind:"mapping",construct:function(e){return null!==e?e:{}}})},function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t=e.length;return 1===t&&"~"===e||4===t&&("null"===e||"Null"===e||"NULL"===e)}function i(){return null}function o(e){return null===e}var s=n(190);e.exports=new s("tag:yaml.org,2002:null",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})},function(e,t,n){"use strict";function r(e){if(null===e)return!1;var t=e.length;return 4===t&&("true"===e||"True"===e||"TRUE"===e)||5===t&&("false"===e||"False"===e||"FALSE"===e)}function i(e){return"true"===e||"True"===e||"TRUE"===e}function o(e){return"[object Boolean]"===Object.prototype.toString.call(e)}var s=n(190);e.exports=new s("tag:yaml.org,2002:bool",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:{lowercase:function(e){return e?"true":"false"},uppercase:function(e){return e?"TRUE":"FALSE"},camelcase:function(e){return e?"True":"False"}},defaultStyle:"lowercase"})},function(e,t,n){"use strict";function r(e){return 48<=e&&e<=57||65<=e&&e<=70||97<=e&&e<=102}function i(e){return 48<=e&&e<=55}function o(e){return 48<=e&&e<=57}function s(e){if(null===e)return!1;var t,n=e.length,s=0,a=!1;if(!n)return!1;if(t=e[s],"-"!==t&&"+"!==t||(t=e[++s]),"0"===t){if(s+1===n)return!0;if(t=e[++s],"b"===t){for(s++;s<n;s++)if(t=e[s],"_"!==t){if("0"!==t&&"1"!==t)return!1;a=!0}return a&&"_"!==t}if("x"===t){for(s++;s<n;s++)if(t=e[s],"_"!==t){if(!r(e.charCodeAt(s)))return!1;a=!0}return a&&"_"!==t}for(;s<n;s++)if(t=e[s],"_"!==t){if(!i(e.charCodeAt(s)))return!1;a=!0}return a&&"_"!==t}if("_"===t)return!1;for(;s<n;s++)if(t=e[s],"_"!==t){if(":"===t)break;if(!o(e.charCodeAt(s)))return!1;a=!0}return!(!a||"_"===t)&&(":"!==t||/^(:[0-5]?[0-9])+$/.test(e.slice(s)))}function a(e){var t,n,r=e,i=1,o=[];return r.indexOf("_")!==-1&&(r=r.replace(/_/g,"")),t=r[0],"-"!==t&&"+"!==t||("-"===t&&(i=-1),r=r.slice(1),t=r[0]),"0"===r?0:"0"===t?"b"===r[1]?i*parseInt(r.slice(2),2):"x"===r[1]?i*parseInt(r,16):i*parseInt(r,8):r.indexOf(":")!==-1?(r.split(":").forEach(function(e){o.unshift(parseInt(e,10))}),r=0,n=1,o.forEach(function(e){r+=e*n,n*=60}),i*r):i*parseInt(r,10)}function u(e){return"[object Number]"===Object.prototype.toString.call(e)&&e%1===0&&!c.isNegativeZero(e)}var c=n(185),l=n(190);e.exports=new l("tag:yaml.org,2002:int",{kind:"scalar",resolve:s,construct:a,predicate:u,represent:{binary:function(e){return"0b"+e.toString(2)},octal:function(e){return"0"+e.toString(8)},decimal:function(e){
return e.toString(10)},hexadecimal:function(e){return"0x"+e.toString(16).toUpperCase()}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},function(e,t,n){"use strict";function r(e){return null!==e&&!(!c.test(e)||"_"===e[e.length-1])}function i(e){var t,n,r,i;return t=e.replace(/_/g,"").toLowerCase(),n="-"===t[0]?-1:1,i=[],"+-".indexOf(t[0])>=0&&(t=t.slice(1)),".inf"===t?1===n?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===t?NaN:t.indexOf(":")>=0?(t.split(":").forEach(function(e){i.unshift(parseFloat(e,10))}),t=0,r=1,i.forEach(function(e){t+=e*r,r*=60}),n*t):n*parseFloat(t,10)}function o(e,t){var n;if(isNaN(e))switch(t){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===e)switch(t){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===e)switch(t){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(a.isNegativeZero(e))return"-0.0";return n=e.toString(10),l.test(n)?n.replace("e",".e"):n}function s(e){return"[object Number]"===Object.prototype.toString.call(e)&&(e%1!==0||a.isNegativeZero(e))}var a=n(185),u=n(190),c=new RegExp("^(?:[-+]?(?:0|[1-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),l=/^[-+]?[0-9]+e/;e.exports=new u("tag:yaml.org,2002:float",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:o,defaultStyle:"lowercase"})},function(e,t,n){"use strict";function r(e){return null!==e&&(null!==a.exec(e)||null!==u.exec(e))}function i(e){var t,n,r,i,o,s,c,l,h,p,f=0,d=null;if(t=a.exec(e),null===t&&(t=u.exec(e)),null===t)throw new Error("Date resolve error");if(n=+t[1],r=+t[2]-1,i=+t[3],!t[4])return new Date(Date.UTC(n,r,i));if(o=+t[4],s=+t[5],c=+t[6],t[7]){for(f=t[7].slice(0,3);f.length<3;)f+="0";f=+f}return t[9]&&(l=+t[10],h=+(t[11]||0),d=6e4*(60*l+h),"-"===t[9]&&(d=-d)),p=new Date(Date.UTC(n,r,i,o,s,c,f)),d&&p.setTime(p.getTime()-d),p}function o(e){return e.toISOString()}var s=n(190),a=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),u=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");e.exports=new s("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:r,construct:i,instanceOf:Date,represent:o})},function(e,t,n){"use strict";function r(e){return"<<"===e||null===e}var i=n(190);e.exports=new i("tag:yaml.org,2002:merge",{kind:"scalar",resolve:r})},function(e,t,n){function r(e){if(null===e)return!1;var t,n,r=0,i=e.length,o=c;for(n=0;n<i;n++)if(t=o.indexOf(e.charAt(n)),!(t>64)){if(t<0)return!1;r+=6}return r%8===0}function i(e){var t,n,r=e.replace(/[\r\n=]/g,""),i=r.length,o=c,s=0,u=[];for(t=0;t<i;t++)t%4===0&&t&&(u.push(s>>16&255),u.push(s>>8&255),u.push(255&s)),s=s<<6|o.indexOf(r.charAt(t));return n=i%4*6,0===n?(u.push(s>>16&255),u.push(s>>8&255),u.push(255&s)):18===n?(u.push(s>>10&255),u.push(s>>2&255)):12===n&&u.push(s>>4&255),a?a.from?a.from(u):new a(u):u}function o(e){var t,n,r="",i=0,o=e.length,s=c;for(t=0;t<o;t++)t%3===0&&t&&(r+=s[i>>18&63],r+=s[i>>12&63],r+=s[i>>6&63],r+=s[63&i]),i=(i<<8)+e[t];return n=o%3,0===n?(r+=s[i>>18&63],r+=s[i>>12&63],r+=s[i>>6&63],r+=s[63&i]):2===n?(r+=s[i>>10&63],r+=s[i>>4&63],r+=s[i<<2&63],r+=s[64]):1===n&&(r+=s[i>>2&63],r+=s[i<<4&63],r+=s[64],r+=s[64]),r}function s(e){return a&&a.isBuffer(e)}var a;try{a=n(204).Buffer}catch(e){}var u=n(190),c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/=\n\r";e.exports=new u("tag:yaml.org,2002:binary",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:o})},function(e,t,n){(function(e){/*!
	 * The buffer module from node.js, for the browser.
	 *
	 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
	 * @license  MIT
	 */
"use strict";function r(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}function i(){return s.TYPED_ARRAY_SUPPORT?**********:**********}function o(e,t){if(i()<t)throw new RangeError("Invalid typed array length");return s.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t),e.__proto__=s.prototype):(null===e&&(e=new s(t)),e.length=t),e}function s(e,t,n){if(!(s.TYPED_ARRAY_SUPPORT||this instanceof s))return new s(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return l(this,e)}return a(this,e,t,n)}function a(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?f(e,t,n,r):"string"==typeof t?h(e,t,n):d(e,t)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function c(e,t,n,r){return u(t),t<=0?o(e,t):void 0!==n?"string"==typeof r?o(e,t).fill(n,r):o(e,t).fill(n):o(e,t)}function l(e,t){if(u(t),e=o(e,t<0?0:0|m(t)),!s.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function h(e,t,n){if("string"==typeof n&&""!==n||(n="utf8"),!s.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|x(t,n);e=o(e,r);var i=e.write(t,n);return i!==r&&(e=e.slice(0,i)),e}function p(e,t){var n=t.length<0?0:0|m(t.length);e=o(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function f(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r),s.TYPED_ARRAY_SUPPORT?(e=t,e.__proto__=s.prototype):e=p(e,t),e}function d(e,t){if(s.isBuffer(t)){var n=0|m(t.length);return e=o(e,n),0===e.length?e:(t.copy(e,0,0,n),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||V(t.length)?o(e,0):p(e,t);if("Buffer"===t.type&&Q(t.data))return p(e,t.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function m(e){if(e>=i())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+i().toString(16)+" bytes");return 0|e}function g(e){return+e!=e&&(e=0),s.alloc(+e)}function x(e,t){if(s.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return K(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return q(e).length;default:if(r)return K(e).length;t=(""+t).toLowerCase(),r=!0}}function y(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return P(this,t,n);case"utf8":case"utf-8":return T(this,t,n);case"ascii":return N(this,t,n);case"latin1":case"binary":return I(this,t,n);case"base64":return F(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return M(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function v(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function b(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=s.from(t,r)),s.isBuffer(t))return 0===t.length?-1:D(e,t,n,r,i);if("number"==typeof t)return t&=255,s.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):D(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function D(e,t,n,r,i){function o(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}var s=1,a=e.length,u=t.length;if(void 0!==r&&(r=String(r).toLowerCase(),"ucs2"===r||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,n/=2}var c;if(i){var l=-1;for(c=n;c<a;c++)if(o(e,c)===o(t,l===-1?0:c-l)){if(l===-1&&(l=c),c-l+1===u)return l*s}else l!==-1&&(c-=c-l),l=-1}else for(n+u>a&&(n=a-u),c=n;c>=0;c--){for(var h=!0,p=0;p<u;p++)if(o(e,c+p)!==o(t,p)){h=!1;break}if(h)return c}return-1}function w(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;var o=t.length;if(o%2!==0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var s=0;s<r;++s){var a=parseInt(t.substr(2*s,2),16);if(isNaN(a))return s;e[n+s]=a}return s}function E(e,t,n,r){return G(K(t,e.length-n),e,n,r)}function k(e,t,n,r){return G(W(t),e,n,r)}function A(e,t,n,r){return k(e,t,n,r)}function C(e,t,n,r){return G(q(t),e,n,r)}function S(e,t,n,r){return G(H(t,e.length-n),e,n,r)}function F(e,t,n){return 0===t&&n===e.length?$.fromByteArray(e):$.fromByteArray(e.slice(t,n))}function T(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o=e[i],s=null,a=o>239?4:o>223?3:o>191?2:1;if(i+a<=n){var u,c,l,h;switch(a){case 1:o<128&&(s=o);break;case 2:u=e[i+1],128===(192&u)&&(h=(31&o)<<6|63&u,h>127&&(s=h));break;case 3:u=e[i+1],c=e[i+2],128===(192&u)&&128===(192&c)&&(h=(15&o)<<12|(63&u)<<6|63&c,h>2047&&(h<55296||h>57343)&&(s=h));break;case 4:u=e[i+1],c=e[i+2],l=e[i+3],128===(192&u)&&128===(192&c)&&128===(192&l)&&(h=(15&o)<<18|(63&u)<<12|(63&c)<<6|63&l,h>65535&&h<1114112&&(s=h))}}null===s?(s=65533,a=1):s>65535&&(s-=65536,r.push(s>>>10&1023|55296),s=56320|1023&s),r.push(s),i+=a}return B(r)}function B(e){var t=e.length;if(t<=ee)return String.fromCharCode.apply(String,e);for(var n="",r=0;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=ee));return n}function N(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function I(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function P(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=Y(e[o]);return i}function M(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function O(e,t,n){if(e%1!==0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function _(e,t,n,r,i,o){if(!s.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function L(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function R(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function U(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function j(e,t,n,r,i){return i||U(e,t,n,4,3.4028234663852886e38,-3.4028234663852886e38),Z.write(e,t,n,r,23,4),n+4}function z(e,t,n,r,i){return i||U(e,t,n,8,1.7976931348623157e308,-1.7976931348623157e308),Z.write(e,t,n,r,52,8),n+8}function J(e){if(e=X(e).replace(te,""),e.length<2)return"";for(;e.length%4!==0;)e+="=";return e}function X(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}function Y(e){return e<16?"0"+e.toString(16):e.toString(16)}function K(e,t){t=t||1/0;for(var n,r=e.length,i=null,o=[],s=0;s<r;++s){if(n=e.charCodeAt(s),n>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=(i-55296<<10|n-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function W(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}function H(e,t){for(var n,r,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)n=e.charCodeAt(s),r=n>>8,i=n%256,o.push(i),o.push(r);return o}function q(e){return $.toByteArray(J(e))}function G(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function V(e){return e!==e}var $=n(205),Z=n(206),Q=n(207);t.Buffer=s,t.SlowBuffer=g,t.INSPECT_MAX_BYTES=50,s.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:r(),t.kMaxLength=i(),s.poolSize=8192,s._augment=function(e){return e.__proto__=s.prototype,e},s.from=function(e,t,n){return a(null,e,t,n)},s.TYPED_ARRAY_SUPPORT&&(s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0})),s.alloc=function(e,t,n){return c(null,e,t,n)},s.allocUnsafe=function(e){return l(null,e)},s.allocUnsafeSlow=function(e){return l(null,e)},s.isBuffer=function(e){return!(null==e||!e._isBuffer)},s.compare=function(e,t){if(!s.isBuffer(e)||!s.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},s.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(e,t){if(!Q(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return s.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=s.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var o=e[n];if(!s.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},s.byteLength=x,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)v(this,t,t+1);return this},s.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)v(this,t,t+3),v(this,t+1,t+2);return this},s.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)v(this,t,t+7),v(this,t+1,t+6),v(this,t+2,t+5),v(this,t+3,t+4);return this},s.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?T(this,0,e):y.apply(this,arguments)},s.prototype.equals=function(e){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===s.compare(this,e)},s.prototype.inspect=function(){var e="",n=t.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(e+=" ... ")),"<Buffer "+e+">"},s.prototype.compare=function(e,t,n,r,i){if(!s.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,i>>>=0,this===e)return 0;for(var o=i-r,a=n-t,u=Math.min(o,a),c=this.slice(r,i),l=e.slice(t,n),h=0;h<u;++h)if(c[h]!==l[h]){o=c[h],a=l[h];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1},s.prototype.indexOf=function(e,t,n){return b(this,e,t,n,!0)},s.prototype.lastIndexOf=function(e,t,n){return b(this,e,t,n,!1)},s.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return w(this,e,t,n);case"utf8":case"utf-8":return E(this,e,t,n);case"ascii":return k(this,e,t,n);case"latin1":case"binary":return A(this,e,t,n);case"base64":return C(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var ee=4096;s.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);var r;if(s.TYPED_ARRAY_SUPPORT)r=this.subarray(e,t),r.__proto__=s.prototype;else{var i=t-e;r=new s(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},s.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||O(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},s.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||O(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},s.prototype.readUInt8=function(e,t){return t||O(e,1,this.length),this[e]},s.prototype.readUInt16LE=function(e,t){return t||O(e,2,this.length),this[e]|this[e+1]<<8},s.prototype.readUInt16BE=function(e,t){return t||O(e,2,this.length),this[e]<<8|this[e+1]},s.prototype.readUInt32LE=function(e,t){return t||O(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},s.prototype.readUInt32BE=function(e,t){return t||O(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},s.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||O(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},s.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||O(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*t)),o},s.prototype.readInt8=function(e,t){return t||O(e,1,this.length),128&this[e]?(255-this[e]+1)*-1:this[e]},s.prototype.readInt16LE=function(e,t){t||O(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(e,t){t||O(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(e,t){return t||O(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},s.prototype.readInt32BE=function(e,t){return t||O(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},s.prototype.readFloatLE=function(e,t){return t||O(e,4,this.length),Z.read(this,e,!0,23,4)},s.prototype.readFloatBE=function(e,t){return t||O(e,4,this.length),Z.read(this,e,!1,23,4)},s.prototype.readDoubleLE=function(e,t){return t||O(e,8,this.length),Z.read(this,e,!0,52,8)},s.prototype.readDoubleBE=function(e,t){return t||O(e,8,this.length),Z.read(this,e,!1,52,8)},s.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;_(this,e,t,n,i,0)}var o=1,s=0;for(this[t]=255&e;++s<n&&(o*=256);)this[t+s]=e/o&255;return t+n},s.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;_(this,e,t,n,i,0)}var o=n-1,s=1;for(this[t+o]=255&e;--o>=0&&(s*=256);)this[t+o]=e/s&255;return t+n},s.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,1,255,0),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},s.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},s.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},s.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):R(this,e,t,!0),t+4},s.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):R(this,e,t,!1),t+4},s.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);_(this,e,t,n,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<n&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+n},s.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);_(this,e,t,n,i-1,-i)}var o=n-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+n},s.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,1,127,-128),s.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},s.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):L(this,e,t,!0),t+2},s.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):L(this,e,t,!1),t+2},s.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,4,**********,-2147483648),s.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):R(this,e,t,!0),t+4},s.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||_(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),s.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):R(this,e,t,!1),t+4},s.prototype.writeFloatLE=function(e,t,n){return j(this,e,t,!0,n)},s.prototype.writeFloatBE=function(e,t,n){return j(this,e,t,!1,n)},s.prototype.writeDoubleLE=function(e,t,n){return z(this,e,t,!0,n)},s.prototype.writeDoubleBE=function(e,t,n){return z(this,e,t,!1,n)},s.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,o=r-n;if(this===e&&n<t&&t<r)for(i=o-1;i>=0;--i)e[i+t]=this[i+n];else if(o<1e3||!s.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},s.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var o;if("number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=s.isBuffer(e)?e:K(new s(e,r).toString()),u=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%u]}return this};var te=/[^+\/0-9A-Za-z-_]/g}).call(t,function(){return this}())},function(e,t){"use strict";function n(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function r(e){return 3*e.length/4-n(e)}function i(e){var t,r,i,o,s,a=e.length;o=n(e),s=new l(3*a/4-o),r=o>0?a-4:a;var u=0;for(t=0;t<r;t+=4)i=c[e.charCodeAt(t)]<<18|c[e.charCodeAt(t+1)]<<12|c[e.charCodeAt(t+2)]<<6|c[e.charCodeAt(t+3)],s[u++]=i>>16&255,s[u++]=i>>8&255,s[u++]=255&i;return 2===o?(i=c[e.charCodeAt(t)]<<2|c[e.charCodeAt(t+1)]>>4,s[u++]=255&i):1===o&&(i=c[e.charCodeAt(t)]<<10|c[e.charCodeAt(t+1)]<<4|c[e.charCodeAt(t+2)]>>2,s[u++]=i>>8&255,s[u++]=255&i),s}function o(e){return u[e>>18&63]+u[e>>12&63]+u[e>>6&63]+u[63&e]}function s(e,t,n){for(var r,i=[],s=t;s<n;s+=3)r=(e[s]<<16)+(e[s+1]<<8)+e[s+2],i.push(o(r));return i.join("")}function a(e){for(var t,n=e.length,r=n%3,i="",o=[],a=16383,c=0,l=n-r;c<l;c+=a)o.push(s(e,c,c+a>l?l:c+a));return 1===r?(t=e[n-1],i+=u[t>>2],i+=u[t<<4&63],i+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],i+=u[t>>10],i+=u[t>>4&63],i+=u[t<<2&63],i+="="),o.push(i),o.join("")}t.byteLength=r,t.toByteArray=i,t.fromByteArray=a;for(var u=[],c=[],l="undefined"!=typeof Uint8Array?Uint8Array:Array,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/",p=0,f=h.length;p<f;++p)u[p]=h[p],c[h.charCodeAt(p)]=p;c["-".charCodeAt(0)]=62,c["_".charCodeAt(0)]=63},function(e,t){t.read=function(e,t,n,r,i){var o,s,a=8*i-r-1,u=(1<<a)-1,c=u>>1,l=-7,h=n?i-1:0,p=n?-1:1,f=e[t+h];for(h+=p,o=f&(1<<-l)-1,f>>=-l,l+=a;l>0;o=256*o+e[t+h],h+=p,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=r;l>0;s=256*s+e[t+h],h+=p,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:(f?-1:1)*(1/0);s+=Math.pow(2,r),o-=c}return(f?-1:1)*s*Math.pow(2,o-r)},t.write=function(e,t,n,r,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,h=l>>1,p=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=r?0:o-1,d=r?1:-1,m=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),t+=s+h>=1?p/u:p*Math.pow(2,1-h),t*u>=2&&(s++,u/=2),s+h>=l?(a=0,s=l):s+h>=1?(a=(t*u-1)*Math.pow(2,i),s+=h):(a=t*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;e[n+f]=255&a,f+=d,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[n+f]=255&s,f+=d,s/=256,c-=8);e[n+f-d]|=128*m}},function(e,t){var n={}.toString;e.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t,n,r,i,o,u=[],c=e;for(t=0,n=c.length;t<n;t+=1){if(r=c[t],o=!1,"[object Object]"!==a.call(r))return!1;for(i in r)if(s.call(r,i)){if(o)return!1;o=!0}if(!o)return!1;if(u.indexOf(i)!==-1)return!1;u.push(i)}return!0}function i(e){return null!==e?e:[]}var o=n(190),s=Object.prototype.hasOwnProperty,a=Object.prototype.toString;e.exports=new o("tag:yaml.org,2002:omap",{kind:"sequence",resolve:r,construct:i})},function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t,n,r,i,o,a=e;for(o=new Array(a.length),t=0,n=a.length;t<n;t+=1){if(r=a[t],"[object Object]"!==s.call(r))return!1;if(i=Object.keys(r),1!==i.length)return!1;o[t]=[i[0],r[i[0]]]}return!0}function i(e){if(null===e)return[];var t,n,r,i,o,s=e;for(o=new Array(s.length),t=0,n=s.length;t<n;t+=1)r=s[t],i=Object.keys(r),o[t]=[i[0],r[i[0]]];return o}var o=n(190),s=Object.prototype.toString;e.exports=new o("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:r,construct:i})},function(e,t,n){"use strict";function r(e){if(null===e)return!0;var t,n=e;for(t in n)if(s.call(n,t)&&null!==n[t])return!1;return!0}function i(e){return null!==e?e:{}}var o=n(190),s=Object.prototype.hasOwnProperty;e.exports=new o("tag:yaml.org,2002:set",{kind:"mapping",resolve:r,construct:i})},function(e,t,n){"use strict";var r=n(189);e.exports=r.DEFAULT=new r({include:[n(188)],explicit:[n(212),n(213),n(214)]})},function(e,t,n){"use strict";function r(){return!0}function i(){}function o(){return""}function s(e){return"undefined"==typeof e}var a=n(190);e.exports=new a("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:o})},function(e,t,n){"use strict";function r(e){if(null===e)return!1;if(0===e.length)return!1;var t=e,n=/\/([gim]*)$/.exec(e),r="";if("/"===t[0]){if(n&&(r=n[1]),r.length>3)return!1;if("/"!==t[t.length-r.length-1])return!1}return!0}function i(e){var t=e,n=/\/([gim]*)$/.exec(e),r="";return"/"===t[0]&&(n&&(r=n[1]),t=t.slice(1,t.length-r.length-1)),new RegExp(t,r)}function o(e){var t="/"+e.source+"/";return e.global&&(t+="g"),e.multiline&&(t+="m"),e.ignoreCase&&(t+="i"),t}function s(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var a=n(190);e.exports=new a("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:o})},function(e,t,n){function r(e){if(null===e)return!1;try{var t="("+e+")",n=a.parse(t,{range:!0});return"Program"===n.type&&1===n.body.length&&"ExpressionStatement"===n.body[0].type&&"FunctionExpression"===n.body[0].expression.type}catch(e){return!1}}function i(e){var t,n="("+e+")",r=a.parse(n,{range:!0}),i=[];if("Program"!==r.type||1!==r.body.length||"ExpressionStatement"!==r.body[0].type||"FunctionExpression"!==r.body[0].expression.type)throw new Error("Failed to resolve function");return r.body[0].expression.params.forEach(function(e){i.push(e.name)}),t=r.body[0].expression.body.range,new Function(i,n.slice(t[0]+1,t[1]-1))}function o(e){return e.toString()}function s(e){return"[object Function]"===Object.prototype.toString.call(e)}var a;try{a=n(215)}catch(e){"undefined"!=typeof window&&(a=window.esprima)}var u=n(190);e.exports=new u("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:o})},function(e,t,n){!function(t,n){e.exports=n()}(this,function(){return function(e){function t(r){if(n[r])return n[r].exports;var i=n[r]={exports:{},id:r,loaded:!1};return e[r].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var n={};return t.m=e,t.c=n,t.p="",t(0)}([function(e,t,n){"use strict";function r(e,t,n){var r=null,i=function(e,t){n&&n(e,t),r&&r.visit(e,t)},u="function"==typeof n?i:null,c=!1;if(t){c="boolean"==typeof t.comment&&t.comment;var l="boolean"==typeof t.attachComment&&t.attachComment;(c||l)&&(r=new o.CommentHandler,r.attach=l,t.comment=!0,u=i)}var h;h=t&&"boolean"==typeof t.jsx&&t.jsx?new a.JSXParser(e,t,u):new s.Parser(e,t,u);var p=h.parseProgram();return c&&(p.comments=r.comments),h.config.tokens&&(p.tokens=h.tokens),h.config.tolerant&&(p.errors=h.errorHandler.errors),p}function i(e,t,n){var r,i=new u.Tokenizer(e,t);r=[];try{for(;;){var o=i.getNextToken();if(!o)break;n&&(o=n(o)),r.push(o)}}catch(e){i.errorHandler.tolerate(e)}return i.errorHandler.tolerant&&(r.errors=i.errors()),r}var o=n(1),s=n(3),a=n(11),u=n(15);t.parse=r,t.tokenize=i;var c=n(2);t.Syntax=c.Syntax,t.version="3.1.3"},function(e,t,n){"use strict";var r=n(2),i=function(){function e(){this.attach=!1,this.comments=[],this.stack=[],this.leading=[],this.trailing=[]}return e.prototype.insertInnerComments=function(e,t){if(e.type===r.Syntax.BlockStatement&&0===e.body.length){for(var n=[],i=this.leading.length-1;i>=0;--i){var o=this.leading[i];t.end.offset>=o.start&&(n.unshift(o.comment),this.leading.splice(i,1),this.trailing.splice(i,1))}n.length&&(e.innerComments=n)}},e.prototype.findTrailingComments=function(e,t){var n=[];if(this.trailing.length>0){for(var r=this.trailing.length-1;r>=0;--r){var i=this.trailing[r];i.start>=t.end.offset&&n.unshift(i.comment)}return this.trailing.length=0,n}var o=this.stack[this.stack.length-1];if(o&&o.node.trailingComments){var s=o.node.trailingComments[0];s&&s.range[0]>=t.end.offset&&(n=o.node.trailingComments,delete o.node.trailingComments)}return n},e.prototype.findLeadingComments=function(e,t){for(var n,r=[];this.stack.length>0;){var i=this.stack[this.stack.length-1];if(!(i&&i.start>=t.start.offset))break;n=this.stack.pop().node}if(n){for(var o=n.leadingComments?n.leadingComments.length:0,s=o-1;s>=0;--s){var a=n.leadingComments[s];a.range[1]<=t.start.offset&&(r.unshift(a),n.leadingComments.splice(s,1))}return n.leadingComments&&0===n.leadingComments.length&&delete n.leadingComments,r}for(var s=this.leading.length-1;s>=0;--s){var i=this.leading[s];i.start<=t.start.offset&&(r.unshift(i.comment),this.leading.splice(s,1))}return r},e.prototype.visitNode=function(e,t){if(!(e.type===r.Syntax.Program&&e.body.length>0)){this.insertInnerComments(e,t);var n=this.findTrailingComments(e,t),i=this.findLeadingComments(e,t);i.length>0&&(e.leadingComments=i),n.length>0&&(e.trailingComments=n),this.stack.push({node:e,start:t.start.offset})}},e.prototype.visitComment=function(e,t){var n="L"===e.type[0]?"Line":"Block",r={type:n,value:e.value};if(e.range&&(r.range=e.range),e.loc&&(r.loc=e.loc),this.comments.push(r),this.attach){var i={comment:{type:n,value:e.value,range:[t.start.offset,t.end.offset]},start:t.start.offset};e.loc&&(i.comment.loc=e.loc),e.type=n,this.leading.push(i),this.trailing.push(i)}},e.prototype.visit=function(e,t){"LineComment"===e.type?this.visitComment(e,t):"BlockComment"===e.type?this.visitComment(e,t):this.attach&&this.visitNode(e,t)},e}();t.CommentHandler=i},function(e,t){"use strict";t.Syntax={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DoWhileStatement:"DoWhileStatement",DebuggerStatement:"DebuggerStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForOfStatement:"ForOfStatement",ForInStatement:"ForInStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchCase:"SwitchCase",SwitchStatement:"SwitchStatement",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"}},function(e,t,n){"use strict";var r=n(4),i=n(5),o=n(6),s=n(7),a=n(8),u=n(2),c=n(10),l="ArrowParameterPlaceHolder",h=function(){function e(e,t,n){void 0===t&&(t={}),this.config={range:"boolean"==typeof t.range&&t.range,loc:"boolean"==typeof t.loc&&t.loc,source:null,tokens:"boolean"==typeof t.tokens&&t.tokens,comment:"boolean"==typeof t.comment&&t.comment,tolerant:"boolean"==typeof t.tolerant&&t.tolerant},this.config.loc&&t.source&&null!==t.source&&(this.config.source=String(t.source)),this.delegate=n,this.errorHandler=new o.ErrorHandler,this.errorHandler.tolerant=this.config.tolerant,this.scanner=new a.Scanner(e,this.errorHandler),this.scanner.trackComment=this.config.comment,this.operatorPrecedence={")":0,";":0,",":0,"=":0,"]":0,"||":1,"&&":2,
"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":11,"/":11,"%":11},this.sourceType=t&&"module"===t.sourceType?"module":"script",this.lookahead=null,this.hasLineTerminator=!1,this.context={allowIn:!0,allowYield:!0,firstCoverInitializedNameError:null,isAssignmentTarget:!1,isBindingElement:!1,inFunctionBody:!1,inIteration:!1,inSwitch:!1,labelSet:{},strict:"module"===this.sourceType},this.tokens=[],this.startMarker={index:0,lineNumber:this.scanner.lineNumber,lineStart:0},this.lastMarker={index:0,lineNumber:this.scanner.lineNumber,lineStart:0},this.nextToken(),this.lastMarker={index:this.scanner.index,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart}}return e.prototype.throwError=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=Array.prototype.slice.call(arguments,1),o=e.replace(/%(\d)/g,function(e,t){return r.assert(t<i.length,"Message reference must be in range"),i[t]}),s=this.lastMarker.index,a=this.lastMarker.lineNumber,u=this.lastMarker.index-this.lastMarker.lineStart+1;throw this.errorHandler.createError(s,a,u,o)},e.prototype.tolerateError=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var i=Array.prototype.slice.call(arguments,1),o=e.replace(/%(\d)/g,function(e,t){return r.assert(t<i.length,"Message reference must be in range"),i[t]}),s=this.lastMarker.index,a=this.scanner.lineNumber,u=this.lastMarker.index-this.lastMarker.lineStart+1;this.errorHandler.tolerateError(s,a,u,o)},e.prototype.unexpectedTokenError=function(e,t){var n,r=t||i.Messages.UnexpectedToken;if(e?(t||(r=e.type===s.Token.EOF?i.Messages.UnexpectedEOS:e.type===s.Token.Identifier?i.Messages.UnexpectedIdentifier:e.type===s.Token.NumericLiteral?i.Messages.UnexpectedNumber:e.type===s.Token.StringLiteral?i.Messages.UnexpectedString:e.type===s.Token.Template?i.Messages.UnexpectedTemplate:i.Messages.UnexpectedToken,e.type===s.Token.Keyword&&(this.scanner.isFutureReservedWord(e.value)?r=i.Messages.UnexpectedReserved:this.context.strict&&this.scanner.isStrictModeReservedWord(e.value)&&(r=i.Messages.StrictReservedWord))),n=e.type===s.Token.Template?e.value.raw:e.value):n="ILLEGAL",r=r.replace("%0",n),e&&"number"==typeof e.lineNumber){var o=e.start,a=e.lineNumber,u=e.start-this.lastMarker.lineStart+1;return this.errorHandler.createError(o,a,u,r)}var o=this.lastMarker.index,a=this.lastMarker.lineNumber,u=o-this.lastMarker.lineStart+1;return this.errorHandler.createError(o,a,u,r)},e.prototype.throwUnexpectedToken=function(e,t){throw this.unexpectedTokenError(e,t)},e.prototype.tolerateUnexpectedToken=function(e,t){this.errorHandler.tolerate(this.unexpectedTokenError(e,t))},e.prototype.collectComments=function(){if(this.config.comment){var e=this.scanner.scanComments();if(e.length>0&&this.delegate)for(var t=0;t<e.length;++t){var n=e[t],r=void 0;r={type:n.multiLine?"BlockComment":"LineComment",value:this.scanner.source.slice(n.slice[0],n.slice[1])},this.config.range&&(r.range=n.range),this.config.loc&&(r.loc=n.loc);var i={start:{line:n.loc.start.line,column:n.loc.start.column,offset:n.range[0]},end:{line:n.loc.end.line,column:n.loc.end.column,offset:n.range[1]}};this.delegate(r,i)}}else this.scanner.scanComments()},e.prototype.getTokenRaw=function(e){return this.scanner.source.slice(e.start,e.end)},e.prototype.convertToken=function(e){var t;return t={type:s.TokenName[e.type],value:this.getTokenRaw(e)},this.config.range&&(t.range=[e.start,e.end]),this.config.loc&&(t.loc={start:{line:this.startMarker.lineNumber,column:this.startMarker.index-this.startMarker.lineStart},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}),e.regex&&(t.regex=e.regex),t},e.prototype.nextToken=function(){var e=this.lookahead;this.lastMarker.index=this.scanner.index,this.lastMarker.lineNumber=this.scanner.lineNumber,this.lastMarker.lineStart=this.scanner.lineStart,this.collectComments(),this.startMarker.index=this.scanner.index,this.startMarker.lineNumber=this.scanner.lineNumber,this.startMarker.lineStart=this.scanner.lineStart;var t;return t=this.scanner.lex(),this.hasLineTerminator=!(!e||!t)&&e.lineNumber!==t.lineNumber,t&&this.context.strict&&t.type===s.Token.Identifier&&this.scanner.isStrictModeReservedWord(t.value)&&(t.type=s.Token.Keyword),this.lookahead=t,this.config.tokens&&t.type!==s.Token.EOF&&this.tokens.push(this.convertToken(t)),e},e.prototype.nextRegexToken=function(){this.collectComments();var e=this.scanner.scanRegExp();return this.config.tokens&&(this.tokens.pop(),this.tokens.push(this.convertToken(e))),this.lookahead=e,this.nextToken(),e},e.prototype.createNode=function(){return{index:this.startMarker.index,line:this.startMarker.lineNumber,column:this.startMarker.index-this.startMarker.lineStart}},e.prototype.startNode=function(e){return{index:e.start,line:e.lineNumber,column:e.start-e.lineStart}},e.prototype.finalize=function(e,t){if(this.config.range&&(t.range=[e.index,this.lastMarker.index]),this.config.loc&&(t.loc={start:{line:e.line,column:e.column},end:{line:this.lastMarker.lineNumber,column:this.lastMarker.index-this.lastMarker.lineStart}},this.config.source&&(t.loc.source=this.config.source)),this.delegate){var n={start:{line:e.line,column:e.column,offset:e.index},end:{line:this.lastMarker.lineNumber,column:this.lastMarker.index-this.lastMarker.lineStart,offset:this.lastMarker.index}};this.delegate(t,n)}return t},e.prototype.expect=function(e){var t=this.nextToken();t.type===s.Token.Punctuator&&t.value===e||this.throwUnexpectedToken(t)},e.prototype.expectCommaSeparator=function(){if(this.config.tolerant){var e=this.lookahead;e.type===s.Token.Punctuator&&","===e.value?this.nextToken():e.type===s.Token.Punctuator&&";"===e.value?(this.nextToken(),this.tolerateUnexpectedToken(e)):this.tolerateUnexpectedToken(e,i.Messages.UnexpectedToken)}else this.expect(",")},e.prototype.expectKeyword=function(e){var t=this.nextToken();t.type===s.Token.Keyword&&t.value===e||this.throwUnexpectedToken(t)},e.prototype.match=function(e){return this.lookahead.type===s.Token.Punctuator&&this.lookahead.value===e},e.prototype.matchKeyword=function(e){return this.lookahead.type===s.Token.Keyword&&this.lookahead.value===e},e.prototype.matchContextualKeyword=function(e){return this.lookahead.type===s.Token.Identifier&&this.lookahead.value===e},e.prototype.matchAssign=function(){if(this.lookahead.type!==s.Token.Punctuator)return!1;var e=this.lookahead.value;return"="===e||"*="===e||"**="===e||"/="===e||"%="===e||"+="===e||"-="===e||"<<="===e||">>="===e||">>>="===e||"&="===e||"^="===e||"|="===e},e.prototype.isolateCoverGrammar=function(e){var t=this.context.isBindingElement,n=this.context.isAssignmentTarget,r=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var i=e.call(this);return null!==this.context.firstCoverInitializedNameError&&this.throwUnexpectedToken(this.context.firstCoverInitializedNameError),this.context.isBindingElement=t,this.context.isAssignmentTarget=n,this.context.firstCoverInitializedNameError=r,i},e.prototype.inheritCoverGrammar=function(e){var t=this.context.isBindingElement,n=this.context.isAssignmentTarget,r=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var i=e.call(this);return this.context.isBindingElement=this.context.isBindingElement&&t,this.context.isAssignmentTarget=this.context.isAssignmentTarget&&n,this.context.firstCoverInitializedNameError=r||this.context.firstCoverInitializedNameError,i},e.prototype.consumeSemicolon=function(){this.match(";")?this.nextToken():this.hasLineTerminator||(this.lookahead.type===s.Token.EOF||this.match("}")||this.throwUnexpectedToken(this.lookahead),this.lastMarker.index=this.startMarker.index,this.lastMarker.lineNumber=this.startMarker.lineNumber,this.lastMarker.lineStart=this.startMarker.lineStart)},e.prototype.parsePrimaryExpression=function(){var e,t,n,r,o=this.createNode();switch(this.lookahead.type){case s.Token.Identifier:"module"===this.sourceType&&"await"===this.lookahead.value&&this.tolerateUnexpectedToken(this.lookahead),e=this.finalize(o,new c.Identifier(this.nextToken().value));break;case s.Token.NumericLiteral:case s.Token.StringLiteral:this.context.strict&&this.lookahead.octal&&this.tolerateUnexpectedToken(this.lookahead,i.Messages.StrictOctalLiteral),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,n=this.nextToken(),r=this.getTokenRaw(n),e=this.finalize(o,new c.Literal(n.value,r));break;case s.Token.BooleanLiteral:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,n=this.nextToken(),n.value="true"===n.value,r=this.getTokenRaw(n),e=this.finalize(o,new c.Literal(n.value,r));break;case s.Token.NullLiteral:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,n=this.nextToken(),n.value=null,r=this.getTokenRaw(n),e=this.finalize(o,new c.Literal(n.value,r));break;case s.Token.Template:e=this.parseTemplateLiteral();break;case s.Token.Punctuator:switch(t=this.lookahead.value){case"(":this.context.isBindingElement=!1,e=this.inheritCoverGrammar(this.parseGroupExpression);break;case"[":e=this.inheritCoverGrammar(this.parseArrayInitializer);break;case"{":e=this.inheritCoverGrammar(this.parseObjectInitializer);break;case"/":case"/=":this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.scanner.index=this.startMarker.index,n=this.nextRegexToken(),r=this.getTokenRaw(n),e=this.finalize(o,new c.RegexLiteral(n.value,r,n.regex));break;default:this.throwUnexpectedToken(this.nextToken())}break;case s.Token.Keyword:!this.context.strict&&this.context.allowYield&&this.matchKeyword("yield")?e=this.parseIdentifierName():!this.context.strict&&this.matchKeyword("let")?e=this.finalize(o,new c.Identifier(this.nextToken().value)):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.matchKeyword("function")?e=this.parseFunctionExpression():this.matchKeyword("this")?(this.nextToken(),e=this.finalize(o,new c.ThisExpression)):this.matchKeyword("class")?e=this.parseClassExpression():this.throwUnexpectedToken(this.nextToken()));break;default:this.throwUnexpectedToken(this.nextToken())}return e},e.prototype.parseSpreadElement=function(){var e=this.createNode();this.expect("...");var t=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.finalize(e,new c.SpreadElement(t))},e.prototype.parseArrayInitializer=function(){var e=this.createNode(),t=[];for(this.expect("[");!this.match("]");)if(this.match(","))this.nextToken(),t.push(null);else if(this.match("...")){var n=this.parseSpreadElement();this.match("]")||(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.expect(",")),t.push(n)}else t.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.match("]")||this.expect(",");return this.expect("]"),this.finalize(e,new c.ArrayExpression(t))},e.prototype.parsePropertyMethod=function(e){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var t=this.context.strict,n=this.isolateCoverGrammar(this.parseFunctionSourceElements);return this.context.strict&&e.firstRestricted&&this.tolerateUnexpectedToken(e.firstRestricted,e.message),this.context.strict&&e.stricted&&this.tolerateUnexpectedToken(e.stricted,e.message),this.context.strict=t,n},e.prototype.parsePropertyMethodFunction=function(){var e=!1,t=this.createNode(),n=this.context.allowYield;this.context.allowYield=!1;var r=this.parseFormalParameters(),i=this.parsePropertyMethod(r);return this.context.allowYield=n,this.finalize(t,new c.FunctionExpression(null,r.params,i,e))},e.prototype.parseObjectPropertyKey=function(){var e=this.createNode(),t=this.nextToken(),n=null;switch(t.type){case s.Token.StringLiteral:case s.Token.NumericLiteral:this.context.strict&&t.octal&&this.tolerateUnexpectedToken(t,i.Messages.StrictOctalLiteral);var r=this.getTokenRaw(t);n=this.finalize(e,new c.Literal(t.value,r));break;case s.Token.Identifier:case s.Token.BooleanLiteral:case s.Token.NullLiteral:case s.Token.Keyword:n=this.finalize(e,new c.Identifier(t.value));break;case s.Token.Punctuator:"["===t.value?(n=this.isolateCoverGrammar(this.parseAssignmentExpression),this.expect("]")):this.throwUnexpectedToken(t);break;default:this.throwUnexpectedToken(t)}return n},e.prototype.isPropertyKey=function(e,t){return e.type===u.Syntax.Identifier&&e.name===t||e.type===u.Syntax.Literal&&e.value===t},e.prototype.parseObjectProperty=function(e){var t,n,r,o=this.createNode(),a=this.lookahead,u=!1,l=!1,h=!1;a.type===s.Token.Identifier?(this.nextToken(),n=this.finalize(o,new c.Identifier(a.value))):this.match("*")?this.nextToken():(u=this.match("["),n=this.parseObjectPropertyKey());var p=this.qualifiedPropertyName(this.lookahead);if(a.type===s.Token.Identifier&&"get"===a.value&&p)t="get",u=this.match("["),n=this.parseObjectPropertyKey(),this.context.allowYield=!1,r=this.parseGetterMethod();else if(a.type===s.Token.Identifier&&"set"===a.value&&p)t="set",u=this.match("["),n=this.parseObjectPropertyKey(),r=this.parseSetterMethod();else if(a.type===s.Token.Punctuator&&"*"===a.value&&p)t="init",u=this.match("["),n=this.parseObjectPropertyKey(),r=this.parseGeneratorMethod(),l=!0;else if(n||this.throwUnexpectedToken(this.lookahead),t="init",this.match(":"))!u&&this.isPropertyKey(n,"__proto__")&&(e.value&&this.tolerateError(i.Messages.DuplicateProtoProperty),e.value=!0),this.nextToken(),r=this.inheritCoverGrammar(this.parseAssignmentExpression);else if(this.match("("))r=this.parsePropertyMethodFunction(),l=!0;else if(a.type===s.Token.Identifier){var f=this.finalize(o,new c.Identifier(a.value));if(this.match("=")){this.context.firstCoverInitializedNameError=this.lookahead,this.nextToken(),h=!0;var d=this.isolateCoverGrammar(this.parseAssignmentExpression);r=this.finalize(o,new c.AssignmentPattern(f,d))}else h=!0,r=f}else this.throwUnexpectedToken(this.nextToken());return this.finalize(o,new c.Property(t,n,u,r,l,h))},e.prototype.parseObjectInitializer=function(){var e=this.createNode();this.expect("{");for(var t=[],n={value:!1};!this.match("}");)t.push(this.parseObjectProperty(n)),this.match("}")||this.expectCommaSeparator();return this.expect("}"),this.finalize(e,new c.ObjectExpression(t))},e.prototype.parseTemplateHead=function(){r.assert(this.lookahead.head,"Template literal must start with a template head");var e=this.createNode(),t=this.nextToken(),n={raw:t.value.raw,cooked:t.value.cooked};return this.finalize(e,new c.TemplateElement(n,t.tail))},e.prototype.parseTemplateElement=function(){this.lookahead.type!==s.Token.Template&&this.throwUnexpectedToken();var e=this.createNode(),t=this.nextToken(),n={raw:t.value.raw,cooked:t.value.cooked};return this.finalize(e,new c.TemplateElement(n,t.tail))},e.prototype.parseTemplateLiteral=function(){var e=this.createNode(),t=[],n=[],r=this.parseTemplateHead();for(n.push(r);!r.tail;)t.push(this.parseExpression()),r=this.parseTemplateElement(),n.push(r);return this.finalize(e,new c.TemplateLiteral(n,t))},e.prototype.reinterpretExpressionAsPattern=function(e){switch(e.type){case u.Syntax.Identifier:case u.Syntax.MemberExpression:case u.Syntax.RestElement:case u.Syntax.AssignmentPattern:break;case u.Syntax.SpreadElement:e.type=u.Syntax.RestElement,this.reinterpretExpressionAsPattern(e.argument);break;case u.Syntax.ArrayExpression:e.type=u.Syntax.ArrayPattern;for(var t=0;t<e.elements.length;t++)null!==e.elements[t]&&this.reinterpretExpressionAsPattern(e.elements[t]);break;case u.Syntax.ObjectExpression:e.type=u.Syntax.ObjectPattern;for(var t=0;t<e.properties.length;t++)this.reinterpretExpressionAsPattern(e.properties[t].value);break;case u.Syntax.AssignmentExpression:e.type=u.Syntax.AssignmentPattern,delete e.operator,this.reinterpretExpressionAsPattern(e.left)}},e.prototype.parseGroupExpression=function(){var e;if(this.expect("("),this.match(")"))this.nextToken(),this.match("=>")||this.expect("=>"),e={type:l,params:[]};else{var t=this.lookahead,n=[];if(this.match("..."))e=this.parseRestElement(n),this.expect(")"),this.match("=>")||this.expect("=>"),e={type:l,params:[e]};else{var r=!1;if(this.context.isBindingElement=!0,e=this.inheritCoverGrammar(this.parseAssignmentExpression),this.match(",")){var i=[];for(this.context.isAssignmentTarget=!1,i.push(e);this.startMarker.index<this.scanner.length&&this.match(",");){if(this.nextToken(),this.match("...")){this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),i.push(this.parseRestElement(n)),this.expect(")"),this.match("=>")||this.expect("=>"),this.context.isBindingElement=!1;for(var o=0;o<i.length;o++)this.reinterpretExpressionAsPattern(i[o]);r=!0,e={type:l,params:i}}else i.push(this.inheritCoverGrammar(this.parseAssignmentExpression));if(r)break}r||(e=this.finalize(this.startNode(t),new c.SequenceExpression(i)))}if(!r){if(this.expect(")"),this.match("=>")&&(e.type===u.Syntax.Identifier&&"yield"===e.name&&(r=!0,e={type:l,params:[e]}),!r)){if(this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),e.type===u.Syntax.SequenceExpression)for(var o=0;o<e.expressions.length;o++)this.reinterpretExpressionAsPattern(e.expressions[o]);else this.reinterpretExpressionAsPattern(e);var s=e.type===u.Syntax.SequenceExpression?e.expressions:[e];e={type:l,params:s}}this.context.isBindingElement=!1}}}return e},e.prototype.parseArguments=function(){this.expect("(");var e=[];if(!this.match(")"))for(;;){var t=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAssignmentExpression);if(e.push(t),this.match(")"))break;this.expectCommaSeparator()}return this.expect(")"),e},e.prototype.isIdentifierName=function(e){return e.type===s.Token.Identifier||e.type===s.Token.Keyword||e.type===s.Token.BooleanLiteral||e.type===s.Token.NullLiteral},e.prototype.parseIdentifierName=function(){var e=this.createNode(),t=this.nextToken();return this.isIdentifierName(t)||this.throwUnexpectedToken(t),this.finalize(e,new c.Identifier(t.value))},e.prototype.parseNewExpression=function(){var e=this.createNode(),t=this.parseIdentifierName();r.assert("new"===t.name,"New expression must start with `new`");var n;if(this.match("."))if(this.nextToken(),this.lookahead.type===s.Token.Identifier&&this.context.inFunctionBody&&"target"===this.lookahead.value){var i=this.parseIdentifierName();n=new c.MetaProperty(t,i)}else this.throwUnexpectedToken(this.lookahead);else{var o=this.isolateCoverGrammar(this.parseLeftHandSideExpression),a=this.match("(")?this.parseArguments():[];n=new c.NewExpression(o,a),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return this.finalize(e,n)},e.prototype.parseLeftHandSideExpressionAllowCall=function(){var e=this.lookahead,t=this.context.allowIn;this.context.allowIn=!0;var n;for(this.matchKeyword("super")&&this.context.inFunctionBody?(n=this.createNode(),this.nextToken(),n=this.finalize(n,new c.Super),this.match("(")||this.match(".")||this.match("[")||this.throwUnexpectedToken(this.lookahead)):n=this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var r=this.parseIdentifierName();n=this.finalize(this.startNode(e),new c.StaticMemberExpression(n,r))}else if(this.match("(")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!1;var i=this.parseArguments();n=this.finalize(this.startNode(e),new c.CallExpression(n,i))}else if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var r=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),n=this.finalize(this.startNode(e),new c.ComputedMemberExpression(n,r))}else{if(this.lookahead.type!==s.Token.Template||!this.lookahead.head)break;var o=this.parseTemplateLiteral();n=this.finalize(this.startNode(e),new c.TaggedTemplateExpression(n,o))}return this.context.allowIn=t,n},e.prototype.parseSuper=function(){var e=this.createNode();return this.expectKeyword("super"),this.match("[")||this.match(".")||this.throwUnexpectedToken(this.lookahead),this.finalize(e,new c.Super)},e.prototype.parseLeftHandSideExpression=function(){r.assert(this.context.allowIn,"callee of new expression always allow in keyword.");for(var e=this.startNode(this.lookahead),t=this.matchKeyword("super")&&this.context.inFunctionBody?this.parseSuper():this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var n=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),t=this.finalize(e,new c.ComputedMemberExpression(t,n))}else if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var n=this.parseIdentifierName();t=this.finalize(e,new c.StaticMemberExpression(t,n))}else{if(this.lookahead.type!==s.Token.Template||!this.lookahead.head)break;var i=this.parseTemplateLiteral();t=this.finalize(e,new c.TaggedTemplateExpression(t,i))}return t},e.prototype.parseUpdateExpression=function(){var e,t=this.lookahead;if(this.match("++")||this.match("--")){var n=this.startNode(t),r=this.nextToken();e=this.inheritCoverGrammar(this.parseUnaryExpression),this.context.strict&&e.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(e.name)&&this.tolerateError(i.Messages.StrictLHSPrefix),this.context.isAssignmentTarget||this.tolerateError(i.Messages.InvalidLHSInAssignment);var o=!0;e=this.finalize(n,new c.UpdateExpression(r.value,e,o)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else if(e=this.inheritCoverGrammar(this.parseLeftHandSideExpressionAllowCall),!this.hasLineTerminator&&this.lookahead.type===s.Token.Punctuator&&(this.match("++")||this.match("--"))){this.context.strict&&e.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(e.name)&&this.tolerateError(i.Messages.StrictLHSPostfix),this.context.isAssignmentTarget||this.tolerateError(i.Messages.InvalidLHSInAssignment),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var a=this.nextToken().value,o=!1;e=this.finalize(this.startNode(t),new c.UpdateExpression(a,e,o))}return e},e.prototype.parseUnaryExpression=function(){var e;if(this.match("+")||this.match("-")||this.match("~")||this.match("!")||this.matchKeyword("delete")||this.matchKeyword("void")||this.matchKeyword("typeof")){var t=this.startNode(this.lookahead),n=this.nextToken();e=this.inheritCoverGrammar(this.parseUnaryExpression),e=this.finalize(t,new c.UnaryExpression(n.value,e)),this.context.strict&&"delete"===e.operator&&e.argument.type===u.Syntax.Identifier&&this.tolerateError(i.Messages.StrictDelete),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else e=this.parseUpdateExpression();return e},e.prototype.parseExponentiationExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseUnaryExpression);if(t.type!==u.Syntax.UnaryExpression&&this.match("**")){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var n=t,r=this.isolateCoverGrammar(this.parseExponentiationExpression);t=this.finalize(this.startNode(e),new c.BinaryExpression("**",n,r))}return t},e.prototype.binaryPrecedence=function(e){var t,n=e.value;return t=e.type===s.Token.Punctuator?this.operatorPrecedence[n]||0:e.type===s.Token.Keyword&&("instanceof"===n||this.context.allowIn&&"in"===n)?7:0},e.prototype.parseBinaryExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseExponentiationExpression),n=this.lookahead,r=this.binaryPrecedence(n);if(r>0){this.nextToken(),n.prec=r,this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;for(var i=[e,this.lookahead],o=t,s=this.isolateCoverGrammar(this.parseExponentiationExpression),a=[o,n,s];;){if(r=this.binaryPrecedence(this.lookahead),r<=0)break;for(;a.length>2&&r<=a[a.length-2].prec;){s=a.pop();var u=a.pop().value;o=a.pop(),i.pop();var l=this.startNode(i[i.length-1]);a.push(this.finalize(l,new c.BinaryExpression(u,o,s)))}n=this.nextToken(),n.prec=r,a.push(n),i.push(this.lookahead),a.push(this.isolateCoverGrammar(this.parseExponentiationExpression))}var h=a.length-1;for(t=a[h],i.pop();h>1;){var l=this.startNode(i.pop());t=this.finalize(l,new c.BinaryExpression(a[h-1].value,a[h-2],t)),h-=2}}return t},e.prototype.parseConditionalExpression=function(){var e=this.lookahead,t=this.inheritCoverGrammar(this.parseBinaryExpression);if(this.match("?")){this.nextToken();var n=this.context.allowIn;this.context.allowIn=!0;var r=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowIn=n,this.expect(":");var i=this.isolateCoverGrammar(this.parseAssignmentExpression);t=this.finalize(this.startNode(e),new c.ConditionalExpression(t,r,i)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return t},e.prototype.checkPatternParam=function(e,t){switch(t.type){case u.Syntax.Identifier:this.validateParam(e,t,t.name);break;case u.Syntax.RestElement:this.checkPatternParam(e,t.argument);break;case u.Syntax.AssignmentPattern:this.checkPatternParam(e,t.left);break;case u.Syntax.ArrayPattern:for(var n=0;n<t.elements.length;n++)null!==t.elements[n]&&this.checkPatternParam(e,t.elements[n]);break;case u.Syntax.YieldExpression:break;default:r.assert(t.type===u.Syntax.ObjectPattern,"Invalid type");for(var n=0;n<t.properties.length;n++)this.checkPatternParam(e,t.properties[n].value)}},e.prototype.reinterpretAsCoverFormalsList=function(e){var t,n=[e];switch(e.type){case u.Syntax.Identifier:break;case l:n=e.params;break;default:return null}t={paramSet:{}};for(var r=0;r<n.length;++r){var o=n[r];o.type===u.Syntax.AssignmentPattern&&o.right.type===u.Syntax.YieldExpression&&(o.right.argument&&this.throwUnexpectedToken(this.lookahead),o.right.type=u.Syntax.Identifier,o.right.name="yield",delete o.right.argument,delete o.right.delegate),this.checkPatternParam(t,o),n[r]=o}if(this.context.strict||!this.context.allowYield)for(var r=0;r<n.length;++r){var o=n[r];o.type===u.Syntax.YieldExpression&&this.throwUnexpectedToken(this.lookahead)}if(t.message===i.Messages.StrictParamDupe){var s=this.context.strict?t.stricted:t.firstRestricted;this.throwUnexpectedToken(s,t.message)}return{params:n,stricted:t.stricted,firstRestricted:t.firstRestricted,message:t.message}},e.prototype.parseAssignmentExpression=function(){var e;if(!this.context.allowYield&&this.matchKeyword("yield"))e=this.parseYieldExpression();else{var t=this.lookahead,n=t;if(e=this.parseConditionalExpression(),e.type===l||this.match("=>")){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var r=this.reinterpretAsCoverFormalsList(e);if(r){this.hasLineTerminator&&this.tolerateUnexpectedToken(this.lookahead),this.context.firstCoverInitializedNameError=null;var o=this.context.strict,s=this.context.allowYield;this.context.allowYield=!0;var a=this.startNode(t);this.expect("=>");var h=this.match("{")?this.parseFunctionSourceElements():this.isolateCoverGrammar(this.parseAssignmentExpression),p=h.type!==u.Syntax.BlockStatement;this.context.strict&&r.firstRestricted&&this.throwUnexpectedToken(r.firstRestricted,r.message),this.context.strict&&r.stricted&&this.tolerateUnexpectedToken(r.stricted,r.message),e=this.finalize(a,new c.ArrowFunctionExpression(r.params,h,p)),this.context.strict=o,this.context.allowYield=s}}else if(this.matchAssign()){if(this.context.isAssignmentTarget||this.tolerateError(i.Messages.InvalidLHSInAssignment),this.context.strict&&e.type===u.Syntax.Identifier){var f=e;this.scanner.isRestrictedWord(f.name)&&this.tolerateUnexpectedToken(n,i.Messages.StrictLHSAssignment),this.scanner.isStrictModeReservedWord(f.name)&&this.tolerateUnexpectedToken(n,i.Messages.StrictReservedWord)}this.match("=")?this.reinterpretExpressionAsPattern(e):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1),n=this.nextToken();var d=this.isolateCoverGrammar(this.parseAssignmentExpression);e=this.finalize(this.startNode(t),new c.AssignmentExpression(n.value,e,d)),this.context.firstCoverInitializedNameError=null}}return e},e.prototype.parseExpression=function(){var e=this.lookahead,t=this.isolateCoverGrammar(this.parseAssignmentExpression);if(this.match(",")){var n=[];for(n.push(t);this.startMarker.index<this.scanner.length&&this.match(",");)this.nextToken(),n.push(this.isolateCoverGrammar(this.parseAssignmentExpression));t=this.finalize(this.startNode(e),new c.SequenceExpression(n))}return t},e.prototype.parseStatementListItem=function(){var e=null;if(this.context.isAssignmentTarget=!0,this.context.isBindingElement=!0,this.lookahead.type===s.Token.Keyword)switch(this.lookahead.value){case"export":"module"!==this.sourceType&&this.tolerateUnexpectedToken(this.lookahead,i.Messages.IllegalExportDeclaration),e=this.parseExportDeclaration();break;case"import":"module"!==this.sourceType&&this.tolerateUnexpectedToken(this.lookahead,i.Messages.IllegalImportDeclaration),e=this.parseImportDeclaration();break;case"const":e=this.parseLexicalDeclaration({inFor:!1});break;case"function":e=this.parseFunctionDeclaration();break;case"class":e=this.parseClassDeclaration();break;case"let":e=this.isLexicalDeclaration()?this.parseLexicalDeclaration({inFor:!1}):this.parseStatement();break;default:e=this.parseStatement()}else e=this.parseStatement();return e},e.prototype.parseBlock=function(){var e=this.createNode();this.expect("{");for(var t=[];;){if(this.match("}"))break;t.push(this.parseStatementListItem())}return this.expect("}"),this.finalize(e,new c.BlockStatement(t))},e.prototype.parseLexicalBinding=function(e,t){var n=this.createNode(),r=[],o=this.parsePattern(r,e);this.context.strict&&o.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(o.name)&&this.tolerateError(i.Messages.StrictVarName);var s=null;return"const"===e?this.matchKeyword("in")||this.matchContextualKeyword("of")||(this.expect("="),s=this.isolateCoverGrammar(this.parseAssignmentExpression)):(!t.inFor&&o.type!==u.Syntax.Identifier||this.match("="))&&(this.expect("="),s=this.isolateCoverGrammar(this.parseAssignmentExpression)),this.finalize(n,new c.VariableDeclarator(o,s))},e.prototype.parseBindingList=function(e,t){for(var n=[this.parseLexicalBinding(e,t)];this.match(",");)this.nextToken(),n.push(this.parseLexicalBinding(e,t));return n},e.prototype.isLexicalDeclaration=function(){var e=this.scanner.index,t=this.scanner.lineNumber,n=this.scanner.lineStart;this.collectComments();var r=this.scanner.lex();return this.scanner.index=e,this.scanner.lineNumber=t,this.scanner.lineStart=n,r.type===s.Token.Identifier||r.type===s.Token.Punctuator&&"["===r.value||r.type===s.Token.Punctuator&&"{"===r.value||r.type===s.Token.Keyword&&"let"===r.value||r.type===s.Token.Keyword&&"yield"===r.value},e.prototype.parseLexicalDeclaration=function(e){var t=this.createNode(),n=this.nextToken().value;r.assert("let"===n||"const"===n,"Lexical declaration must be either let or const");var i=this.parseBindingList(n,e);return this.consumeSemicolon(),this.finalize(t,new c.VariableDeclaration(i,n))},e.prototype.parseBindingRestElement=function(e,t){var n=this.createNode();this.expect("...");var r=this.parsePattern(e,t);return this.finalize(n,new c.RestElement(r))},e.prototype.parseArrayPattern=function(e,t){var n=this.createNode();this.expect("[");for(var r=[];!this.match("]");)if(this.match(","))this.nextToken(),r.push(null);else{if(this.match("...")){r.push(this.parseBindingRestElement(e,t));break}r.push(this.parsePatternWithDefault(e,t)),this.match("]")||this.expect(",")}return this.expect("]"),this.finalize(n,new c.ArrayPattern(r))},e.prototype.parsePropertyPattern=function(e,t){var n,r,i=this.createNode(),o=!1,a=!1,u=!1;if(this.lookahead.type===s.Token.Identifier){var l=this.lookahead;n=this.parseVariableIdentifier();var h=this.finalize(i,new c.Identifier(l.value));if(this.match("=")){
e.push(l),a=!0,this.nextToken();var p=this.parseAssignmentExpression();r=this.finalize(this.startNode(l),new c.AssignmentPattern(h,p))}else this.match(":")?(this.expect(":"),r=this.parsePatternWithDefault(e,t)):(e.push(l),a=!0,r=h)}else o=this.match("["),n=this.parseObjectPropertyKey(),this.expect(":"),r=this.parsePatternWithDefault(e,t);return this.finalize(i,new c.Property("init",n,o,r,u,a))},e.prototype.parseObjectPattern=function(e,t){var n=this.createNode(),r=[];for(this.expect("{");!this.match("}");)r.push(this.parsePropertyPattern(e,t)),this.match("}")||this.expect(",");return this.expect("}"),this.finalize(n,new c.ObjectPattern(r))},e.prototype.parsePattern=function(e,t){var n;return this.match("[")?n=this.parseArrayPattern(e,t):this.match("{")?n=this.parseObjectPattern(e,t):(!this.matchKeyword("let")||"const"!==t&&"let"!==t||this.tolerateUnexpectedToken(this.lookahead,i.Messages.UnexpectedToken),e.push(this.lookahead),n=this.parseVariableIdentifier(t)),n},e.prototype.parsePatternWithDefault=function(e,t){var n=this.lookahead,r=this.parsePattern(e,t);if(this.match("=")){this.nextToken();var i=this.context.allowYield;this.context.allowYield=!0;var o=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowYield=i,r=this.finalize(this.startNode(n),new c.AssignmentPattern(r,o))}return r},e.prototype.parseVariableIdentifier=function(e){var t=this.createNode(),n=this.nextToken();return n.type===s.Token.Keyword&&"yield"===n.value?(this.context.strict&&this.tolerateUnexpectedToken(n,i.Messages.StrictReservedWord),this.context.allowYield||this.throwUnexpectedToken(n)):n.type!==s.Token.Identifier?this.context.strict&&n.type===s.Token.Keyword&&this.scanner.isStrictModeReservedWord(n.value)?this.tolerateUnexpectedToken(n,i.Messages.StrictReservedWord):(this.context.strict||"let"!==n.value||"var"!==e)&&this.throwUnexpectedToken(n):"module"===this.sourceType&&n.type===s.Token.Identifier&&"await"===n.value&&this.tolerateUnexpectedToken(n),this.finalize(t,new c.Identifier(n.value))},e.prototype.parseVariableDeclaration=function(e){var t=this.createNode(),n=[],r=this.parsePattern(n,"var");this.context.strict&&r.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(r.name)&&this.tolerateError(i.Messages.StrictVarName);var o=null;return this.match("=")?(this.nextToken(),o=this.isolateCoverGrammar(this.parseAssignmentExpression)):r.type===u.Syntax.Identifier||e.inFor||this.expect("="),this.finalize(t,new c.VariableDeclarator(r,o))},e.prototype.parseVariableDeclarationList=function(e){var t={inFor:e.inFor},n=[];for(n.push(this.parseVariableDeclaration(t));this.match(",");)this.nextToken(),n.push(this.parseVariableDeclaration(t));return n},e.prototype.parseVariableStatement=function(){var e=this.createNode();this.expectKeyword("var");var t=this.parseVariableDeclarationList({inFor:!1});return this.consumeSemicolon(),this.finalize(e,new c.VariableDeclaration(t,"var"))},e.prototype.parseEmptyStatement=function(){var e=this.createNode();return this.expect(";"),this.finalize(e,new c.EmptyStatement)},e.prototype.parseExpressionStatement=function(){var e=this.createNode(),t=this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new c.ExpressionStatement(t))},e.prototype.parseIfStatement=function(){var e,t=this.createNode(),n=null;this.expectKeyword("if"),this.expect("(");var r=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new c.EmptyStatement)):(this.expect(")"),e=this.parseStatement(),this.matchKeyword("else")&&(this.nextToken(),n=this.parseStatement())),this.finalize(t,new c.IfStatement(r,e,n))},e.prototype.parseDoWhileStatement=function(){var e=this.createNode();this.expectKeyword("do");var t=this.context.inIteration;this.context.inIteration=!0;var n=this.parseStatement();this.context.inIteration=t,this.expectKeyword("while"),this.expect("(");var r=this.parseExpression();return this.expect(")"),this.match(";")&&this.nextToken(),this.finalize(e,new c.DoWhileStatement(n,r))},e.prototype.parseWhileStatement=function(){var e,t=this.createNode();this.expectKeyword("while"),this.expect("(");var n=this.parseExpression();if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),e=this.finalize(this.createNode(),new c.EmptyStatement);else{this.expect(")");var r=this.context.inIteration;this.context.inIteration=!0,e=this.parseStatement(),this.context.inIteration=r}return this.finalize(t,new c.WhileStatement(n,e))},e.prototype.parseForStatement=function(){var e,t,n=null,r=null,o=null,s=!0,a=this.createNode();if(this.expectKeyword("for"),this.expect("("),this.match(";"))this.nextToken();else if(this.matchKeyword("var")){n=this.createNode(),this.nextToken();var l=this.context.allowIn;this.context.allowIn=!1;var h=this.parseVariableDeclarationList({inFor:!0});if(this.context.allowIn=l,1===h.length&&this.matchKeyword("in")){var p=h[0];p.init&&(p.id.type===u.Syntax.ArrayPattern||p.id.type===u.Syntax.ObjectPattern||this.context.strict)&&this.tolerateError(i.Messages.ForInOfLoopInitializer,"for-in"),n=this.finalize(n,new c.VariableDeclaration(h,"var")),this.nextToken(),e=n,t=this.parseExpression(),n=null}else 1===h.length&&null===h[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new c.VariableDeclaration(h,"var")),this.nextToken(),e=n,t=this.parseAssignmentExpression(),n=null,s=!1):(n=this.finalize(n,new c.VariableDeclaration(h,"var")),this.expect(";"))}else if(this.matchKeyword("const")||this.matchKeyword("let")){n=this.createNode();var f=this.nextToken().value;if(this.context.strict||"in"!==this.lookahead.value){var l=this.context.allowIn;this.context.allowIn=!1;var h=this.parseBindingList(f,{inFor:!0});this.context.allowIn=l,1===h.length&&null===h[0].init&&this.matchKeyword("in")?(n=this.finalize(n,new c.VariableDeclaration(h,f)),this.nextToken(),e=n,t=this.parseExpression(),n=null):1===h.length&&null===h[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new c.VariableDeclaration(h,f)),this.nextToken(),e=n,t=this.parseAssignmentExpression(),n=null,s=!1):(this.consumeSemicolon(),n=this.finalize(n,new c.VariableDeclaration(h,f)))}else n=this.finalize(n,new c.Identifier(f)),this.nextToken(),e=n,t=this.parseExpression(),n=null}else{var d=this.lookahead,l=this.context.allowIn;if(this.context.allowIn=!1,n=this.inheritCoverGrammar(this.parseAssignmentExpression),this.context.allowIn=l,this.matchKeyword("in"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(i.Messages.InvalidLHSInForIn),this.nextToken(),this.reinterpretExpressionAsPattern(n),e=n,t=this.parseExpression(),n=null;else if(this.matchContextualKeyword("of"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(i.Messages.InvalidLHSInForLoop),this.nextToken(),this.reinterpretExpressionAsPattern(n),e=n,t=this.parseAssignmentExpression(),n=null,s=!1;else{if(this.match(",")){for(var m=[n];this.match(",");)this.nextToken(),m.push(this.isolateCoverGrammar(this.parseAssignmentExpression));n=this.finalize(this.startNode(d),new c.SequenceExpression(m))}this.expect(";")}}"undefined"==typeof e&&(this.match(";")||(r=this.parseExpression()),this.expect(";"),this.match(")")||(o=this.parseExpression()));var g;if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),g=this.finalize(this.createNode(),new c.EmptyStatement);else{this.expect(")");var x=this.context.inIteration;this.context.inIteration=!0,g=this.isolateCoverGrammar(this.parseStatement),this.context.inIteration=x}return"undefined"==typeof e?this.finalize(a,new c.ForStatement(n,r,o,g)):s?this.finalize(a,new c.ForInStatement(e,t,g)):this.finalize(a,new c.ForOfStatement(e,t,g))},e.prototype.parseContinueStatement=function(){var e=this.createNode();this.expectKeyword("continue");var t=null;if(this.lookahead.type===s.Token.Identifier&&!this.hasLineTerminator){t=this.parseVariableIdentifier();var n="$"+t.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,n)||this.throwError(i.Messages.UnknownLabel,t.name)}return this.consumeSemicolon(),null!==t||this.context.inIteration||this.throwError(i.Messages.IllegalContinue),this.finalize(e,new c.ContinueStatement(t))},e.prototype.parseBreakStatement=function(){var e=this.createNode();this.expectKeyword("break");var t=null;if(this.lookahead.type===s.Token.Identifier&&!this.hasLineTerminator){t=this.parseVariableIdentifier();var n="$"+t.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,n)||this.throwError(i.Messages.UnknownLabel,t.name)}return this.consumeSemicolon(),null!==t||this.context.inIteration||this.context.inSwitch||this.throwError(i.Messages.IllegalBreak),this.finalize(e,new c.BreakStatement(t))},e.prototype.parseReturnStatement=function(){this.context.inFunctionBody||this.tolerateError(i.Messages.IllegalReturn);var e=this.createNode();this.expectKeyword("return");var t=!this.match(";")&&!this.match("}")&&!this.hasLineTerminator&&this.lookahead.type!==s.Token.EOF,n=t?this.parseExpression():null;return this.consumeSemicolon(),this.finalize(e,new c.ReturnStatement(n))},e.prototype.parseWithStatement=function(){this.context.strict&&this.tolerateError(i.Messages.StrictModeWith);var e=this.createNode();this.expectKeyword("with"),this.expect("(");var t=this.parseExpression();this.expect(")");var n=this.parseStatement();return this.finalize(e,new c.WithStatement(t,n))},e.prototype.parseSwitchCase=function(){var e,t=this.createNode();this.matchKeyword("default")?(this.nextToken(),e=null):(this.expectKeyword("case"),e=this.parseExpression()),this.expect(":");for(var n=[];;){if(this.match("}")||this.matchKeyword("default")||this.matchKeyword("case"))break;n.push(this.parseStatementListItem())}return this.finalize(t,new c.SwitchCase(e,n))},e.prototype.parseSwitchStatement=function(){var e=this.createNode();this.expectKeyword("switch"),this.expect("(");var t=this.parseExpression();this.expect(")");var n=this.context.inSwitch;this.context.inSwitch=!0;var r=[],o=!1;for(this.expect("{");;){if(this.match("}"))break;var s=this.parseSwitchCase();null===s.test&&(o&&this.throwError(i.Messages.MultipleDefaultsInSwitch),o=!0),r.push(s)}return this.expect("}"),this.context.inSwitch=n,this.finalize(e,new c.SwitchStatement(t,r))},e.prototype.parseLabelledStatement=function(){var e,t=this.createNode(),n=this.parseExpression();if(n.type===u.Syntax.Identifier&&this.match(":")){this.nextToken();var r=n,o="$"+r.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,o)&&this.throwError(i.Messages.Redeclaration,"Label",r.name),this.context.labelSet[o]=!0;var s=this.parseStatement();delete this.context.labelSet[o],e=new c.LabeledStatement(r,s)}else this.consumeSemicolon(),e=new c.ExpressionStatement(n);return this.finalize(t,e)},e.prototype.parseThrowStatement=function(){var e=this.createNode();this.expectKeyword("throw"),this.hasLineTerminator&&this.throwError(i.Messages.NewlineAfterThrow);var t=this.parseExpression();return this.consumeSemicolon(),this.finalize(e,new c.ThrowStatement(t))},e.prototype.parseCatchClause=function(){var e=this.createNode();this.expectKeyword("catch"),this.expect("("),this.match(")")&&this.throwUnexpectedToken(this.lookahead);for(var t=[],n=this.parsePattern(t),r={},o=0;o<t.length;o++){var s="$"+t[o].value;Object.prototype.hasOwnProperty.call(r,s)&&this.tolerateError(i.Messages.DuplicateBinding,t[o].value),r[s]=!0}this.context.strict&&n.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(n.name)&&this.tolerateError(i.Messages.StrictCatchVariable),this.expect(")");var a=this.parseBlock();return this.finalize(e,new c.CatchClause(n,a))},e.prototype.parseFinallyClause=function(){return this.expectKeyword("finally"),this.parseBlock()},e.prototype.parseTryStatement=function(){var e=this.createNode();this.expectKeyword("try");var t=this.parseBlock(),n=this.matchKeyword("catch")?this.parseCatchClause():null,r=this.matchKeyword("finally")?this.parseFinallyClause():null;return n||r||this.throwError(i.Messages.NoCatchOrFinally),this.finalize(e,new c.TryStatement(t,n,r))},e.prototype.parseDebuggerStatement=function(){var e=this.createNode();return this.expectKeyword("debugger"),this.consumeSemicolon(),this.finalize(e,new c.DebuggerStatement)},e.prototype.parseStatement=function(){var e=null;switch(this.lookahead.type){case s.Token.BooleanLiteral:case s.Token.NullLiteral:case s.Token.NumericLiteral:case s.Token.StringLiteral:case s.Token.Template:case s.Token.RegularExpression:e=this.parseExpressionStatement();break;case s.Token.Punctuator:var t=this.lookahead.value;e="{"===t?this.parseBlock():"("===t?this.parseExpressionStatement():";"===t?this.parseEmptyStatement():this.parseExpressionStatement();break;case s.Token.Identifier:e=this.parseLabelledStatement();break;case s.Token.Keyword:switch(this.lookahead.value){case"break":e=this.parseBreakStatement();break;case"continue":e=this.parseContinueStatement();break;case"debugger":e=this.parseDebuggerStatement();break;case"do":e=this.parseDoWhileStatement();break;case"for":e=this.parseForStatement();break;case"function":e=this.parseFunctionDeclaration();break;case"if":e=this.parseIfStatement();break;case"return":e=this.parseReturnStatement();break;case"switch":e=this.parseSwitchStatement();break;case"throw":e=this.parseThrowStatement();break;case"try":e=this.parseTryStatement();break;case"var":e=this.parseVariableStatement();break;case"while":e=this.parseWhileStatement();break;case"with":e=this.parseWithStatement();break;default:e=this.parseExpressionStatement()}break;default:this.throwUnexpectedToken(this.lookahead)}return e},e.prototype.parseFunctionSourceElements=function(){var e=this.createNode();this.expect("{");var t=this.parseDirectivePrologues(),n=this.context.labelSet,r=this.context.inIteration,i=this.context.inSwitch,o=this.context.inFunctionBody;for(this.context.labelSet={},this.context.inIteration=!1,this.context.inSwitch=!1,this.context.inFunctionBody=!0;this.startMarker.index<this.scanner.length&&!this.match("}");)t.push(this.parseStatementListItem());return this.expect("}"),this.context.labelSet=n,this.context.inIteration=r,this.context.inSwitch=i,this.context.inFunctionBody=o,this.finalize(e,new c.BlockStatement(t))},e.prototype.validateParam=function(e,t,n){var r="$"+n;this.context.strict?(this.scanner.isRestrictedWord(n)&&(e.stricted=t,e.message=i.Messages.StrictParamName),Object.prototype.hasOwnProperty.call(e.paramSet,r)&&(e.stricted=t,e.message=i.Messages.StrictParamDupe)):e.firstRestricted||(this.scanner.isRestrictedWord(n)?(e.firstRestricted=t,e.message=i.Messages.StrictParamName):this.scanner.isStrictModeReservedWord(n)?(e.firstRestricted=t,e.message=i.Messages.StrictReservedWord):Object.prototype.hasOwnProperty.call(e.paramSet,r)&&(e.stricted=t,e.message=i.Messages.StrictParamDupe)),"function"==typeof Object.defineProperty?Object.defineProperty(e.paramSet,r,{value:!0,enumerable:!0,writable:!0,configurable:!0}):e.paramSet[r]=!0},e.prototype.parseRestElement=function(e){var t=this.createNode();this.expect("...");var n=this.parsePattern(e);return this.match("=")&&this.throwError(i.Messages.DefaultRestParameter),this.match(")")||this.throwError(i.Messages.ParameterAfterRestParameter),this.finalize(t,new c.RestElement(n))},e.prototype.parseFormalParameter=function(e){for(var t=[],n=this.match("...")?this.parseRestElement(t):this.parsePatternWithDefault(t),r=0;r<t.length;r++)this.validateParam(e,t[r],t[r].value);return e.params.push(n),!this.match(")")},e.prototype.parseFormalParameters=function(e){var t;if(t={params:[],firstRestricted:e},this.expect("("),!this.match(")"))for(t.paramSet={};this.startMarker.index<this.scanner.length&&this.parseFormalParameter(t);)this.expect(",");return this.expect(")"),{params:t.params,stricted:t.stricted,firstRestricted:t.firstRestricted,message:t.message}},e.prototype.parseFunctionDeclaration=function(e){var t=this.createNode();this.expectKeyword("function");var n=this.match("*");n&&this.nextToken();var r,o=null,s=null;if(!e||!this.match("(")){var a=this.lookahead;o=this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(a.value)&&this.tolerateUnexpectedToken(a,i.Messages.StrictFunctionName):this.scanner.isRestrictedWord(a.value)?(s=a,r=i.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(a.value)&&(s=a,r=i.Messages.StrictReservedWord)}var u=this.context.allowYield;this.context.allowYield=!n;var l=this.parseFormalParameters(s),h=l.params,p=l.stricted;s=l.firstRestricted,l.message&&(r=l.message);var f=this.context.strict,d=this.parseFunctionSourceElements();return this.context.strict&&s&&this.throwUnexpectedToken(s,r),this.context.strict&&p&&this.tolerateUnexpectedToken(p,r),this.context.strict=f,this.context.allowYield=u,this.finalize(t,new c.FunctionDeclaration(o,h,d,n))},e.prototype.parseFunctionExpression=function(){var e=this.createNode();this.expectKeyword("function");var t=this.match("*");t&&this.nextToken();var n,r,o=null,s=this.context.allowYield;if(this.context.allowYield=!t,!this.match("(")){var a=this.lookahead;o=this.context.strict||t||!this.matchKeyword("yield")?this.parseVariableIdentifier():this.parseIdentifierName(),this.context.strict?this.scanner.isRestrictedWord(a.value)&&this.tolerateUnexpectedToken(a,i.Messages.StrictFunctionName):this.scanner.isRestrictedWord(a.value)?(r=a,n=i.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(a.value)&&(r=a,n=i.Messages.StrictReservedWord)}var u=this.parseFormalParameters(r),l=u.params,h=u.stricted;r=u.firstRestricted,u.message&&(n=u.message);var p=this.context.strict,f=this.parseFunctionSourceElements();return this.context.strict&&r&&this.throwUnexpectedToken(r,n),this.context.strict&&h&&this.tolerateUnexpectedToken(h,n),this.context.strict=p,this.context.allowYield=s,this.finalize(e,new c.FunctionExpression(o,l,f,t))},e.prototype.parseDirective=function(){var e=this.lookahead,t=null,n=this.createNode(),r=this.parseExpression();return r.type===u.Syntax.Literal&&(t=this.getTokenRaw(e).slice(1,-1)),this.consumeSemicolon(),this.finalize(n,t?new c.Directive(r,t):new c.ExpressionStatement(r))},e.prototype.parseDirectivePrologues=function(){for(var e=null,t=[];;){var n=this.lookahead;if(n.type!==s.Token.StringLiteral)break;var r=this.parseDirective();t.push(r);var o=r.directive;if("string"!=typeof o)break;"use strict"===o?(this.context.strict=!0,e&&this.tolerateUnexpectedToken(e,i.Messages.StrictOctalLiteral)):!e&&n.octal&&(e=n)}return t},e.prototype.qualifiedPropertyName=function(e){switch(e.type){case s.Token.Identifier:case s.Token.StringLiteral:case s.Token.BooleanLiteral:case s.Token.NullLiteral:case s.Token.NumericLiteral:case s.Token.Keyword:return!0;case s.Token.Punctuator:return"["===e.value}return!1},e.prototype.parseGetterMethod=function(){var e=this.createNode();this.expect("("),this.expect(")");var t=!1,n={params:[],stricted:null,firstRestricted:null,message:null},r=this.context.allowYield;this.context.allowYield=!1;var i=this.parsePropertyMethod(n);return this.context.allowYield=r,this.finalize(e,new c.FunctionExpression(null,n.params,i,t))},e.prototype.parseSetterMethod=function(){var e=this.createNode(),t={params:[],firstRestricted:null,paramSet:{}},n=!1,r=this.context.allowYield;this.context.allowYield=!1,this.expect("("),this.match(")")?this.tolerateUnexpectedToken(this.lookahead):this.parseFormalParameter(t),this.expect(")");var i=this.parsePropertyMethod(t);return this.context.allowYield=r,this.finalize(e,new c.FunctionExpression(null,t.params,i,n))},e.prototype.parseGeneratorMethod=function(){var e=this.createNode(),t=!0,n=this.context.allowYield;this.context.allowYield=!0;var r=this.parseFormalParameters();this.context.allowYield=!1;var i=this.parsePropertyMethod(r);return this.context.allowYield=n,this.finalize(e,new c.FunctionExpression(null,r.params,i,t))},e.prototype.isStartOfExpression=function(){var e=!0,t=this.lookahead.value;switch(this.lookahead.type){case s.Token.Punctuator:e="["===t||"("===t||"{"===t||"+"===t||"-"===t||"!"===t||"~"===t||"++"===t||"--"===t||"/"===t||"/="===t;break;case s.Token.Keyword:e="class"===t||"delete"===t||"function"===t||"let"===t||"new"===t||"super"===t||"this"===t||"typeof"===t||"void"===t||"yield"===t}return e},e.prototype.parseYieldExpression=function(){var e=this.createNode();this.expectKeyword("yield");var t=null,n=!1;if(!this.hasLineTerminator){var r=this.context.allowYield;this.context.allowYield=!1,n=this.match("*"),n?(this.nextToken(),t=this.parseAssignmentExpression()):this.isStartOfExpression()&&(t=this.parseAssignmentExpression()),this.context.allowYield=r}return this.finalize(e,new c.YieldExpression(t,n))},e.prototype.parseClassElement=function(e){var t,n,r,o=this.lookahead,a=this.createNode(),u=!1,l=!1,h=!1;if(this.match("*"))this.nextToken();else{u=this.match("["),n=this.parseObjectPropertyKey();var p=n;"static"===p.name&&(this.qualifiedPropertyName(this.lookahead)||this.match("*"))&&(o=this.lookahead,h=!0,u=this.match("["),this.match("*")?this.nextToken():n=this.parseObjectPropertyKey())}var f=this.qualifiedPropertyName(this.lookahead);return o.type===s.Token.Identifier?"get"===o.value&&f?(t="get",u=this.match("["),n=this.parseObjectPropertyKey(),this.context.allowYield=!1,r=this.parseGetterMethod()):"set"===o.value&&f&&(t="set",u=this.match("["),n=this.parseObjectPropertyKey(),r=this.parseSetterMethod()):o.type===s.Token.Punctuator&&"*"===o.value&&f&&(t="init",u=this.match("["),n=this.parseObjectPropertyKey(),r=this.parseGeneratorMethod(),l=!0),!t&&n&&this.match("(")&&(t="init",r=this.parsePropertyMethodFunction(),l=!0),t||this.throwUnexpectedToken(this.lookahead),"init"===t&&(t="method"),u||(h&&this.isPropertyKey(n,"prototype")&&this.throwUnexpectedToken(o,i.Messages.StaticPrototype),!h&&this.isPropertyKey(n,"constructor")&&("method"===t&&l&&!r.generator||this.throwUnexpectedToken(o,i.Messages.ConstructorSpecialMethod),e.value?this.throwUnexpectedToken(o,i.Messages.DuplicateConstructor):e.value=!0,t="constructor")),this.finalize(a,new c.MethodDefinition(n,u,r,t,h))},e.prototype.parseClassElementList=function(){var e=[],t={value:!1};for(this.expect("{");!this.match("}");)this.match(";")?this.nextToken():e.push(this.parseClassElement(t));return this.expect("}"),e},e.prototype.parseClassBody=function(){var e=this.createNode(),t=this.parseClassElementList();return this.finalize(e,new c.ClassBody(t))},e.prototype.parseClassDeclaration=function(e){var t=this.createNode(),n=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var r=e&&this.lookahead.type!==s.Token.Identifier?null:this.parseVariableIdentifier(),i=null;this.matchKeyword("extends")&&(this.nextToken(),i=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var o=this.parseClassBody();return this.context.strict=n,this.finalize(t,new c.ClassDeclaration(r,i,o))},e.prototype.parseClassExpression=function(){var e=this.createNode(),t=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var n=this.lookahead.type===s.Token.Identifier?this.parseVariableIdentifier():null,r=null;this.matchKeyword("extends")&&(this.nextToken(),r=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var i=this.parseClassBody();return this.context.strict=t,this.finalize(e,new c.ClassExpression(n,r,i))},e.prototype.parseProgram=function(){for(var e=this.createNode(),t=this.parseDirectivePrologues();this.startMarker.index<this.scanner.length;)t.push(this.parseStatementListItem());return this.finalize(e,new c.Program(t,this.sourceType))},e.prototype.parseModuleSpecifier=function(){var e=this.createNode();this.lookahead.type!==s.Token.StringLiteral&&this.throwError(i.Messages.InvalidModuleSpecifier);var t=this.nextToken(),n=this.getTokenRaw(t);return this.finalize(e,new c.Literal(t.value,n))},e.prototype.parseImportSpecifier=function(){var e,t,n=this.createNode();return this.lookahead.type===s.Token.Identifier?(e=this.parseVariableIdentifier(),t=e,this.matchContextualKeyword("as")&&(this.nextToken(),t=this.parseVariableIdentifier())):(e=this.parseIdentifierName(),t=e,this.matchContextualKeyword("as")?(this.nextToken(),t=this.parseVariableIdentifier()):this.throwUnexpectedToken(this.nextToken())),this.finalize(n,new c.ImportSpecifier(t,e))},e.prototype.parseNamedImports=function(){this.expect("{");for(var e=[];!this.match("}");)e.push(this.parseImportSpecifier()),this.match("}")||this.expect(",");return this.expect("}"),e},e.prototype.parseImportDefaultSpecifier=function(){var e=this.createNode(),t=this.parseIdentifierName();return this.finalize(e,new c.ImportDefaultSpecifier(t))},e.prototype.parseImportNamespaceSpecifier=function(){var e=this.createNode();this.expect("*"),this.matchContextualKeyword("as")||this.throwError(i.Messages.NoAsAfterImportNamespace),this.nextToken();var t=this.parseIdentifierName();return this.finalize(e,new c.ImportNamespaceSpecifier(t))},e.prototype.parseImportDeclaration=function(){this.context.inFunctionBody&&this.throwError(i.Messages.IllegalImportDeclaration);var e=this.createNode();this.expectKeyword("import");var t,n=[];if(this.lookahead.type===s.Token.StringLiteral)t=this.parseModuleSpecifier();else{if(this.match("{")?n=n.concat(this.parseNamedImports()):this.match("*")?n.push(this.parseImportNamespaceSpecifier()):this.isIdentifierName(this.lookahead)&&!this.matchKeyword("default")?(n.push(this.parseImportDefaultSpecifier()),this.match(",")&&(this.nextToken(),this.match("*")?n.push(this.parseImportNamespaceSpecifier()):this.match("{")?n=n.concat(this.parseNamedImports()):this.throwUnexpectedToken(this.lookahead))):this.throwUnexpectedToken(this.nextToken()),!this.matchContextualKeyword("from")){var r=this.lookahead.value?i.Messages.UnexpectedToken:i.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}this.nextToken(),t=this.parseModuleSpecifier()}return this.consumeSemicolon(),this.finalize(e,new c.ImportDeclaration(n,t))},e.prototype.parseExportSpecifier=function(){var e=this.createNode(),t=this.parseIdentifierName(),n=t;return this.matchContextualKeyword("as")&&(this.nextToken(),n=this.parseIdentifierName()),this.finalize(e,new c.ExportSpecifier(t,n))},e.prototype.parseExportDeclaration=function(){this.context.inFunctionBody&&this.throwError(i.Messages.IllegalExportDeclaration);var e=this.createNode();this.expectKeyword("export");var t;if(this.matchKeyword("default"))if(this.nextToken(),this.matchKeyword("function")){var n=this.parseFunctionDeclaration(!0);t=this.finalize(e,new c.ExportDefaultDeclaration(n))}else if(this.matchKeyword("class")){var n=this.parseClassDeclaration(!0);t=this.finalize(e,new c.ExportDefaultDeclaration(n))}else{this.matchContextualKeyword("from")&&this.throwError(i.Messages.UnexpectedToken,this.lookahead.value);var n=this.match("{")?this.parseObjectInitializer():this.match("[")?this.parseArrayInitializer():this.parseAssignmentExpression();this.consumeSemicolon(),t=this.finalize(e,new c.ExportDefaultDeclaration(n))}else if(this.match("*")){if(this.nextToken(),!this.matchContextualKeyword("from")){var r=this.lookahead.value?i.Messages.UnexpectedToken:i.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}this.nextToken();var o=this.parseModuleSpecifier();this.consumeSemicolon(),t=this.finalize(e,new c.ExportAllDeclaration(o))}else if(this.lookahead.type===s.Token.Keyword){var n=void 0;switch(this.lookahead.value){case"let":case"const":n=this.parseLexicalDeclaration({inFor:!1});break;case"var":case"class":case"function":n=this.parseStatementListItem();break;default:this.throwUnexpectedToken(this.lookahead)}t=this.finalize(e,new c.ExportNamedDeclaration(n,[],null))}else{var a=[],u=null,l=!1;for(this.expect("{");!this.match("}");)l=l||this.matchKeyword("default"),a.push(this.parseExportSpecifier()),this.match("}")||this.expect(",");if(this.expect("}"),this.matchContextualKeyword("from"))this.nextToken(),u=this.parseModuleSpecifier(),this.consumeSemicolon();else if(l){var r=this.lookahead.value?i.Messages.UnexpectedToken:i.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}else this.consumeSemicolon();t=this.finalize(e,new c.ExportNamedDeclaration(null,a,u))}return t},e}();t.Parser=h},function(e,t){"use strict";function n(e,t){if(!e)throw new Error("ASSERT: "+t)}t.assert=n},function(e,t){"use strict";t.Messages={UnexpectedToken:"Unexpected token %0",UnexpectedTokenIllegal:"Unexpected token ILLEGAL",UnexpectedNumber:"Unexpected number",UnexpectedString:"Unexpected string",UnexpectedIdentifier:"Unexpected identifier",UnexpectedReserved:"Unexpected reserved word",UnexpectedTemplate:"Unexpected quasi %0",UnexpectedEOS:"Unexpected end of input",NewlineAfterThrow:"Illegal newline after throw",InvalidRegExp:"Invalid regular expression",UnterminatedRegExp:"Invalid regular expression: missing /",InvalidLHSInAssignment:"Invalid left-hand side in assignment",InvalidLHSInForIn:"Invalid left-hand side in for-in",InvalidLHSInForLoop:"Invalid left-hand side in for-loop",MultipleDefaultsInSwitch:"More than one default clause in switch statement",NoCatchOrFinally:"Missing catch or finally after try",UnknownLabel:"Undefined label '%0'",Redeclaration:"%0 '%1' has already been declared",IllegalContinue:"Illegal continue statement",IllegalBreak:"Illegal break statement",IllegalReturn:"Illegal return statement",StrictModeWith:"Strict mode code may not include a with statement",StrictCatchVariable:"Catch variable may not be eval or arguments in strict mode",StrictVarName:"Variable name may not be eval or arguments in strict mode",StrictParamName:"Parameter name eval or arguments is not allowed in strict mode",StrictParamDupe:"Strict mode function may not have duplicate parameter names",StrictFunctionName:"Function name may not be eval or arguments in strict mode",StrictOctalLiteral:"Octal literals are not allowed in strict mode.",StrictDelete:"Delete of an unqualified identifier in strict mode.",StrictLHSAssignment:"Assignment to eval or arguments is not allowed in strict mode",StrictLHSPostfix:"Postfix increment/decrement may not have eval or arguments operand in strict mode",StrictLHSPrefix:"Prefix increment/decrement may not have eval or arguments operand in strict mode",StrictReservedWord:"Use of future reserved word in strict mode",TemplateOctalLiteral:"Octal literals are not allowed in template strings.",ParameterAfterRestParameter:"Rest parameter must be last formal parameter",DefaultRestParameter:"Unexpected token =",DuplicateProtoProperty:"Duplicate __proto__ fields are not allowed in object literals",ConstructorSpecialMethod:"Class constructor may not be an accessor",DuplicateConstructor:"A class may only have one constructor",StaticPrototype:"Classes may not have static property named prototype",MissingFromClause:"Unexpected token",NoAsAfterImportNamespace:"Unexpected token",InvalidModuleSpecifier:"Unexpected token",IllegalImportDeclaration:"Unexpected token",IllegalExportDeclaration:"Unexpected token",DuplicateBinding:"Duplicate binding %0",ForInOfLoopInitializer:"%0 loop variable declaration may not have an initializer"}},function(e,t){"use strict";var n=function(){function e(){this.errors=[],this.tolerant=!1}return e.prototype.recordError=function(e){this.errors.push(e)},e.prototype.tolerate=function(e){if(!this.tolerant)throw e;this.recordError(e)},e.prototype.constructError=function(e,t){var n=new Error(e);try{throw n}catch(e){Object.create&&Object.defineProperty&&(n=Object.create(e),Object.defineProperty(n,"column",{value:t}))}finally{return n}},e.prototype.createError=function(e,t,n,r){var i="Line "+t+": "+r,o=this.constructError(i,n);return o.index=e,o.lineNumber=t,o.description=r,o},e.prototype.throwError=function(e,t,n,r){throw this.createError(e,t,n,r)},e.prototype.tolerateError=function(e,t,n,r){var i=this.createError(e,t,n,r);if(!this.tolerant)throw i;this.recordError(i)},e}();t.ErrorHandler=n},function(e,t){"use strict";!function(e){e[e.BooleanLiteral=1]="BooleanLiteral",
e[e.EOF=2]="EOF",e[e.Identifier=3]="Identifier",e[e.Keyword=4]="Keyword",e[e.NullLiteral=5]="NullLiteral",e[e.NumericLiteral=6]="NumericLiteral",e[e.Punctuator=7]="Punctuator",e[e.StringLiteral=8]="StringLiteral",e[e.RegularExpression=9]="RegularExpression",e[e.Template=10]="Template"}(t.Token||(t.Token={}));var n=t.Token;t.TokenName={},t.TokenName[n.BooleanLiteral]="Boolean",t.TokenName[n.EOF]="<end>",t.TokenName[n.Identifier]="Identifier",t.TokenName[n.Keyword]="Keyword",t.TokenName[n.NullLiteral]="Null",t.TokenName[n.NumericLiteral]="Numeric",t.TokenName[n.Punctuator]="Punctuator",t.TokenName[n.StringLiteral]="String",t.TokenName[n.RegularExpression]="RegularExpression",t.TokenName[n.Template]="Template"},function(e,t,n){"use strict";function r(e){return"**********abcdef".indexOf(e.toLowerCase())}function i(e){return"01234567".indexOf(e)}var o=n(4),s=n(5),a=n(9),u=n(7),c=function(){function e(e,t){this.source=e,this.errorHandler=t,this.trackComment=!1,this.length=e.length,this.index=0,this.lineNumber=e.length>0?1:0,this.lineStart=0,this.curlyStack=[]}return e.prototype.eof=function(){return this.index>=this.length},e.prototype.throwUnexpectedToken=function(e){void 0===e&&(e=s.Messages.UnexpectedTokenIllegal),this.errorHandler.throwError(this.index,this.lineNumber,this.index-this.lineStart+1,e)},e.prototype.tolerateUnexpectedToken=function(){this.errorHandler.tolerateError(this.index,this.lineNumber,this.index-this.lineStart+1,s.Messages.UnexpectedTokenIllegal)},e.prototype.skipSingleLineComment=function(e){var t,n,r;for(this.trackComment&&(t=[],n=this.index-e,r={start:{line:this.lineNumber,column:this.index-this.lineStart-e},end:{}});!this.eof();){var i=this.source.charCodeAt(this.index);if(++this.index,a.Character.isLineTerminator(i)){if(this.trackComment){r.end={line:this.lineNumber,column:this.index-this.lineStart-1};var o={multiLine:!1,slice:[n+e,this.index-1],range:[n,this.index-1],loc:r};t.push(o)}return 13===i&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,t}}if(this.trackComment){r.end={line:this.lineNumber,column:this.index-this.lineStart};var o={multiLine:!1,slice:[n+e,this.index],range:[n,this.index],loc:r};t.push(o)}return t},e.prototype.skipMultiLineComment=function(){var e,t,n;for(this.trackComment&&(e=[],t=this.index-2,n={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{}});!this.eof();){var r=this.source.charCodeAt(this.index);if(a.Character.isLineTerminator(r))13===r&&10===this.source.charCodeAt(this.index+1)&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(42===r){if(47===this.source.charCodeAt(this.index+1)){if(this.index+=2,this.trackComment){n.end={line:this.lineNumber,column:this.index-this.lineStart};var i={multiLine:!0,slice:[t+2,this.index-2],range:[t,this.index],loc:n};e.push(i)}return e}++this.index}else++this.index}if(this.trackComment){n.end={line:this.lineNumber,column:this.index-this.lineStart};var i={multiLine:!0,slice:[t+2,this.index],range:[t,this.index],loc:n};e.push(i)}return this.tolerateUnexpectedToken(),e},e.prototype.scanComments=function(){var e;this.trackComment&&(e=[]);for(var t=0===this.index;!this.eof();){var n=this.source.charCodeAt(this.index);if(a.Character.isWhiteSpace(n))++this.index;else if(a.Character.isLineTerminator(n))++this.index,13===n&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,t=!0;else if(47===n)if(n=this.source.charCodeAt(this.index+1),47===n){this.index+=2;var r=this.skipSingleLineComment(2);this.trackComment&&(e=e.concat(r)),t=!0}else{if(42!==n)break;this.index+=2;var r=this.skipMultiLineComment();this.trackComment&&(e=e.concat(r))}else if(t&&45===n){if(45!==this.source.charCodeAt(this.index+1)||62!==this.source.charCodeAt(this.index+2))break;this.index+=3;var r=this.skipSingleLineComment(3);this.trackComment&&(e=e.concat(r))}else{if(60!==n)break;if("!--"!==this.source.slice(this.index+1,this.index+4))break;this.index+=4;var r=this.skipSingleLineComment(4);this.trackComment&&(e=e.concat(r))}}return e},e.prototype.isFutureReservedWord=function(e){switch(e){case"enum":case"export":case"import":case"super":return!0;default:return!1}},e.prototype.isStrictModeReservedWord=function(e){switch(e){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"yield":case"let":return!0;default:return!1}},e.prototype.isRestrictedWord=function(e){return"eval"===e||"arguments"===e},e.prototype.isKeyword=function(e){switch(e.length){case 2:return"if"===e||"in"===e||"do"===e;case 3:return"var"===e||"for"===e||"new"===e||"try"===e||"let"===e;case 4:return"this"===e||"else"===e||"case"===e||"void"===e||"with"===e||"enum"===e;case 5:return"while"===e||"break"===e||"catch"===e||"throw"===e||"const"===e||"yield"===e||"class"===e||"super"===e;case 6:return"return"===e||"typeof"===e||"delete"===e||"switch"===e||"export"===e||"import"===e;case 7:return"default"===e||"finally"===e||"extends"===e;case 8:return"function"===e||"continue"===e||"debugger"===e;case 10:return"instanceof"===e;default:return!1}},e.prototype.codePointAt=function(e){var t=this.source.charCodeAt(e);if(t>=55296&&t<=56319){var n=this.source.charCodeAt(e+1);if(n>=56320&&n<=57343){var r=t;t=1024*(r-55296)+n-56320+65536}}return t},e.prototype.scanHexEscape=function(e){for(var t="u"===e?4:2,n=0,i=0;i<t;++i){if(this.eof()||!a.Character.isHexDigit(this.source.charCodeAt(this.index)))return"";n=16*n+r(this.source[this.index++])}return String.fromCharCode(n)},e.prototype.scanUnicodeCodePointEscape=function(){var e=this.source[this.index],t=0;for("}"===e&&this.throwUnexpectedToken();!this.eof()&&(e=this.source[this.index++],a.Character.isHexDigit(e.charCodeAt(0)));)t=16*t+r(e);return(t>1114111||"}"!==e)&&this.throwUnexpectedToken(),a.Character.fromCodePoint(t)},e.prototype.getIdentifier=function(){for(var e=this.index++;!this.eof();){var t=this.source.charCodeAt(this.index);if(92===t)return this.index=e,this.getComplexIdentifier();if(t>=55296&&t<57343)return this.index=e,this.getComplexIdentifier();if(!a.Character.isIdentifierPart(t))break;++this.index}return this.source.slice(e,this.index)},e.prototype.getComplexIdentifier=function(){var e=this.codePointAt(this.index),t=a.Character.fromCodePoint(e);this.index+=t.length;var n;for(92===e&&(117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,n=this.scanUnicodeCodePointEscape()):(n=this.scanHexEscape("u"),e=n.charCodeAt(0),n&&"\\"!==n&&a.Character.isIdentifierStart(e)||this.throwUnexpectedToken()),t=n);!this.eof()&&(e=this.codePointAt(this.index),a.Character.isIdentifierPart(e));)n=a.Character.fromCodePoint(e),t+=n,this.index+=n.length,92===e&&(t=t.substr(0,t.length-1),117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,n=this.scanUnicodeCodePointEscape()):(n=this.scanHexEscape("u"),e=n.charCodeAt(0),n&&"\\"!==n&&a.Character.isIdentifierPart(e)||this.throwUnexpectedToken()),t+=n);return t},e.prototype.octalToDecimal=function(e){var t="0"!==e,n=i(e);return!this.eof()&&a.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(t=!0,n=8*n+i(this.source[this.index++]),"0123".indexOf(e)>=0&&!this.eof()&&a.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(n=8*n+i(this.source[this.index++]))),{code:n,octal:t}},e.prototype.scanIdentifier=function(){var e,t=this.index,n=92===this.source.charCodeAt(t)?this.getComplexIdentifier():this.getIdentifier();return e=1===n.length?u.Token.Identifier:this.isKeyword(n)?u.Token.Keyword:"null"===n?u.Token.NullLiteral:"true"===n||"false"===n?u.Token.BooleanLiteral:u.Token.Identifier,{type:e,value:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},e.prototype.scanPunctuator=function(){var e={type:u.Token.Punctuator,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index},t=this.source[this.index];switch(t){case"(":case"{":"{"===t&&this.curlyStack.push("{"),++this.index;break;case".":++this.index,"."===this.source[this.index]&&"."===this.source[this.index+1]&&(this.index+=2,t="...");break;case"}":++this.index,this.curlyStack.pop();break;case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;default:t=this.source.substr(this.index,4),">>>="===t?this.index+=4:(t=t.substr(0,3),"==="===t||"!=="===t||">>>"===t||"<<="===t||">>="===t||"**="===t?this.index+=3:(t=t.substr(0,2),"&&"===t||"||"===t||"=="===t||"!="===t||"+="===t||"-="===t||"*="===t||"/="===t||"++"===t||"--"===t||"<<"===t||">>"===t||"&="===t||"|="===t||"^="===t||"%="===t||"<="===t||">="===t||"=>"===t||"**"===t?this.index+=2:(t=this.source[this.index],"<>=!+-*%&|^/".indexOf(t)>=0&&++this.index)))}return this.index===e.start&&this.throwUnexpectedToken(),e.end=this.index,e.value=t,e},e.prototype.scanHexLiteral=function(e){for(var t="";!this.eof()&&a.Character.isHexDigit(this.source.charCodeAt(this.index));)t+=this.source[this.index++];return 0===t.length&&this.throwUnexpectedToken(),a.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:u.Token.NumericLiteral,value:parseInt("0x"+t,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanBinaryLiteral=function(e){for(var t,n="";!this.eof()&&(t=this.source[this.index],"0"===t||"1"===t);)n+=this.source[this.index++];return 0===n.length&&this.throwUnexpectedToken(),this.eof()||(t=this.source.charCodeAt(this.index),(a.Character.isIdentifierStart(t)||a.Character.isDecimalDigit(t))&&this.throwUnexpectedToken()),{type:u.Token.NumericLiteral,value:parseInt(n,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanOctalLiteral=function(e,t){var n="",r=!1;for(a.Character.isOctalDigit(e.charCodeAt(0))?(r=!0,n="0"+this.source[this.index++]):++this.index;!this.eof()&&a.Character.isOctalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];return r||0!==n.length||this.throwUnexpectedToken(),(a.Character.isIdentifierStart(this.source.charCodeAt(this.index))||a.Character.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:u.Token.NumericLiteral,value:parseInt(n,8),octal:r,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},e.prototype.isImplicitOctalLiteral=function(){for(var e=this.index+1;e<this.length;++e){var t=this.source[e];if("8"===t||"9"===t)return!1;if(!a.Character.isOctalDigit(t.charCodeAt(0)))return!0}return!0},e.prototype.scanNumericLiteral=function(){var e=this.index,t=this.source[e];o.assert(a.Character.isDecimalDigit(t.charCodeAt(0))||"."===t,"Numeric literal must start with a decimal digit or a decimal point");var n="";if("."!==t){if(n=this.source[this.index++],t=this.source[this.index],"0"===n){if("x"===t||"X"===t)return++this.index,this.scanHexLiteral(e);if("b"===t||"B"===t)return++this.index,this.scanBinaryLiteral(e);if("o"===t||"O"===t)return this.scanOctalLiteral(t,e);if(t&&a.Character.isOctalDigit(t.charCodeAt(0))&&this.isImplicitOctalLiteral())return this.scanOctalLiteral(t,e)}for(;a.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];t=this.source[this.index]}if("."===t){for(n+=this.source[this.index++];a.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];t=this.source[this.index]}if("e"===t||"E"===t)if(n+=this.source[this.index++],t=this.source[this.index],"+"!==t&&"-"!==t||(n+=this.source[this.index++]),a.Character.isDecimalDigit(this.source.charCodeAt(this.index)))for(;a.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];else this.throwUnexpectedToken();return a.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:u.Token.NumericLiteral,value:parseFloat(n),lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanStringLiteral=function(){var e=this.index,t=this.source[e];o.assert("'"===t||'"'===t,"String literal must starts with a quote"),++this.index;for(var n=!1,r="";!this.eof();){var i=this.source[this.index++];if(i===t){t="";break}if("\\"===i)if(i=this.source[this.index++],i&&a.Character.isLineTerminator(i.charCodeAt(0)))++this.lineNumber,"\r"===i&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(i){case"u":case"x":if("{"===this.source[this.index])++this.index,r+=this.scanUnicodeCodePointEscape();else{var s=this.scanHexEscape(i);s||this.throwUnexpectedToken(),r+=s}break;case"n":r+="\n";break;case"r":r+="\r";break;case"t":r+="\t";break;case"b":r+="\b";break;case"f":r+="\f";break;case"v":r+="\v";break;case"8":case"9":r+=i,this.tolerateUnexpectedToken();break;default:if(i&&a.Character.isOctalDigit(i.charCodeAt(0))){var c=this.octalToDecimal(i);n=c.octal||n,r+=String.fromCharCode(c.code)}else r+=i}else{if(a.Character.isLineTerminator(i.charCodeAt(0)))break;r+=i}}return""!==t&&(this.index=e,this.throwUnexpectedToken()),{type:u.Token.StringLiteral,value:r,octal:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.scanTemplate=function(){var e="",t=!1,n=this.index,r="`"===this.source[n],i=!1,o=2;for(++this.index;!this.eof();){var c=this.source[this.index++];if("`"===c){o=1,i=!0,t=!0;break}if("$"===c){if("{"===this.source[this.index]){this.curlyStack.push("${"),++this.index,t=!0;break}e+=c}else if("\\"===c)if(c=this.source[this.index++],a.Character.isLineTerminator(c.charCodeAt(0)))++this.lineNumber,"\r"===c&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(c){case"n":e+="\n";break;case"r":e+="\r";break;case"t":e+="\t";break;case"u":case"x":if("{"===this.source[this.index])++this.index,e+=this.scanUnicodeCodePointEscape();else{var l=this.index,h=this.scanHexEscape(c);h?e+=h:(this.index=l,e+=c)}break;case"b":e+="\b";break;case"f":e+="\f";break;case"v":e+="\v";break;default:"0"===c?(a.Character.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral),e+="\0"):a.Character.isOctalDigit(c.charCodeAt(0))?this.throwUnexpectedToken(s.Messages.TemplateOctalLiteral):e+=c}else a.Character.isLineTerminator(c.charCodeAt(0))?(++this.lineNumber,"\r"===c&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index,e+="\n"):e+=c}return t||this.throwUnexpectedToken(),r||this.curlyStack.pop(),{type:u.Token.Template,value:{cooked:e,raw:this.source.slice(n+1,this.index-o)},head:r,tail:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:n,end:this.index}},e.prototype.testRegExp=function(e,t){var n="￿",r=e,i=this;t.indexOf("u")>=0&&(r=r.replace(/\\u\{([0-9a-fA-F]+)\}|\\u([a-fA-F0-9]{4})/g,function(e,t,r){var o=parseInt(t||r,16);return o>1114111&&i.throwUnexpectedToken(s.Messages.InvalidRegExp),o<=65535?String.fromCharCode(o):n}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,n));try{RegExp(r)}catch(e){this.throwUnexpectedToken(s.Messages.InvalidRegExp)}try{return new RegExp(e,t)}catch(e){return null}},e.prototype.scanRegExpBody=function(){var e=this.source[this.index];o.assert("/"===e,"Regular expression literal must start with a slash");for(var t=this.source[this.index++],n=!1,r=!1;!this.eof();)if(e=this.source[this.index++],t+=e,"\\"===e)e=this.source[this.index++],a.Character.isLineTerminator(e.charCodeAt(0))&&this.throwUnexpectedToken(s.Messages.UnterminatedRegExp),t+=e;else if(a.Character.isLineTerminator(e.charCodeAt(0)))this.throwUnexpectedToken(s.Messages.UnterminatedRegExp);else if(n)"]"===e&&(n=!1);else{if("/"===e){r=!0;break}"["===e&&(n=!0)}r||this.throwUnexpectedToken(s.Messages.UnterminatedRegExp);var i=t.substr(1,t.length-2);return{value:i,literal:t}},e.prototype.scanRegExpFlags=function(){for(var e="",t="";!this.eof();){var n=this.source[this.index];if(!a.Character.isIdentifierPart(n.charCodeAt(0)))break;if(++this.index,"\\"!==n||this.eof())t+=n,e+=n;else if(n=this.source[this.index],"u"===n){++this.index;var r=this.index;if(n=this.scanHexEscape("u"))for(t+=n,e+="\\u";r<this.index;++r)e+=this.source[r];else this.index=r,t+="u",e+="\\u";this.tolerateUnexpectedToken()}else e+="\\",this.tolerateUnexpectedToken()}return{value:t,literal:e}},e.prototype.scanRegExp=function(){var e=this.index,t=this.scanRegExpBody(),n=this.scanRegExpFlags(),r=this.testRegExp(t.value,n.value);return{type:u.Token.RegularExpression,value:r,literal:t.literal+n.literal,regex:{pattern:t.value,flags:n.value},lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},e.prototype.lex=function(){if(this.eof())return{type:u.Token.EOF,lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};var e=this.source.charCodeAt(this.index);return a.Character.isIdentifierStart(e)?this.scanIdentifier():40===e||41===e||59===e?this.scanPunctuator():39===e||34===e?this.scanStringLiteral():46===e?a.Character.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():a.Character.isDecimalDigit(e)?this.scanNumericLiteral():96===e||125===e&&"${"===this.curlyStack[this.curlyStack.length-1]?this.scanTemplate():e>=55296&&e<57343&&a.Character.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()},e}();t.Scanner=c},function(e,t){"use strict";var n={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/
};t.Character={fromCodePoint:function(e){return e<65536?String.fromCharCode(e):String.fromCharCode(55296+(e-65536>>10))+String.fromCharCode(56320+(e-65536&1023))},isWhiteSpace:function(e){return 32===e||9===e||11===e||12===e||160===e||e>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(e)>=0},isLineTerminator:function(e){return 10===e||13===e||8232===e||8233===e},isIdentifierStart:function(e){return 36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||92===e||e>=128&&n.NonAsciiIdentifierStart.test(t.Character.fromCodePoint(e))},isIdentifierPart:function(e){return 36===e||95===e||e>=65&&e<=90||e>=97&&e<=122||e>=48&&e<=57||92===e||e>=128&&n.NonAsciiIdentifierPart.test(t.Character.fromCodePoint(e))},isDecimalDigit:function(e){return e>=48&&e<=57},isHexDigit:function(e){return e>=48&&e<=57||e>=65&&e<=70||e>=97&&e<=102},isOctalDigit:function(e){return e>=48&&e<=55}}},function(e,t,n){"use strict";var r=n(2),i=function(){function e(e){this.type=r.Syntax.ArrayExpression,this.elements=e}return e}();t.ArrayExpression=i;var o=function(){function e(e){this.type=r.Syntax.ArrayPattern,this.elements=e}return e}();t.ArrayPattern=o;var s=function(){function e(e,t,n){this.type=r.Syntax.ArrowFunctionExpression,this.id=null,this.params=e,this.body=t,this.generator=!1,this.expression=n}return e}();t.ArrowFunctionExpression=s;var a=function(){function e(e,t,n){this.type=r.Syntax.AssignmentExpression,this.operator=e,this.left=t,this.right=n}return e}();t.AssignmentExpression=a;var u=function(){function e(e,t){this.type=r.Syntax.AssignmentPattern,this.left=e,this.right=t}return e}();t.AssignmentPattern=u;var c=function(){function e(e,t,n){var i="||"===e||"&&"===e;this.type=i?r.Syntax.LogicalExpression:r.Syntax.BinaryExpression,this.operator=e,this.left=t,this.right=n}return e}();t.BinaryExpression=c;var l=function(){function e(e){this.type=r.Syntax.BlockStatement,this.body=e}return e}();t.BlockStatement=l;var h=function(){function e(e){this.type=r.Syntax.BreakStatement,this.label=e}return e}();t.BreakStatement=h;var p=function(){function e(e,t){this.type=r.Syntax.CallExpression,this.callee=e,this.arguments=t}return e}();t.CallExpression=p;var f=function(){function e(e,t){this.type=r.Syntax.CatchClause,this.param=e,this.body=t}return e}();t.CatchClause=f;var d=function(){function e(e){this.type=r.Syntax.ClassBody,this.body=e}return e}();t.ClassBody=d;var m=function(){function e(e,t,n){this.type=r.Syntax.ClassDeclaration,this.id=e,this.superClass=t,this.body=n}return e}();t.ClassDeclaration=m;var g=function(){function e(e,t,n){this.type=r.Syntax.ClassExpression,this.id=e,this.superClass=t,this.body=n}return e}();t.ClassExpression=g;var x=function(){function e(e,t){this.type=r.Syntax.MemberExpression,this.computed=!0,this.object=e,this.property=t}return e}();t.ComputedMemberExpression=x;var y=function(){function e(e,t,n){this.type=r.Syntax.ConditionalExpression,this.test=e,this.consequent=t,this.alternate=n}return e}();t.ConditionalExpression=y;var v=function(){function e(e){this.type=r.Syntax.ContinueStatement,this.label=e}return e}();t.ContinueStatement=v;var b=function(){function e(){this.type=r.Syntax.DebuggerStatement}return e}();t.DebuggerStatement=b;var D=function(){function e(e,t){this.type=r.Syntax.ExpressionStatement,this.expression=e,this.directive=t}return e}();t.Directive=D;var w=function(){function e(e,t){this.type=r.Syntax.DoWhileStatement,this.body=e,this.test=t}return e}();t.DoWhileStatement=w;var E=function(){function e(){this.type=r.Syntax.EmptyStatement}return e}();t.EmptyStatement=E;var k=function(){function e(e){this.type=r.Syntax.ExportAllDeclaration,this.source=e}return e}();t.ExportAllDeclaration=k;var A=function(){function e(e){this.type=r.Syntax.ExportDefaultDeclaration,this.declaration=e}return e}();t.ExportDefaultDeclaration=A;var C=function(){function e(e,t,n){this.type=r.Syntax.ExportNamedDeclaration,this.declaration=e,this.specifiers=t,this.source=n}return e}();t.ExportNamedDeclaration=C;var S=function(){function e(e,t){this.type=r.Syntax.ExportSpecifier,this.exported=t,this.local=e}return e}();t.ExportSpecifier=S;var F=function(){function e(e){this.type=r.Syntax.ExpressionStatement,this.expression=e}return e}();t.ExpressionStatement=F;var T=function(){function e(e,t,n){this.type=r.Syntax.ForInStatement,this.left=e,this.right=t,this.body=n,this.each=!1}return e}();t.ForInStatement=T;var B=function(){function e(e,t,n){this.type=r.Syntax.ForOfStatement,this.left=e,this.right=t,this.body=n}return e}();t.ForOfStatement=B;var N=function(){function e(e,t,n,i){this.type=r.Syntax.ForStatement,this.init=e,this.test=t,this.update=n,this.body=i}return e}();t.ForStatement=N;var I=function(){function e(e,t,n,i){this.type=r.Syntax.FunctionDeclaration,this.id=e,this.params=t,this.body=n,this.generator=i,this.expression=!1}return e}();t.FunctionDeclaration=I;var P=function(){function e(e,t,n,i){this.type=r.Syntax.FunctionExpression,this.id=e,this.params=t,this.body=n,this.generator=i,this.expression=!1}return e}();t.FunctionExpression=P;var M=function(){function e(e){this.type=r.Syntax.Identifier,this.name=e}return e}();t.Identifier=M;var O=function(){function e(e,t,n){this.type=r.Syntax.IfStatement,this.test=e,this.consequent=t,this.alternate=n}return e}();t.IfStatement=O;var _=function(){function e(e,t){this.type=r.Syntax.ImportDeclaration,this.specifiers=e,this.source=t}return e}();t.ImportDeclaration=_;var L=function(){function e(e){this.type=r.Syntax.ImportDefaultSpecifier,this.local=e}return e}();t.ImportDefaultSpecifier=L;var R=function(){function e(e){this.type=r.Syntax.ImportNamespaceSpecifier,this.local=e}return e}();t.ImportNamespaceSpecifier=R;var U=function(){function e(e,t){this.type=r.Syntax.ImportSpecifier,this.local=e,this.imported=t}return e}();t.ImportSpecifier=U;var j=function(){function e(e,t){this.type=r.Syntax.LabeledStatement,this.label=e,this.body=t}return e}();t.LabeledStatement=j;var z=function(){function e(e,t){this.type=r.Syntax.Literal,this.value=e,this.raw=t}return e}();t.Literal=z;var J=function(){function e(e,t){this.type=r.Syntax.MetaProperty,this.meta=e,this.property=t}return e}();t.MetaProperty=J;var X=function(){function e(e,t,n,i,o){this.type=r.Syntax.MethodDefinition,this.key=e,this.computed=t,this.value=n,this.kind=i,this.static=o}return e}();t.MethodDefinition=X;var Y=function(){function e(e,t){this.type=r.Syntax.NewExpression,this.callee=e,this.arguments=t}return e}();t.NewExpression=Y;var K=function(){function e(e){this.type=r.Syntax.ObjectExpression,this.properties=e}return e}();t.ObjectExpression=K;var W=function(){function e(e){this.type=r.Syntax.ObjectPattern,this.properties=e}return e}();t.ObjectPattern=W;var H=function(){function e(e,t){this.type=r.Syntax.Program,this.body=e,this.sourceType=t}return e}();t.Program=H;var q=function(){function e(e,t,n,i,o,s){this.type=r.Syntax.Property,this.key=t,this.computed=n,this.value=i,this.kind=e,this.method=o,this.shorthand=s}return e}();t.Property=q;var G=function(){function e(e,t,n){this.type=r.Syntax.Literal,this.value=e,this.raw=t,this.regex=n}return e}();t.RegexLiteral=G;var V=function(){function e(e){this.type=r.Syntax.RestElement,this.argument=e}return e}();t.RestElement=V;var $=function(){function e(e){this.type=r.Syntax.ReturnStatement,this.argument=e}return e}();t.ReturnStatement=$;var Z=function(){function e(e){this.type=r.Syntax.SequenceExpression,this.expressions=e}return e}();t.SequenceExpression=Z;var Q=function(){function e(e){this.type=r.Syntax.SpreadElement,this.argument=e}return e}();t.SpreadElement=Q;var ee=function(){function e(e,t){this.type=r.Syntax.MemberExpression,this.computed=!1,this.object=e,this.property=t}return e}();t.StaticMemberExpression=ee;var te=function(){function e(){this.type=r.Syntax.Super}return e}();t.Super=te;var ne=function(){function e(e,t){this.type=r.Syntax.SwitchCase,this.test=e,this.consequent=t}return e}();t.SwitchCase=ne;var re=function(){function e(e,t){this.type=r.Syntax.SwitchStatement,this.discriminant=e,this.cases=t}return e}();t.SwitchStatement=re;var ie=function(){function e(e,t){this.type=r.Syntax.TaggedTemplateExpression,this.tag=e,this.quasi=t}return e}();t.TaggedTemplateExpression=ie;var oe=function(){function e(e,t){this.type=r.Syntax.TemplateElement,this.value=e,this.tail=t}return e}();t.TemplateElement=oe;var se=function(){function e(e,t){this.type=r.Syntax.TemplateLiteral,this.quasis=e,this.expressions=t}return e}();t.TemplateLiteral=se;var ae=function(){function e(){this.type=r.Syntax.ThisExpression}return e}();t.ThisExpression=ae;var ue=function(){function e(e){this.type=r.Syntax.ThrowStatement,this.argument=e}return e}();t.ThrowStatement=ue;var ce=function(){function e(e,t,n){this.type=r.Syntax.TryStatement,this.block=e,this.handler=t,this.finalizer=n}return e}();t.TryStatement=ce;var le=function(){function e(e,t){this.type=r.Syntax.UnaryExpression,this.operator=e,this.argument=t,this.prefix=!0}return e}();t.UnaryExpression=le;var he=function(){function e(e,t,n){this.type=r.Syntax.UpdateExpression,this.operator=e,this.argument=t,this.prefix=n}return e}();t.UpdateExpression=he;var pe=function(){function e(e,t){this.type=r.Syntax.VariableDeclaration,this.declarations=e,this.kind=t}return e}();t.VariableDeclaration=pe;var fe=function(){function e(e,t){this.type=r.Syntax.VariableDeclarator,this.id=e,this.init=t}return e}();t.VariableDeclarator=fe;var de=function(){function e(e,t){this.type=r.Syntax.WhileStatement,this.test=e,this.body=t}return e}();t.WhileStatement=de;var me=function(){function e(e,t){this.type=r.Syntax.WithStatement,this.object=e,this.body=t}return e}();t.WithStatement=me;var ge=function(){function e(e,t){this.type=r.Syntax.YieldExpression,this.argument=e,this.delegate=t}return e}();t.YieldExpression=ge},function(e,t,n){"use strict";function r(e){var t;switch(e.type){case l.JSXSyntax.JSXIdentifier:var n=e;t=n.name;break;case l.JSXSyntax.JSXNamespacedName:var i=e;t=r(i.namespace)+":"+r(i.name);break;case l.JSXSyntax.JSXMemberExpression:var o=e;t=r(o.object)+"."+r(o.property)}return t}var i,o=this&&this.__extends||function(e,t){function n(){this.constructor=e}for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r]);e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},s=n(9),a=n(7),u=n(3),c=n(12),l=n(13),h=n(10),p=n(14);!function(e){e[e.Identifier=100]="Identifier",e[e.Text=101]="Text"}(i||(i={})),a.TokenName[i.Identifier]="JSXIdentifier",a.TokenName[i.Text]="JSXText";var f=function(e){function t(t,n,r){e.call(this,t,n,r)}return o(t,e),t.prototype.parsePrimaryExpression=function(){return this.match("<")?this.parseJSXRoot():e.prototype.parsePrimaryExpression.call(this)},t.prototype.startJSX=function(){this.scanner.index=this.startMarker.index,this.scanner.lineNumber=this.startMarker.lineNumber,this.scanner.lineStart=this.startMarker.lineStart},t.prototype.finishJSX=function(){this.nextToken()},t.prototype.reenterJSX=function(){this.startJSX(),this.expectJSX("}"),this.config.tokens&&this.tokens.pop()},t.prototype.createJSXNode=function(){return this.collectComments(),{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},t.prototype.createJSXChildNode=function(){return{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},t.prototype.scanXHTMLEntity=function(e){for(var t="&",n=!0,r=!1,i=!1,o=!1;!this.scanner.eof()&&n&&!r;){var a=this.scanner.source[this.scanner.index];if(a===e)break;if(r=";"===a,t+=a,++this.scanner.index,!r)switch(t.length){case 2:i="#"===a;break;case 3:i&&(o="x"===a,n=o||s.Character.isDecimalDigit(a.charCodeAt(0)),i=i&&!o);break;default:n=n&&!(i&&!s.Character.isDecimalDigit(a.charCodeAt(0))),n=n&&!(o&&!s.Character.isHexDigit(a.charCodeAt(0)))}}if(n&&r&&t.length>2){var u=t.substr(1,t.length-2);i&&u.length>1?t=String.fromCharCode(parseInt(u.substr(1),10)):o&&u.length>2?t=String.fromCharCode(parseInt("0"+u.substr(1),16)):i||o||!c.XHTMLEntities[u]||(t=c.XHTMLEntities[u])}return t},t.prototype.lexJSX=function(){var e=this.scanner.source.charCodeAt(this.scanner.index);if(60===e||62===e||47===e||58===e||61===e||123===e||125===e){var t=this.scanner.source[this.scanner.index++];return{type:a.Token.Punctuator,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index-1,end:this.scanner.index}}if(34===e||39===e){for(var n=this.scanner.index,r=this.scanner.source[this.scanner.index++],o="";!this.scanner.eof();){var u=this.scanner.source[this.scanner.index++];if(u===r)break;o+="&"===u?this.scanXHTMLEntity(r):u}return{type:a.Token.StringLiteral,value:o,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:n,end:this.scanner.index}}if(46===e){var c=this.scanner.source.charCodeAt(this.scanner.index+1),l=this.scanner.source.charCodeAt(this.scanner.index+2),t=46===c&&46===l?"...":".",n=this.scanner.index;return this.scanner.index+=t.length,{type:a.Token.Punctuator,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:n,end:this.scanner.index}}if(96===e)return{type:a.Token.Template,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index,end:this.scanner.index};if(s.Character.isIdentifierStart(e)&&92!==e){var n=this.scanner.index;for(++this.scanner.index;!this.scanner.eof();){var u=this.scanner.source.charCodeAt(this.scanner.index);if(s.Character.isIdentifierPart(u)&&92!==u)++this.scanner.index;else{if(45!==u)break;++this.scanner.index}}var h=this.scanner.source.slice(n,this.scanner.index);return{type:i.Identifier,value:h,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:n,end:this.scanner.index}}this.scanner.throwUnexpectedToken()},t.prototype.nextJSXToken=function(){this.collectComments(),this.startMarker.index=this.scanner.index,this.startMarker.lineNumber=this.scanner.lineNumber,this.startMarker.lineStart=this.scanner.lineStart;var e=this.lexJSX();return this.lastMarker.index=this.scanner.index,this.lastMarker.lineNumber=this.scanner.lineNumber,this.lastMarker.lineStart=this.scanner.lineStart,this.config.tokens&&this.tokens.push(this.convertToken(e)),e},t.prototype.nextJSXText=function(){this.startMarker.index=this.scanner.index,this.startMarker.lineNumber=this.scanner.lineNumber,this.startMarker.lineStart=this.scanner.lineStart;for(var e=this.scanner.index,t="";!this.scanner.eof();){var n=this.scanner.source[this.scanner.index];if("{"===n||"<"===n)break;++this.scanner.index,t+=n,s.Character.isLineTerminator(n.charCodeAt(0))&&(++this.scanner.lineNumber,"\r"===n&&"\n"===this.scanner.source[this.scanner.index]&&++this.scanner.index,this.scanner.lineStart=this.scanner.index)}this.lastMarker.index=this.scanner.index,this.lastMarker.lineNumber=this.scanner.lineNumber,this.lastMarker.lineStart=this.scanner.lineStart;var r={type:i.Text,value:t,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:e,end:this.scanner.index};return t.length>0&&this.config.tokens&&this.tokens.push(this.convertToken(r)),r},t.prototype.peekJSXToken=function(){var e=this.scanner.index,t=this.scanner.lineNumber,n=this.scanner.lineStart;this.scanner.scanComments();var r=this.lexJSX();return this.scanner.index=e,this.scanner.lineNumber=t,this.scanner.lineStart=n,r},t.prototype.expectJSX=function(e){var t=this.nextJSXToken();t.type===a.Token.Punctuator&&t.value===e||this.throwUnexpectedToken(t)},t.prototype.matchJSX=function(e){var t=this.peekJSXToken();return t.type===a.Token.Punctuator&&t.value===e},t.prototype.parseJSXIdentifier=function(){var e=this.createJSXNode(),t=this.nextJSXToken();return t.type!==i.Identifier&&this.throwUnexpectedToken(t),this.finalize(e,new p.JSXIdentifier(t.value))},t.prototype.parseJSXElementName=function(){var e=this.createJSXNode(),t=this.parseJSXIdentifier();if(this.matchJSX(":")){var n=t;this.expectJSX(":");var r=this.parseJSXIdentifier();t=this.finalize(e,new p.JSXNamespacedName(n,r))}else if(this.matchJSX("."))for(;this.matchJSX(".");){var i=t;this.expectJSX(".");var o=this.parseJSXIdentifier();t=this.finalize(e,new p.JSXMemberExpression(i,o))}return t},t.prototype.parseJSXAttributeName=function(){var e,t=this.createJSXNode(),n=this.parseJSXIdentifier();if(this.matchJSX(":")){var r=n;this.expectJSX(":");var i=this.parseJSXIdentifier();e=this.finalize(t,new p.JSXNamespacedName(r,i))}else e=n;return e},t.prototype.parseJSXStringLiteralAttribute=function(){var e=this.createJSXNode(),t=this.nextJSXToken();t.type!==a.Token.StringLiteral&&this.throwUnexpectedToken(t);var n=this.getTokenRaw(t);return this.finalize(e,new h.Literal(t.value,n))},t.prototype.parseJSXExpressionAttribute=function(){var e=this.createJSXNode();this.expectJSX("{"),this.finishJSX(),this.match("}")&&this.tolerateError("JSX attributes must only be assigned a non-empty expression");var t=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(e,new p.JSXExpressionContainer(t))},t.prototype.parseJSXAttributeValue=function(){return this.matchJSX("{")?this.parseJSXExpressionAttribute():this.matchJSX("<")?this.parseJSXElement():this.parseJSXStringLiteralAttribute()},t.prototype.parseJSXNameValueAttribute=function(){var e=this.createJSXNode(),t=this.parseJSXAttributeName(),n=null;return this.matchJSX("=")&&(this.expectJSX("="),n=this.parseJSXAttributeValue()),this.finalize(e,new p.JSXAttribute(t,n))},t.prototype.parseJSXSpreadAttribute=function(){var e=this.createJSXNode();this.expectJSX("{"),this.expectJSX("..."),this.finishJSX();var t=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(e,new p.JSXSpreadAttribute(t))},t.prototype.parseJSXAttributes=function(){for(var e=[];!this.matchJSX("/")&&!this.matchJSX(">");){var t=this.matchJSX("{")?this.parseJSXSpreadAttribute():this.parseJSXNameValueAttribute();e.push(t)}return e},t.prototype.parseJSXOpeningElement=function(){var e=this.createJSXNode();this.expectJSX("<");var t=this.parseJSXElementName(),n=this.parseJSXAttributes(),r=this.matchJSX("/");return r&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(e,new p.JSXOpeningElement(t,r,n))},t.prototype.parseJSXBoundaryElement=function(){var e=this.createJSXNode();if(this.expectJSX("<"),this.matchJSX("/")){this.expectJSX("/");var t=this.parseJSXElementName();return this.expectJSX(">"),this.finalize(e,new p.JSXClosingElement(t))}var n=this.parseJSXElementName(),r=this.parseJSXAttributes(),i=this.matchJSX("/");return i&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(e,new p.JSXOpeningElement(n,i,r))},t.prototype.parseJSXEmptyExpression=function(){var e=this.createJSXChildNode();return this.collectComments(),this.lastMarker.index=this.scanner.index,this.lastMarker.lineNumber=this.scanner.lineNumber,this.lastMarker.lineStart=this.scanner.lineStart,this.finalize(e,new p.JSXEmptyExpression)},t.prototype.parseJSXExpressionContainer=function(){var e=this.createJSXNode();this.expectJSX("{");var t;return this.matchJSX("}")?(t=this.parseJSXEmptyExpression(),this.expectJSX("}")):(this.finishJSX(),t=this.parseAssignmentExpression(),this.reenterJSX()),this.finalize(e,new p.JSXExpressionContainer(t))},t.prototype.parseJSXChildren=function(){for(var e=[];!this.scanner.eof();){var t=this.createJSXChildNode(),n=this.nextJSXText();if(n.start<n.end){var r=this.getTokenRaw(n),i=this.finalize(t,new p.JSXText(n.value,r));e.push(i)}if("{"!==this.scanner.source[this.scanner.index])break;var o=this.parseJSXExpressionContainer();e.push(o)}return e},t.prototype.parseComplexJSXElement=function(e){for(var t=[];!this.scanner.eof();){e.children=e.children.concat(this.parseJSXChildren());var n=this.createJSXChildNode(),i=this.parseJSXBoundaryElement();if(i.type===l.JSXSyntax.JSXOpeningElement){var o=i;if(o.selfClosing){var s=this.finalize(n,new p.JSXElement(o,[],null));e.children.push(s)}else t.push(e),e={node:n,opening:o,closing:null,children:[]}}if(i.type===l.JSXSyntax.JSXClosingElement){e.closing=i;var a=r(e.opening.name),u=r(e.closing.name);if(a!==u&&this.tolerateError("Expected corresponding JSX closing tag for %0",a),!(t.length>0))break;var s=this.finalize(e.node,new p.JSXElement(e.opening,e.children,e.closing));e=t.pop(),e.children.push(s)}}return e},t.prototype.parseJSXElement=function(){var e=this.createJSXNode(),t=this.parseJSXOpeningElement(),n=[],r=null;if(!t.selfClosing){var i=this.parseComplexJSXElement({node:e,opening:t,closing:r,children:n});n=i.children,r=i.closing}return this.finalize(e,new p.JSXElement(t,n,r))},t.prototype.parseJSXRoot=function(){this.config.tokens&&this.tokens.pop(),this.startJSX();var e=this.parseJSXElement();return this.finishJSX(),e},t}(u.Parser);t.JSXParser=f},function(e,t){"use strict";t.XHTMLEntities={quot:'"',amp:"&",apos:"'",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",lang:"⟨",rang:"⟩"}},function(e,t){"use strict";t.JSXSyntax={JSXAttribute:"JSXAttribute",JSXClosingElement:"JSXClosingElement",JSXElement:"JSXElement",JSXEmptyExpression:"JSXEmptyExpression",JSXExpressionContainer:"JSXExpressionContainer",JSXIdentifier:"JSXIdentifier",JSXMemberExpression:"JSXMemberExpression",JSXNamespacedName:"JSXNamespacedName",JSXOpeningElement:"JSXOpeningElement",JSXSpreadAttribute:"JSXSpreadAttribute",JSXText:"JSXText"}},function(e,t,n){"use strict";var r=n(13),i=function(){function e(e){this.type=r.JSXSyntax.JSXClosingElement,this.name=e}return e}();t.JSXClosingElement=i;var o=function(){function e(e,t,n){this.type=r.JSXSyntax.JSXElement,this.openingElement=e,this.children=t,this.closingElement=n}return e}();t.JSXElement=o;var s=function(){function e(){this.type=r.JSXSyntax.JSXEmptyExpression}return e}();t.JSXEmptyExpression=s;var a=function(){function e(e){this.type=r.JSXSyntax.JSXExpressionContainer,this.expression=e}return e}();t.JSXExpressionContainer=a;var u=function(){function e(e){this.type=r.JSXSyntax.JSXIdentifier,this.name=e}return e}();t.JSXIdentifier=u;var c=function(){function e(e,t){this.type=r.JSXSyntax.JSXMemberExpression,this.object=e,this.property=t}return e}();t.JSXMemberExpression=c;var l=function(){function e(e,t){this.type=r.JSXSyntax.JSXAttribute,this.name=e,this.value=t}return e}();t.JSXAttribute=l;var h=function(){function e(e,t){this.type=r.JSXSyntax.JSXNamespacedName,this.namespace=e,this.name=t}return e}();t.JSXNamespacedName=h;var p=function(){function e(e,t,n){this.type=r.JSXSyntax.JSXOpeningElement,this.name=e,this.selfClosing=t,this.attributes=n}return e}();t.JSXOpeningElement=p;var f=function(){function e(e){this.type=r.JSXSyntax.JSXSpreadAttribute,this.argument=e}return e}();t.JSXSpreadAttribute=f;var d=function(){function e(e,t){this.type=r.JSXSyntax.JSXText,this.value=e,this.raw=t}return e}();t.JSXText=d},function(e,t,n){"use strict";var r=n(8),i=n(6),o=n(7),s=function(){function e(){this.values=[],this.curly=this.paren=-1}return e.prototype.beforeFunctionExpression=function(e){return["(","{","[","in","typeof","instanceof","new","return","case","delete","throw","void","=","+=","-=","*=","**=","/=","%=","<<=",">>=",">>>=","&=","|=","^=",",","+","-","*","**","/","%","++","--","<<",">>",">>>","&","|","^","!","~","&&","||","?",":","===","==",">=","<=","<",">","!=","!=="].indexOf(e)>=0},e.prototype.isRegexStart=function(){var e=this.values[this.values.length-1],t=null!==e;switch(e){case"this":case"]":t=!1;break;case")":var n=this.values[this.paren-1];t="if"===n||"while"===n||"for"===n||"with"===n;break;case"}":if(t=!1,"function"===this.values[this.curly-3]){var r=this.values[this.curly-4];t=!!r&&!this.beforeFunctionExpression(r)}else if("function"===this.values[this.curly-4]){var i=this.values[this.curly-5];t=!i||!this.beforeFunctionExpression(i)}}return t},e.prototype.push=function(e){e.type===o.Token.Punctuator||e.type===o.Token.Keyword?("{"===e.value?this.curly=this.values.length:"("===e.value&&(this.paren=this.values.length),this.values.push(e.value)):this.values.push(null)},e}(),a=function(){function e(e,t){this.errorHandler=new i.ErrorHandler,this.errorHandler.tolerant=!!t&&("boolean"==typeof t.tolerant&&t.tolerant),this.scanner=new r.Scanner(e,this.errorHandler),this.scanner.trackComment=!!t&&("boolean"==typeof t.comment&&t.comment),this.trackRange=!!t&&("boolean"==typeof t.range&&t.range),this.trackLoc=!!t&&("boolean"==typeof t.loc&&t.loc),this.buffer=[],this.reader=new s}return e.prototype.errors=function(){return this.errorHandler.errors},e.prototype.getNextToken=function(){if(0===this.buffer.length){var e=this.scanner.scanComments();if(this.scanner.trackComment)for(var t=0;t<e.length;++t){var n=e[t],r=void 0,i=this.scanner.source.slice(n.slice[0],n.slice[1]);r={type:n.multiLine?"BlockComment":"LineComment",value:i},this.trackRange&&(r.range=n.range),this.trackLoc&&(r.loc=n.loc),this.buffer.push(r)}if(!this.scanner.eof()){var s=void 0;this.trackLoc&&(s={start:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},end:{}});var a=void 0;a="/"===this.scanner.source[this.scanner.index]?this.reader.isRegexStart()?this.scanner.scanRegExp():this.scanner.scanPunctuator():this.scanner.lex(),this.reader.push(a);var u=void 0;u={type:o.TokenName[a.type],value:this.scanner.source.slice(a.start,a.end)},this.trackRange&&(u.range=[a.start,a.end]),this.trackLoc&&(s.end={line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},u.loc=s),a.regex&&(u.regex=a.regex),this.buffer.push(u)}}return this.buffer.shift()},e}();t.Tokenizer=a}])})},function(e,t,n){"use strict";function r(e,t){var n,r,i,o,s,a,u;if(null===t)return{};for(n={},r=Object.keys(t),i=0,o=r.length;i<o;i+=1)s=r[i],a=String(t[s]),"!!"===s.slice(0,2)&&(s="tag:yaml.org,2002:"+s.slice(2)),u=e.compiledTypeMap.fallback[s],u&&O.call(u.styleAliases,a)&&(a=u.styleAliases[a]),n[s]=a;return n}function i(e){var t,n,r;if(t=e.toString(16).toUpperCase(),e<=255)n="x",r=2;else if(e<=65535)n="u",r=4;else{if(!(e<=4294967295))throw new N("code point within a string may not be greater than 0xFFFFFFFF");n="U",r=8}return"\\"+n+B.repeat("0",r-t.length)+t}function o(e){this.schema=e.schema||I,this.indent=Math.max(1,e.indent||2),this.skipInvalid=e.skipInvalid||!1,this.flowLevel=B.isNothing(e.flowLevel)?-1:e.flowLevel,this.styleMap=r(this.schema,e.styles||null),this.sortKeys=e.sortKeys||!1,this.lineWidth=e.lineWidth||80,this.noRefs=e.noRefs||!1,this.noCompatMode=e.noCompatMode||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function s(e,t){for(var n,r=B.repeat(" ",t),i=0,o=-1,s="",a=e.length;i<a;)o=e.indexOf("\n",i),o===-1?(n=e.slice(i),i=a):(n=e.slice(i,o+1),i=o+1),n.length&&"\n"!==n&&(s+=r),s+=n;return s}function a(e,t){return"\n"+B.repeat(" ",e.indent*t)}function u(e,t){var n,r,i;for(n=0,r=e.implicitTypes.length;n<r;n+=1)if(i=e.implicitTypes[n],i.resolve(t))return!0;return!1}function c(e){return e===R||e===_}function l(e){return 32<=e&&e<=126||161<=e&&e<=55295&&8232!==e&&8233!==e||57344<=e&&e<=65533&&65279!==e||65536<=e&&e<=1114111}function h(e){return l(e)&&65279!==e&&e!==W&&e!==Z&&e!==Q&&e!==te&&e!==re&&e!==q&&e!==z}function p(e){return l(e)&&65279!==e&&!c(e)&&e!==H&&e!==V&&e!==q&&e!==W&&e!==Z&&e!==Q&&e!==te&&e!==re&&e!==z&&e!==X&&e!==K&&e!==U&&e!==ne&&e!==G&&e!==Y&&e!==j&&e!==J&&e!==$&&e!==ee}function f(e,t,n,r,i){var o,s,a=!1,u=!1,f=r!==-1,d=-1,m=p(e.charCodeAt(0))&&!c(e.charCodeAt(e.length-1));if(t)for(o=0;o<e.length;o++){if(s=e.charCodeAt(o),!l(s))return le;m=m&&h(s)}else{for(o=0;o<e.length;o++){if(s=e.charCodeAt(o),s===L)a=!0,f&&(u=u||o-d-1>r&&" "!==e[d+1],d=o);else if(!l(s))return le;m=m&&h(s)}u=u||f&&o-d-1>r&&" "!==e[d+1]}return a||u?" "===e[0]&&n>9?le:u?ce:ue:m&&!i(e)?se:ae}function d(e,t,n,r){e.dump=function(){function i(t){return u(e,t)}if(0===t.length)return"''";if(!e.noCompatMode&&oe.indexOf(t)!==-1)return"'"+t+"'";var o=e.indent*Math.max(1,n),a=e.lineWidth===-1?-1:Math.max(Math.min(e.lineWidth,40),e.lineWidth-o),c=r||e.flowLevel>-1&&n>=e.flowLevel;switch(f(t,c,e.indent,a,i)){case se:return t;case ae:return"'"+t.replace(/'/g,"''")+"'";case ue:return"|"+m(t,e.indent)+g(s(t,o));case ce:return">"+m(t,e.indent)+g(s(x(t,a),o));case le:return'"'+v(t,a)+'"';default:throw new N("impossible error: invalid scalar style")}}()}function m(e,t){var n=" "===e[0]?String(t):"",r="\n"===e[e.length-1],i=r&&("\n"===e[e.length-2]||"\n"===e),o=i?"+":r?"":"-";return n+o+"\n"}function g(e){return"\n"===e[e.length-1]?e.slice(0,-1):e}function x(e,t){for(var n,r,i=/(\n+)([^\n]*)/g,o=function(){var n=e.indexOf("\n");return n=n!==-1?n:e.length,i.lastIndex=n,y(e.slice(0,n),t)}(),s="\n"===e[0]||" "===e[0];r=i.exec(e);){var a=r[1],u=r[2];n=" "===u[0],o+=a+(s||n||""===u?"":"\n")+y(u,t),s=n}return o}function y(e,t){if(""===e||" "===e[0])return e;for(var n,r,i=/ [^ ]/g,o=0,s=0,a=0,u="";n=i.exec(e);)a=n.index,a-o>t&&(r=s>o?s:a,u+="\n"+e.slice(o,r),o=r+1),s=a;return u+="\n",u+=e.length-o>t&&s>o?e.slice(o,s)+"\n"+e.slice(s+1):e.slice(o),u.slice(1)}function v(e){for(var t,n,r="",o=0;o<e.length;o++)t=e.charCodeAt(o),n=ie[t],r+=!n&&l(t)?e[o]:n||i(t);return r}function b(e,t,n){var r,i,o="",s=e.tag;for(r=0,i=n.length;r<i;r+=1)A(e,t,n[r],!1,!1)&&(0!==r&&(o+=", "),o+=e.dump);e.tag=s,e.dump="["+o+"]"}function D(e,t,n,r){var i,o,s="",u=e.tag;for(i=0,o=n.length;i<o;i+=1)A(e,t+1,n[i],!0,!0)&&(r&&0===i||(s+=a(e,t)),s+=e.dump&&L===e.dump.charCodeAt(0)?"-":"- ",s+=e.dump);e.tag=u,e.dump=s||"[]"}function w(e,t,n){var r,i,o,s,a,u="",c=e.tag,l=Object.keys(n);for(r=0,i=l.length;r<i;r+=1)a="",0!==r&&(a+=", "),o=l[r],s=n[o],A(e,t,o,!1,!1)&&(e.dump.length>1024&&(a+="? "),a+=e.dump+": ",A(e,t,s,!1,!1)&&(a+=e.dump,u+=a));e.tag=c,e.dump="{"+u+"}"}function E(e,t,n,r){var i,o,s,u,c,l,h="",p=e.tag,f=Object.keys(n);if(e.sortKeys===!0)f.sort();else if("function"==typeof e.sortKeys)f.sort(e.sortKeys);else if(e.sortKeys)throw new N("sortKeys must be a boolean or a function");
for(i=0,o=f.length;i<o;i+=1)l="",r&&0===i||(l+=a(e,t)),s=f[i],u=n[s],A(e,t+1,s,!0,!0,!0)&&(c=null!==e.tag&&"?"!==e.tag||e.dump&&e.dump.length>1024,c&&(l+=e.dump&&L===e.dump.charCodeAt(0)?"?":"? "),l+=e.dump,c&&(l+=a(e,t)),A(e,t+1,u,!0,c)&&(l+=e.dump&&L===e.dump.charCodeAt(0)?":":": ",l+=e.dump,h+=l));e.tag=p,e.dump=h||"{}"}function k(e,t,n){var r,i,o,s,a,u;for(i=n?e.explicitTypes:e.implicitTypes,o=0,s=i.length;o<s;o+=1)if(a=i[o],(a.instanceOf||a.predicate)&&(!a.instanceOf||"object"==typeof t&&t instanceof a.instanceOf)&&(!a.predicate||a.predicate(t))){if(e.tag=n?a.tag:"?",a.represent){if(u=e.styleMap[a.tag]||a.defaultStyle,"[object Function]"===M.call(a.represent))r=a.represent(t,u);else{if(!O.call(a.represent,u))throw new N("!<"+a.tag+'> tag resolver accepts not "'+u+'" style');r=a.represent[u](t,u)}e.dump=r}return!0}return!1}function A(e,t,n,r,i,o){e.tag=null,e.dump=n,k(e,n,!1)||k(e,n,!0);var s=M.call(e.dump);r&&(r=e.flowLevel<0||e.flowLevel>t);var a,u,c="[object Object]"===s||"[object Array]"===s;if(c&&(a=e.duplicates.indexOf(n),u=a!==-1),(null!==e.tag&&"?"!==e.tag||u||2!==e.indent&&t>0)&&(i=!1),u&&e.usedDuplicates[a])e.dump="*ref_"+a;else{if(c&&u&&!e.usedDuplicates[a]&&(e.usedDuplicates[a]=!0),"[object Object]"===s)r&&0!==Object.keys(e.dump).length?(E(e,t,e.dump,i),u&&(e.dump="&ref_"+a+e.dump)):(w(e,t,e.dump),u&&(e.dump="&ref_"+a+" "+e.dump));else if("[object Array]"===s)r&&0!==e.dump.length?(D(e,t,e.dump,i),u&&(e.dump="&ref_"+a+e.dump)):(b(e,t,e.dump),u&&(e.dump="&ref_"+a+" "+e.dump));else{if("[object String]"!==s){if(e.skipInvalid)return!1;throw new N("unacceptable kind of an object to dump "+s)}"?"!==e.tag&&d(e,e.dump,t,o)}null!==e.tag&&"?"!==e.tag&&(e.dump="!<"+e.tag+"> "+e.dump)}return!0}function C(e,t){var n,r,i=[],o=[];for(S(e,i,o),n=0,r=o.length;n<r;n+=1)t.duplicates.push(i[o[n]]);t.usedDuplicates=new Array(r)}function S(e,t,n){var r,i,o;if(null!==e&&"object"==typeof e)if(i=t.indexOf(e),i!==-1)n.indexOf(i)===-1&&n.push(i);else if(t.push(e),Array.isArray(e))for(i=0,o=e.length;i<o;i+=1)S(e[i],t,n);else for(r=Object.keys(e),i=0,o=r.length;i<o;i+=1)S(e[r[i]],t,n)}function F(e,t){t=t||{};var n=new o(t);return n.noRefs||C(e,n),A(n,0,e,!0,!0)?n.dump+"\n":""}function T(e,t){return F(e,B.extend({schema:P},t))}var B=n(185),N=n(186),I=n(211),P=n(188),M=Object.prototype.toString,O=Object.prototype.hasOwnProperty,_=9,L=10,R=32,U=33,j=34,z=35,J=37,X=38,Y=39,K=42,W=44,H=45,q=58,G=62,V=63,$=64,Z=91,Q=93,ee=96,te=123,ne=124,re=125,ie={};ie[0]="\\0",ie[7]="\\a",ie[8]="\\b",ie[9]="\\t",ie[10]="\\n",ie[11]="\\v",ie[12]="\\f",ie[13]="\\r",ie[27]="\\e",ie[34]='\\"',ie[92]="\\\\",ie[133]="\\N",ie[160]="\\_",ie[8232]="\\L",ie[8233]="\\P";var oe=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],se=1,ae=2,ue=3,ce=4,le=5;e.exports.dump=F,e.exports.safeDump=T},function(e,t){e.exports='---\nurl: "http://petstore.swagger.io/v2/swagger.json"\ndom_id: "#swagger-ui"\nvalidatorUrl: "https://online.swagger.io/validator"\noauth2RedirectUrl: "http://localhost:3200/oauth2-redirect.html"\n'}]))});
//# sourceMappingURL=swagger-ui-standalone-preset.js.map