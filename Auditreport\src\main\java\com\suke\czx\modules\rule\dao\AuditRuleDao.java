package com.suke.czx.modules.rule.dao;

import com.suke.czx.modules.rule.entity.AuditRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface AuditRuleDao extends BaseDao<AuditRule> {

    /**
     * 将规则文件信息存入父表中
     * @param auditRule
     * @return
     */
    Integer saveAuditRule(AuditRule auditRule);

    /**
     * 查询全部规则父表
     * @return
     */
    List<AuditRule> queryAllRule(@Param("id") String id);

    /**
     * 根据id更新规则名称
     * @param id
     * @param name
     * @return
     */
    Integer updateRuleById(@Param("id") String id, @Param("name") String name,@Param("updateUserId") String updateUserId, @Param("updateTime") LocalDateTime updateTime);


    /**
     * 根据id更新规则名称
     * @param auditRule
     * @return
     */
    void add(AuditRule auditRule);

    /**
     * 根据name查询
     * @param name
     * @return
     */
    List<AuditRule> queryAuditRule(@Param("name") String name);

    /**
     * 根据name查询
     * @param url_id
     * @return
     */
    Map<String, Object> queryAuditRuleUrl(@Param("url_id") Integer url_id);


    List<AuditRule> queryAuditRuleWithParam(String keyword);
}
