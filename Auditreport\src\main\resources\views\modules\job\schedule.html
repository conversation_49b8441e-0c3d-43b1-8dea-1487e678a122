<!DOCTYPE html>
<html>
<head>
<title>定时任务</title>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
<link rel="stylesheet" href="../../css/bootstrap.min.css">
<link rel="stylesheet" href="../../css/font-awesome.min.css">
<link rel="stylesheet" href="../../plugins/jqgrid/ui.jqgrid-bootstrap.css">
<link rel="stylesheet" href="../../plugins/ztree/css/metroStyle/metroStyle.css">
<link rel="stylesheet" href="../../css/main.css">
<script src="../../libs/jquery.min.js"></script>
<script src="../../plugins/layer/layer.js"></script>
<script src="../../libs/bootstrap.min.js"></script>
<script src="../../libs/vue.min.js"></script>
<script src="../../plugins/jqgrid/grid.locale-cn.js"></script>
<script src="../../plugins/jqgrid/jquery.jqGrid.min.js"></script>
<script src="../../plugins/ztree/jquery.ztree.all.min.js"></script>
<script src="../../js/common.js"></script>
</head>
<body>
<div id="rrapp" v-cloak>
	<div v-show="showList">
		<div class="grid-btn" style="height:34px;">
			<div class="form-group col-sm-2">
				<input type="text" class="form-control" v-model="q.beanName" @keyup.enter="query" placeholder="bean名称">
			</div>
			<a class="btn btn-default" @click="query">查询</a>
			<a v-if="hasPermission('sys:schedule:save')" class="btn btn-primary" @click="add"><i class="fa fa-plus"></i>&nbsp;新增</a>
			<a v-if="hasPermission('sys:schedule:update')" class="btn btn-primary" @click="update"><i class="fa fa-pencil-square-o"></i>&nbsp;修改</a>
			<a v-if="hasPermission('sys:schedule:delete')" class="btn btn-primary" @click="del"><i class="fa fa-trash-o"></i>&nbsp;删除</a>
			<a v-if="hasPermission('sys:schedule:pause')" class="btn btn-primary" @click="pause"><i class="fa fa-pause"></i>&nbsp;暂停</a>
			<a v-if="hasPermission('sys:schedule:resume')" class="btn btn-primary" @click="resume"><i class="fa fa-play"></i>&nbsp;恢复</a>
			<a v-if="hasPermission('sys:schedule:run')" class="btn btn-primary" @click="runOnce"><i class="fa fa-arrow-circle-right"></i>&nbsp;立即执行</a>
			<a v-if="hasPermission('sys:schedule:log')" class="btn btn-danger" style="float:right;" href="schedule_log.html">日志列表</a>
		</div>
	    <table id="jqGrid"></table>
	    <div id="jqGridPager"></div>
	</div>
	
	<div v-show="!showList" class="panel panel-default">
		<div class="panel-heading">{{title}}</div>
		<form class="form-horizontal">
			<div class="form-group">
			   	<div class="col-sm-2 control-label">bean名称</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="schedule.beanName" placeholder="spring bean名称，如：testTask"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">方法名称</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="schedule.methodName" placeholder="方法名称"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">参数</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="schedule.params" placeholder="参数"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">cron表达式</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="schedule.cronExpression" placeholder="如：0 0 12 * * ?"/>
			    </div>
			</div>
			<div class="form-group">
			   	<div class="col-sm-2 control-label">备注</div>
			   	<div class="col-sm-10">
			      <input type="text" class="form-control" v-model="schedule.remark" placeholder="备注"/>
			    </div>
			</div>
			<div class="form-group">
				<div class="col-sm-2 control-label"></div> 
				<input type="button" class="btn btn-primary" @click="saveOrUpdate" value="确定"/>
				&nbsp;&nbsp;<input type="button" class="btn btn-warning" @click="reload" value="返回"/>
			</div>
		</form>
	</div>
</div>

<script src="../../js/modules/job/schedule.js"></script>
</body>
</html>