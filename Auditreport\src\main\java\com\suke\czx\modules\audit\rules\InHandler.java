package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetInDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.List;
import java.util.Map;

public class InHandler extends ExcelHandleRuler {
    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetInDto condition = (SheetInDto) param;
        boolean res = true;
        Sheet sheet = condition.getSheet();
        int inColumn = condition.getColumn();
        List<String> matchList = condition.getMatchList();
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);
            Cell oneRowTargetCell = oneRow.getCell(inColumn);
            setCellStr(oneRowTargetCell);
            String cellStrVal = oneRowTargetCell.getStringCellValue();
            if (!matchList.contains(cellStrVal)) {
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }
        }
        return res ? success() : fail();
    }
}
