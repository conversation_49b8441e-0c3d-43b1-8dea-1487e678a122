<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.DatasourceDefMapper">

    <insert id="addDatasourceInfo" parameterType="com.suke.czx.newland.dto.DatasourceInfoDto">
        insert into DATASOURCE_DEF (DATA_SOURCE_ID, DATA_SOURCE_NAME, DATA_SOURCE_TYPE, url, username, password, create_time, status)
        values (database_def_seqId.nextval,
                #{datasourceName},
                #{datasourceType},
                #{url},
                #{datasourceUsername},
                #{datasourcePassword},
                sysdate,
                #{status})
    </insert>
    <update id="updateDatasourceInfoByDatasourceId">
        update DATASOURCE_DEF set DATA_SOURCE_NAME = #{datasourceName},
            DATA_SOURCE_TYPE = #{datasourceType},
            URL = #{url},
            USERNAME = #{datasourceUsername},
            PASSWORD = #{datasourcePassword},
            STATUS = #{status}
        where DATA_SOURCE_ID = #{datasourceId}
    </update>

    <select id="qryAllDatasourceWithActivated" resultType="com.suke.czx.newland.dto.DatasourceInfoDto">
        select DATA_SOURCE_ID,
            DATA_SOURCE_NAME,
            DATA_SOURCE_TYPE datasourceType,
            URL,
            USERNAME datasourceUsername,
            PASSWORD datasourcePassword,
            CREATE_TIME,
            STATUS
        from DATASOURCE_DEF where STATUS = '0'
    </select>
    <select id="qryDatasourceWithCondition" resultType="com.suke.czx.newland.dto.DatasourceInfoDto">
        select DATA_SOURCE_ID,
               DATA_SOURCE_NAME,
               DATA_SOURCE_TYPE,
               URL,
               USERNAME datasourceUsername,
               PASSWORD datasourcePassword,
               CREATE_TIME,
               STATUS
        from DATASOURCE_DEF
        where 1 = 1
        <if test="datasourceName != null and datasourceName != ''">
            and DATA_SOURCE_NAME like '%${datasourceName}%'
        </if>
        <if test="datasourceType != null and datasourceType != ''">
            and DATA_SOURCE_TYPE = '${datasourceType}'
        </if>
            and STATUS = ${status}
        <if test="datasourceUsername != null and datasourceUsername != ''">
            and USERNAME like '%${datasourceUsername}%'
        </if>
        order by CREATE_TIME desc
    </select>
    <select id="qryDatasourceInfoByDatasourceId" resultType="com.suke.czx.newland.dto.DatasourceInfoDto">
        select DATA_SOURCE_ID,
               DATA_SOURCE_NAME,
               DATA_SOURCE_TYPE,
               URL,
               USERNAME datasourceUsername,
               PASSWORD datasourcePassword,
               CREATE_TIME,
               STATUS
        from DATASOURCE_DEF
        where 1 = 1
        and DATA_SOURCE_ID = #{datasourceId}
    </select>


</mapper>