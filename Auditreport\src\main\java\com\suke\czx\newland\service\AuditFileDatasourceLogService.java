package com.suke.czx.newland.service;

import com.github.pagehelper.PageInfo;
import com.suke.czx.newland.dto.AuditFileDatasourceLogQryDto;
import com.suke.czx.newland.dto.AuditFileDatasourceLogResDto;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;

import java.util.List;

public interface AuditFileDatasourceLogService {
    void addBatchAuditFileDatasourceLog(List<AuditFileDatasourceLogPo> records);

    PageInfo<AuditFileDatasourceLogResDto> qryAuditDatasourceLogWithPageInfo(AuditFileDatasourceLogQryDto condition);


    void delBatchAuditFileDatasourceLog(List<String> ids);

}
