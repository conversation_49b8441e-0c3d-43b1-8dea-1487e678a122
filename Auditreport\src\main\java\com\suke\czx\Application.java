package com.suke.czx;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import javax.annotation.PostConstruct;

@Slf4j
//开启异步
@EnableAsync
@EnableSwagger2
@EnableTransactionManagement
@SpringBootApplication
public class Application extends SpringBootServletInitializer {

	@Value("${custom.property}")
	private String customProperty;

	public static void main(String[] args) {
		SpringApplication.run(Application.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(Application.class);
	}

	@PostConstruct
	public void init() {
		log.info("=======================additional config load successfully: {}", customProperty);
	}
}
