package com.suke.czx.modules.audit.service;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.audit.entity.AuditComparison;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AuditComparisonService {

    /**
     * 比对报表
     * @param fileName
     * @param userId
     * @return
     */
    R comparison(String fileName, String userId, String auditName, HttpServletRequest request, HttpServletResponse response);

    /**
     * 获取全部的比对信息
     * @return
     */
    List<AuditComparison> queryComparisonList(String name,Integer status,String userId);

    /**
     * 批量导出比对结果
     * @param ids
     */
    R batchExport(Long[] ids);


    /** 删除对比报表
     *
     * @param id
     */
    int deleteBatch(Long id) throws Exception;

    /**
     * 批量删除
     * @param ids
     */
    int delComparisonBatch(Long[] ids) throws Exception;
}
