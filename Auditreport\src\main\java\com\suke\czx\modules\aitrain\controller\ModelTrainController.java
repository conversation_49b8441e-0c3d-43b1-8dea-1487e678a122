package com.suke.czx.modules.aitrain.controller;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.aitrain.entity.ModelTrain;
import com.suke.czx.modules.aitrain.service.ModelTrainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/modelTrain")
public class ModelTrainController {

    @Resource
    private ModelTrainService modelTrainService;

    @GetMapping("getTrainName")
    public R getTrainName() {
        List<String> list = modelTrainService.queryAllTrainName();
        return R.ok(list);
    }

    @GetMapping("getModelTrainInfo")
    public R getModelTrainInfo(@RequestParam("trainName") String trainName)  {
        List<ModelTrain> modelTrainList = modelTrainService.queryByTrainName(trainName);
        return R.ok(modelTrainList);
    }

    @GetMapping("getModelSum")
    public R getModelSum()  {
        return R.ok(modelTrainService.getModelSum());
    }

    /**
     * 获取模型名称和得分，用于echarts图表
     * @param trainName
     * @return
     */
    @GetMapping("getModelAndScoreTest")
    public R getModelAndScoreTest(@RequestParam("trainName") String trainName)  {
        return R.ok(modelTrainService.getModelAndScoreTest(trainName));
    }
}
