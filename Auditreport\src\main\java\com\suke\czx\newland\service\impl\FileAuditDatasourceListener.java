package com.suke.czx.newland.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.Utils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSONObject;
import com.suke.czx.modules.sys.entity.SysUserEntity;
import com.suke.czx.newland.consts.AuditFileDatasourceStatus;
import com.suke.czx.newland.consts.UploadFileType;
import com.suke.czx.newland.dto.AuditFileDatasourceCompareDto;
import com.suke.czx.newland.dto.ExcelColorDto;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;
import com.suke.czx.newland.po.AuditFileDatasourceRulePo;
import com.suke.czx.newland.po.AuditUploadFilePo;
import com.suke.czx.newland.service.AuditFileDatasourceLogService;
import com.suke.czx.newland.service.FileService;
import com.suke.czx.newland.util.DataSourceUtil;
import com.suke.czx.newland.util.FileUtil;
import com.suke.czx.newland.util.SpringConfigTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.disk.DiskFileItem;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.shiro.SecurityUtils;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class FileAuditDatasourceListener implements ReadListener<Map<Integer, String>> {

    private final AuditFileDatasourceRulePo ruleInfo;
    private final DataSourceUtil dataSourceUtil;
    private final FileService fileService;
    private final AuditUploadFilePo fileInfo;
    private final AuditFileDatasourceLogService logService;
    private AuditFileDatasourceStatus auditStatus;
    private final long beginTime;
    private long endTime;
    private String generateUpFileId;


    public FileAuditDatasourceListener(AuditFileDatasourceRulePo ruleInfo, AuditUploadFilePo fileInfo) {
        this.beginTime = System.currentTimeMillis();
        this.ruleInfo = ruleInfo;
        this.fileInfo = fileInfo;
        this.dataSourceUtil = ((DataSourceUtil) SpringConfigTool.getBean(DataSourceUtil.class));
        this.fileService = ((FileService) SpringConfigTool.getBean(FileService.class));
        this.logService = ((AuditFileDatasourceLogService) SpringConfigTool.getBean(AuditFileDatasourceLogService.class));
    }

    List<AuditFileDatasourceCompareDto> datas = new ArrayList<>();
    Map<String, Integer> headIndexMap = new HashMap<>();
    Map<String, Integer> headFieldMap = new LinkedHashMap<>();

    @Override
    public void invoke(Map<Integer, String> data, AnalysisContext analysisContext) {
        AuditFileDatasourceCompareDto record = new AuditFileDatasourceCompareDto();
        record.setRow(analysisContext.getCurrentRowNum());
        record.setRecord(data);
        datas.add(record);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        try {
            List<JSONObject> qryRes = dataSourceUtil.executeSqlGetAll(ruleInfo.getRunDatasourceId(), ruleInfo.getAuditSqlClob(), true);
            log.info("qryRes:{}", JSONUtil.toJsonStr(qryRes));
            log.info("datas:{}", JSONUtil.toJsonStr(datas));
            //文件 cell 为空时 需要添加一个空元素
            List<AuditFileDatasourceCompareDto> handleEmptyList = datas.stream().filter(e -> e.getRecord().size() < headFieldMap.size()).peek(e -> {
                Map<Integer, String> record = e.getRecord();
                for (int i = 0; i < headFieldMap.size(); i++) {
                    if (!record.containsKey(i)) {
                        record.put(i, "");
                    }
                }
            }).collect(Collectors.toList());

            handleEmptyList.forEach(item -> {
                int index = datas.indexOf(item);
                datas.set(index, item);
            });

            //获取稽核字段 和 组合唯一索引
            String auditFieldsIndex = ruleInfo.getAuditFieldsIndex();
            List<String> indexList = Arrays.stream(auditFieldsIndex.split(",")).collect(Collectors.toList());
            //查询结果生成唯一索引 MAP
            Map<String, JSONObject> indexMap = qryRes.stream().collect(Collectors.toMap(key -> {
                StringBuilder res = new StringBuilder();
                for (String item : indexList) {
                    res.append(key.get(item));
                }
                return res.toString();
            }, val -> val));
            //文件生成为一索引 MAP
            Map<String, AuditFileDatasourceCompareDto> fileDataMap = datas.stream().collect(Collectors.toMap(key -> {
                StringBuilder res = new StringBuilder();
                headIndexMap.forEach((indexKey, val) -> {
                    res.append(key.getRecord().get(val));
                });
                return res.toString();
            }, val -> val));

            //比对数据
            fileDataMap.forEach((key, val) -> {
                if (indexMap.containsKey(key)) {
                    JSONObject qryResOneRecord = indexMap.get(key);
                    compareDto(val, qryResOneRecord);
                    indexMap.remove(key);
                } else {
                    val.setFileMore(true);
                }
            });
            //写入结果 indexMap 数据库多出来的数据.
            log.info("result : {}", JSONUtil.toJsonStr(fileDataMap));
            log.info("indexMap : {}", JSONUtil.toJsonStr(indexMap));

            if (!indexMap.isEmpty()) {
                this.auditStatus = AuditFileDatasourceStatus.UN_NORMAL;
            }

            LinkedList<ExcelColorDto> excelColors = constructColorList(fileDataMap);

            //处理要写的数据
            LinkedList<ArrayList<String>> writeData = datas.stream().map(e -> {
                ArrayList<String> record = new ArrayList<>();
                e.getRecord().forEach((key, val) -> record.add(val));
                return record;
            }).collect(Collectors.toCollection(LinkedList::new));
            indexMap.forEach((key, val) -> {
                int lastRow = writeData.size();
                ArrayList<String> record = new ArrayList<>();
                headFieldMap.forEach((fieldName, index) -> {
                    record.add(val.getString(fieldName));
                    ExcelColorDto temp = new ExcelColorDto();
                    temp.setRowNo(lastRow + 1);
                    temp.setColumnNo(index);
                    temp.setColor(IndexedColors.BLUE_GREY);
                    excelColors.add(temp);
                });
                writeData.addLast(record);
            });
            String newFileName = fileService.getAuditedFileName(fileInfo);

            ByteArrayOutputStream outFileOutStream = new ByteArrayOutputStream();
            //进行写入
            EasyExcel.write(outFileOutStream).registerWriteHandler(new FileAuditDatasourceWriteHandler(excelColors)).sheet().head(getHead()).doWrite(writeData);
            ByteArrayInputStream outFileInputStream = new ByteArrayInputStream(outFileOutStream.toByteArray());

            String contentType = "text/plain";
            // 使用字节数组创建 CommonsMultipartFile
            CommonsMultipartFile multipartFile = createCommonsMultipartFile(outFileOutStream.toByteArray(), newFileName, contentType);
            this.generateUpFileId = fileService.uploadReturnId(newFileName, FileUtil.calculateMD5(outFileInputStream), multipartFile, UploadFileType.AUDITED_FILE);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            this.auditStatus = AuditFileDatasourceStatus.AUDIT_EXCEPTION;
            throw new RuntimeException(e);
        } finally {
            logService.addBatchAuditFileDatasourceLog(Collections.singletonList(constructAuditFileDatasourceLogPo()));
        }
    }

    private AuditFileDatasourceLogPo constructAuditFileDatasourceLogPo() {
        AuditFileDatasourceLogPo po = new AuditFileDatasourceLogPo();
        po.setFileId(generateUpFileId);
        po.setRuleId(ruleInfo.getId());
        po.setAuditSqlClob(ruleInfo.getAuditSqlClob());
        po.setStatus(this.auditStatus.getStatus());
        SysUserEntity principal = (SysUserEntity) SecurityUtils.getSubject().getPrincipal();
        po.setCreateBy(principal.getUserId());
        this.endTime = System.currentTimeMillis();
        po.setCostTime(String.valueOf(endTime - beginTime));
        po.setRunDatasourceId(ruleInfo.getRunDatasourceId());
        return po;
    }

    private CommonsMultipartFile createCommonsMultipartFile(byte[] fileContent, String fileName, String contentType) {
        // 创建 DiskFileItem，指定文件名、内容类型、是否为表单字段、文件名、大小、临时目录
        DiskFileItem fileItem = new DiskFileItem("file",                      // 表单字段名
                contentType,                 // 内容类型
                false,                       // 不是表单字段
                fileName,                    // 原始文件名
                fileContent.length,          // 文件大小
                null                         // 临时目录（可以是 null）
        );

        // 将 ByteArrayInputStream 的数据写入 DiskFileItem
        try (ByteArrayInputStream inputStream = new ByteArrayInputStream(fileContent); OutputStream os = fileItem.getOutputStream()) {
            int read;
            byte[] buffer = new byte[1024];
            while ((read = inputStream.read(buffer)) != -1) {
                os.write(buffer, 0, read);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 创建并返回 CommonsMultipartFile
        return new CommonsMultipartFile(fileItem);
    }

    private List<List<String>> getHead() {
        Set<String> heads = this.headFieldMap.keySet();
        List<List<String>> head = new ArrayList<>();
        for (String s : heads) {
            List<String> oneHead = new ArrayList<>();
            oneHead.add(s);
            head.add(oneHead);
        }
        return head;
    }

    private LinkedList<ExcelColorDto> constructColorList(Map<String, AuditFileDatasourceCompareDto> colorList) {
        LinkedList<ExcelColorDto> res = new LinkedList<>();
        if (colorList == null || colorList.isEmpty()) {
            return res;
        }
        colorList.forEach((key, val) -> {
            if (val.isFileMore()) {
                Map<Integer, String> record = val.getRecord();
                record.forEach((recordKey, recordVal) -> {
                    ExcelColorDto temp = new ExcelColorDto();
                    temp.setRowNo(val.getRow());
                    temp.setColumnNo(recordKey);
                    temp.setColor(IndexedColors.YELLOW);
                    res.add(temp);
                });
                return;
            }

            if (val.getDifColumns().isEmpty()) {
                return;
            }

            if (val.isDifferent()) {
                for (Integer difColumn : val.getDifColumns()) {
                    ExcelColorDto temp = new ExcelColorDto();
                    temp.setRowNo(val.getRow());
                    temp.setColumnNo(difColumn);
                    temp.setColor(IndexedColors.RED);
                    res.add(temp);
                }
            }
        });
        return res;
    }

    private void compareDto(AuditFileDatasourceCompareDto fileRecord, JSONObject qryRecord) {
        Map<Integer, String> record = fileRecord.getRecord();
        headFieldMap.forEach((key, val) -> {
            String fileFieldVal = record.get(val);
            String qryResVal = qryRecord.getString(key);
            if ((fileFieldVal == null && qryResVal != null) || (fileFieldVal != null && qryResVal == null)) {
                fileRecord.setDifferent(true);
                fileRecord.getDifColumns().add(val);
                this.auditStatus = AuditFileDatasourceStatus.UN_NORMAL;
            }
            if (fileFieldVal == null && qryResVal == null) {
                return;
            }
            if (fileFieldVal != null && !fileFieldVal.equals(qryResVal)) {
                fileRecord.setDifferent(true);
                fileRecord.getDifColumns().add(val);
                this.auditStatus = AuditFileDatasourceStatus.UN_NORMAL;
            }
        });

    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        List<String> ruleFields = Arrays.stream(ruleInfo.getAuditFields().split(",")).collect(Collectors.toList());
        int fileHeadSize = headMap.size();
        if (ruleFields.size() != fileHeadSize) {
            throw new IllegalArgumentException("规则字段和文件表头字段数量不匹配.");
        }
        String auditFieldsIndex = ruleInfo.getAuditFieldsIndex();
        List<String> indexList = Arrays.stream(auditFieldsIndex.split(",")).collect(Collectors.toList());
        headMap.forEach((key, val) -> {
            if (!ruleFields.contains(val.getStringValue())) {
                throw new IllegalArgumentException("规则字段和文件标题对应不上:" + val.getStringValue());
            }
            if (indexList.contains(val.getStringValue())) {
                headIndexMap.put(val.getStringValue(), key);
            }
            headFieldMap.put(val.getStringValue(), key);
        });

        ReadListener.super.invokeHead(headMap, context);
    }
}
