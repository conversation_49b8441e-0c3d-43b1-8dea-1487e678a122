package com.suke.czx.newland.service.impl;


import com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper;
import com.suke.czx.newland.service.IExcelService;
import com.suke.czx.newland.util.DateUtils;
import com.suke.czx.newland.vo.audit.AuditConfigVo;
import com.suke.czx.newland.vo.audit.AuditLogVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.jsoup.helper.DataUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.text.ParseException;
import java.util.*;


@Service
public class ExcelServiceImpl implements IExcelService {

    private static final Logger logger = LoggerFactory.getLogger(ExcelServiceImpl.class);


    @Autowired(required = false)
    private IAuditCfgMapper iAuditCfgMapper;

    /**
     * @param wb TODO 新增
     * <AUTHOR>
     */
    @Override
    public void importResult(Workbook wb) {

        try {
            Sheet fbs = null;
            Row row = null;
            //根据路径获取这个操作excel的实例
            List<AuditLogVo> importDatas = new ArrayList<>();
            int sheetNum = wb.getNumberOfSheets();
            for (int j = 0; j < sheetNum; j++) {
                String sheetName = wb.getSheetName(j);
                fbs = wb.getSheetAt(j);

                for (int i = 1; i < fbs.getPhysicalNumberOfRows(); i++) {
                    AuditLogVo data = new AuditLogVo();
                    //获取每一行数据
                    row = fbs.getRow(i);
                    for (int z = 0; z < 25; z++) {
                        Cell cell = row.getCell(z);
                        if (cell == null || cell.getCellType() == CellType.BLANK) {
                            continue; // 跳过当前循环迭代，处理下一列
                        }
                        switch (z) {
                            case 0:
                                cell.setCellType(CellType.STRING);
                                data.setLogId(Long.parseLong(cell.getStringCellValue()));
                                break;
                            case 1:
                                cell.setCellType(CellType.STRING);
                                data.setDbName(cell.getStringCellValue());
                                break;
                            case 2:
                                cell.setCellType(CellType.STRING);
                                data.setDbMachine(cell.getStringCellValue());
                                break;
                            case 3:
                                cell.setCellType(CellType.STRING);
                                data.setRuleId(Long.parseLong(cell.getStringCellValue()));
                                break;
                            case 4:
                                cell.setCellType(CellType.STRING);
                                data.setModifyPerson(row.getCell(4).getStringCellValue());
                                break;
                            case 5:
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    //说明是修改过的
                                    data.setModifyDate(cell.getDateCellValue());
                                }else {
                                    data.setModifyDate(DateUtils.getDate(cell.getStringCellValue()));
                                }
                                break;
                            case 6:
                                cell.setCellType(CellType.STRING);
                                data.setChkTypeId(row.getCell(6).getStringCellValue());
                                break;
                            case 7:
                                cell.setCellType(CellType.STRING);
                                data.setChkTypeName(row.getCell(7).getStringCellValue());
                                break;
                            case 8:
                                cell.setCellType(CellType.STRING);
                                data.setCenterId(row.getCell(8).getStringCellValue());
                                break;
                            case 9:
                                cell.setCellType(CellType.STRING);
                                data.setCenterName(row.getCell(9).getStringCellValue());
                                break;
                            case 10:
                                cell.setCellType(CellType.STRING);
                                data.setModleId(row.getCell(10).getStringCellValue());
                                break;
                            case 11:
                                cell.setCellType(CellType.STRING);
                                data.setModleName(row.getCell(11).getStringCellValue());
                                break;
                            case 12:
                                cell.setCellType(CellType.STRING);
                                data.setRuleName(row.getCell(12).getStringCellValue());
                                break;
                            case 13:
                                cell.setCellType(CellType.STRING);
                                data.setRuleSql(row.getCell(13).getStringCellValue());
                                break;
                            case 14:
                                cell.setCellType(CellType.STRING);
                                data.setLimitValue(row.getCell(14).getStringCellValue());
                                break;
                            case 15:
                                cell.setCellType(CellType.STRING);
                                data.setSqlValue(row.getCell(15).getStringCellValue());
                                break;
                            case 16:
                                cell.setCellType(CellType.STRING);
                                data.setStatus(Short.parseShort(row.getCell(16).getStringCellValue()));
                                break;
                            case 17:
                                cell.setCellType(CellType.STRING);
                                data.setConclusion(row.getCell(17).getStringCellValue());
                                break;
                            case 18:
                                cell.setCellType(CellType.STRING);
                                data.setExecMsec(Long.parseLong(row.getCell(18).getStringCellValue()));
                                break;
                            case 19:
                                cell.setCellType(CellType.STRING);
                                data.setExecRemark(row.getCell(19).getStringCellValue());
                                break;
                            case 20:
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    //修改后的
                                    data.setCreateDate(cell.getDateCellValue());
                                }else {
                                    //未修改的
                                    data.setCreateDate(DateUtils.getDate(cell.getStringCellValue()));
                                }
                                break;
                            case 21:
                                cell.setCellType(CellType.STRING);
                                data.setCreatePerson(row.getCell(21).getStringCellValue());
                                break;
                            case 22:
                                if (cell.getCellType() == CellType.NUMERIC) {
                                    data.setExecDate(cell.getDateCellValue());
                                }else {
                                    data.setExecDate(DateUtils.getDate(cell.getStringCellValue()));
                                }
                                break;
                            case 23:
                                cell.setCellType(CellType.STRING);
                                data.setExecPerson(row.getCell(23).getStringCellValue());
                                break;
                            case 24:
                                cell.setCellType(CellType.STRING);
                                data.setAnalysisRes(row.getCell(24).getStringCellValue());
                                break;
                            default:
                                logger.info("异常==================");
                        }
                    }

                    logger.info("data的值：{}", data);
                    importDatas.add(data);
                }
            }
            if (!importDatas.isEmpty()) {
                iAuditCfgMapper.importResultData(importDatas);
            }
        } catch (Exception e) {
            logger.error("插入数据异常", e);
            throw new RuntimeException("插入数据异常", e);
        }
    }

    /**
     * @param wb TODO 新增
     * <AUTHOR>
     */
    @Override
    public void importAudit(Workbook wb) {
        try {
            Sheet fbs = null;
            Row row = null;
            //根据路径获取这个操作excel的实例
            List<AuditConfigVo> importDatas = new ArrayList<>();
            int sheetNum = wb.getNumberOfSheets();
            for (int j = 0; j < sheetNum; j++) {
                fbs = wb.getSheetAt(j);
                for (int i = 1; i < fbs.getPhysicalNumberOfRows(); i++) {
                    //获取每一行数据
                    row = fbs.getRow(i);
                    AuditConfigVo data = constructAuditVoFrRow(row);
                    logger.debug("data的值:{}", data);
                    importDatas.add(data);
                }
            }
            if (importDatas.size() > 0) {
                iAuditCfgMapper.importAuditData(importDatas);
            }

        } catch (Exception e) {
            logger.error("插入数据异常", e);
            throw new RuntimeException("插入数据异常", e);
        }
    }
    public AuditConfigVo constructAuditVoFrRow(Row row){
        try {
            AuditConfigVo temp = new AuditConfigVo();
            for (int i = 0; i < 14; i++) {
                Cell cell = row.getCell(i);
                if (cell == null || cell.getCellType() == CellType.BLANK) {
                    continue;//跳过循环，处理下一列
                }
                switch (i) {
                    case 0:
                        cell.setCellType(CellType.STRING);
                        String ruleId = cell.getStringCellValue();
                        //12 数据库字段长度
                        if (ruleId == null || ruleId.isEmpty() || ruleId.length() > 12){
                            throw new IllegalArgumentException("rule id 不能为空，且长度不能大于 12. 对应行数:"+ row.getRowNum());
                        }
                        temp.setRuleId(Long.parseLong(ruleId));
                        break;
                    case 1:
                        cell.setCellType(CellType.STRING);
                        String ruleName = cell.getStringCellValue();
                        //200 数据库字段长度
                        if (ruleName == null || ruleName.isEmpty() || ruleName.length() > 200){
                            throw new IllegalArgumentException("rule name 不能为空，且长度不能大于 200. 对应行数:"+ row.getRowNum());
                        }
                        temp.setRuleName(ruleName);
                        break;
                    case 2:
                        cell.setCellType(CellType.STRING);
                        String status = cell.getStringCellValue();
                        //1 数据库字段长度
                        if (status == null || status.length() != 1){
                            throw new IllegalArgumentException("status 不能为空，且长度需要等于1. 对应行数:"+ row.getRowNum());
                        }
                        temp.setStatus(Short.parseShort(status));
                        break;
                    case 3:
                        row.getCell(3).setCellType(CellType.STRING);
                        temp.setCreatePerson(row.getCell(3).getStringCellValue());
                        break;
                    case 4:
                        if (cell.getCellType() == CellType.NUMERIC) {
                            temp.setCreateDate(cell.getDateCellValue());
                        }else {
                            temp.setCreateDate(DateUtils.getDate(cell.getStringCellValue()));
                        }
                        break;
                    case 5:
                        row.getCell(5).setCellType(CellType.STRING);
                        temp.setModifyPerson(row.getCell(5).getStringCellValue());
                        break;
                    case 6:
                        if (cell.getCellType() == CellType.NUMERIC) {
                            temp.setCreateDate(cell.getDateCellValue());
                        }else {
                            temp.setModifyDate(DateUtils.getDate(cell.getStringCellValue()));
                        }
                        break;
                    case 7:
                        cell.setCellType(CellType.STRING);
                        String checkTypeId = cell.getStringCellValue();

                        // 10 字段长度
                        if (checkTypeId == null || checkTypeId.isEmpty() || checkTypeId.length() > 10){
                            throw new IllegalArgumentException("checkTypeId 不能为空，且长度不能大于10. 对应行数:"+ row.getRowNum());
                        }
                        temp.setChkTypeId(checkTypeId);
                        break;
                    case 8:
                        cell.setCellType(CellType.STRING);
                        String centerId = cell.getStringCellValue();
                        // 10 字段长度
                        if (centerId == null || centerId.isEmpty() || centerId.length() > 10){
                            throw new IllegalArgumentException("centerId 不能为空，且长度不能大于10. 对应行数:"+ row.getRowNum());
                        }
                        temp.setCenterId(centerId);
                        break;
                    case 9:
                        cell.setCellType(CellType.STRING);
                        String modleId = cell.getStringCellValue();
                        // 10 字段长度
                        if (modleId == null || modleId.isEmpty() || modleId.length() > 10){
                            throw new IllegalArgumentException("modleId 不能为空，且长度不能大于10. 对应行数:"+ row.getRowNum());
                        }
                        temp.setModleId(modleId);
                        break;
                    case 10:
                        cell.setCellType(CellType.STRING);
                        temp.setRuleSql(cell.getStringCellValue());
                        break;
                    case 11:
                        cell.setCellType(CellType.STRING);
                        temp.setLimitValue(cell.getStringCellValue());
                        break;
                    case 12:
                        cell.setCellType(CellType.STRING);
                        temp.setRuleDesc(cell.getStringCellValue());
                        break;
                    case 13:
                        cell.setCellType(CellType.STRING);
                        temp.setEnvType(cell.getStringCellValue());
                        break;
                    default:
                        logger.info("位置列数错误");
                }
            }

            return temp;
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取字符串值
     *
     * @param sourceObject 源对象
     * @return 字符串
     */
    private static String getStringValue(Object sourceObject) {
        return Optional.ofNullable(sourceObject).map(Object::toString).orElse(StringUtils.EMPTY);
    }
}
