package com.suke.czx.modules.rule.service;

import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface AuditRuleConfigService {

    /**
     * 根据规则表的父id查询规则子表
     * @return
     */
    List<AuditRuleConfig> queryRuleConfigList(String faId,String type);

    /**
     * 更新规则信息（子表）
     * @param auditRuleConfig
     * @return
     */
    Integer updateRuleConfig(AuditRuleConfig auditRuleConfig);

    /**
     * 根据id删除规则文件(子表)
     * @param id
     * @return
     */
    Integer delRuleConfigByid(Long id);

    void delRuleConfigByIds(String[] ids);

    /**
     * 根据id获取规则的详细信息
     * @param id
     * @return
     */
    AuditRuleConfig queryRuleConfigInfoById(Long id);

    /**
     * 保存单条规则信息，在页面点击添加规则按钮（向规则子表中）
     * @param auditRuleConfig
     * @return
     */
    void addRuleConfig(AuditRuleConfig auditRuleConfig);
    /**
     * 子规则明细查询，下载使用
     * @param faId
     * @return
     */
    List<AuditRuleConfig> getAuditRuleById(String faId);
}
