package com.suke.czx.modules.sys.service.impl;

import com.suke.czx.modules.sys.dao.SysMenuDao;
import com.suke.czx.modules.sys.dao.SysUserDao;
import com.suke.czx.modules.sys.dao.SysUserTokenDao;
import com.suke.czx.modules.sys.entity.SysUserTokenEntity;
import com.suke.czx.modules.sys.service.ShiroService;
import com.suke.czx.modules.sys.entity.SysUserEntity;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ShiroServiceImpl implements ShiroService {
    @Autowired
    private SysMenuDao sysMenuDao;
    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private SysUserTokenDao sysUserTokenDao;

    @Override
    public Set<String> getUserPermissions(String userId) {
        List<String> permsList;

        //查询系统全部权限
        permsList = sysUserDao.queryAllPerms(userId);

        //用户权限列表
        Set<String> permsSet = new HashSet<>();
        for(String perms : permsList){
            if(StringUtils.isBlank(perms)){
                continue;
            }
            permsSet.addAll(Arrays.asList(perms.trim().split(",")));
        }
        return permsSet;
    }

    @Override
    public SysUserTokenEntity queryByToken(String token) {
        return sysUserTokenDao.queryByToken(token);
    }

    @Override
    public SysUserEntity queryUser(String userId) {
        return sysUserDao.queryObject(userId);
    }
}
