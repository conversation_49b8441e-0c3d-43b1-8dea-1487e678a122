package com.suke.czx.modules.aitrain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模型训练的参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelTrainParam {

    /**
     * 正常训练文件名
     */
    private String normalFileName;

    /**
     * 异常训练文件名
     */
    private String abnormalFileName;


    /**
     * 原始的的字段索引下标列表（正常文件）
     */
    private List<Integer> normalOriginFieldIndexList;

    /**
     * 原始的的字段索引下标列表（异常文件）
     */
    private List<Integer> abnormalOriginFieldIndexList;

    /**
     * 被选中的字段索引下标列表（正常文件）
     */
    private List<Integer> normalCheckFieldIndexList;

    /**
     * 被选中的字段索引下标列表（异常文件）
     */
    private List<Integer> abnormalCheckFieldIndexList;

    /**
     * 数据集表中标签列名称，默认为'label'
     */
    private String labelColumn;

    /**
     * 评估指标：选择是分类还是回归问题
     */
    private String metric;


    /**
     * 预设模型质量，默认为'medium_quality'
     */
    private String preset;

    /**
     * 测试集比例，默认为0.2
     */
    private String testSize;

    /**
     * 随机种子，用于数据集划分，默认为1926
     */
    private String randomState;

    /**
     * 分类问题，默认为None，表示自动基于数据集标签判别。(选项: ['binary', 'multiclass', 'regression'])
     */
    private String problemType;

    /**
     * 模型名称
     */
    private String modelName;
}
