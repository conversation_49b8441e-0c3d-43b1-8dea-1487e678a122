package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFRichTextString;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.util.HashMap;
import java.util.Map;

public abstract class ExcelHandleRuler {
    public abstract Map<String, Object> sheetHandle(SheetBaseDto condition);


    void setCellRed(Cell cell, CellStyle cellStyle) {
        cellStyle.cloneStyleFrom(cell.getCellStyle());
        cellStyle.setFillForegroundColor(IndexedColors.RED.getIndex());
        cellStyle.setFillBackgroundColor(IndexedColors.RED.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cell.setCellStyle(cellStyle);
    }

    protected void setCellStr(Cell cell) {
        cell.setCellType(CellType.STRING);
    }

    protected Map<String, Object> success() {
        Map<String, Object> res = new HashMap<>();
        res.put("status", 2);
        return res;
    }

    protected Map<String, Object> fail() {
        Map<String, Object> res = new HashMap<>();
        res.put("status", 3);
        return res;
    }

    protected boolean isCellNumeric(Cell cell) {
        if (cell == null) {
            return true;
        }
        return cell.getCellType().equals(CellType.NUMERIC);
    }

    protected void setCellComment(Sheet sheet, Cell cell, String msg) {
        Workbook workbook = sheet.getWorkbook();
        Drawing<?> drawing = sheet.createDrawingPatriarch();
        //创建锚点
        ClientAnchor anchor;
        if (workbook instanceof HSSFWorkbook) {
            anchor = new HSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6);
        }else if (workbook instanceof XSSFWorkbook) {
            anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 3, 3, (short) 5, 6);
        }else {
            throw new IllegalArgumentException("不支持的文件类型");
        }
        //创建注释
        Comment comment = drawing.createCellComment(anchor);
        //设置注释内容
        RichTextString richTextString;
        if (workbook instanceof HSSFWorkbook) {
            richTextString = new HSSFRichTextString(msg);
        }else if (workbook instanceof XSSFWorkbook) {
            richTextString = new XSSFRichTextString(msg);
        }else {
            throw new IllegalArgumentException("不支持的文件类型");
        }
        comment.setString(richTextString);
        cell.setCellComment(comment);
    }
}
