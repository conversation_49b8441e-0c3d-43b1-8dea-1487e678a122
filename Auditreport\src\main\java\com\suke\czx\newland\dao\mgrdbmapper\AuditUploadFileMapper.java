package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.AuditUploadQryConditionDto;
import com.suke.czx.newland.po.AuditUploadFilePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AuditUploadFileMapper {

    AuditUploadFilePo qryAuditFileWithAuditFileId(@Param("auditFileId") String auditFileId);

    List<AuditUploadFilePo> qryAuditFileWithAuditFileIds(List<String> ids);

    AuditUploadFilePo qryAuditFileWithAuditFileIdWithoutStatus(@Param("auditFileId") String auditFileId);

    void insert(AuditUploadFilePo record);

    void insertReturnId(AuditUploadFilePo record);

    void delAuditFileWithAuditFileId(@Param("auditFileId") String auditFileId);

    void delBatchAuditFileWithAuditFileIds(List<String> ids);

    List<AuditUploadFilePo> qryAuditWithCondition(AuditUploadQryConditionDto condition);
}
