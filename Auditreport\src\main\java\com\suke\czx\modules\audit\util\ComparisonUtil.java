package com.suke.czx.modules.audit.util;

import com.alibaba.druid.util.Utils;
import com.suke.czx.modules.audit.entity.AuditComparison;
import com.suke.czx.modules.audit.rules.*;
import com.suke.czx.modules.audit.rules.dto.*;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFDataFormatter;
import org.apache.poi.ss.usermodel.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 处理比对时的一些数据
 */

@Slf4j
public class ComparisonUtil {

    /**
     * 比较规则的类型然后进行比对
     *
     * @param sheet      一个sheet
     * @param ruleConfig 具体的比对规则
     * @param map        返回结果的map
     * @param cellStyle  设置单元格的样式(此处设置背景为红色)
     * @return 最终返回map
     */
    public static Map<String, Object> compareAndComparison(Sheet sheet, AuditRuleConfig ruleConfig, Map<String, Object> map, CellStyle cellStyle) {
        //起始行
        int startRow = ruleConfig.getTitleRow();
        //最后一行
        int lastRowNum = sheet.getLastRowNum();
        log.info("start row num : {} , last row num : {}", startRow, lastRowNum);
        log.info("audit rule cfg : {}", ruleConfig);
        //输入行值
        int ruleRow = ruleConfig.getRuleRow();
        try {
            switch (ruleConfig.getType()) {
                case "TOGETHER":
                    ExcelHandleRuler togetherHandler = new TogetherHandler();
                    SheetTogetherDto condition = new SheetTogetherDto();
                    condition.setSheet(sheet);
                    condition.setCellStyle(cellStyle);
                    if (ruleRow < 0) {
                        condition.setStartRow(lastRowNum + ruleRow);
                        condition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        condition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            condition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            condition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        condition.setStartRow(ruleRow - 1);
                        condition.setEndRow(ruleRow - 1);
                    }
                    condition.setColumn(ruleConfig.getRuleColumn() - 1);
                    condition.setMatchStr(ruleConfig.getParameter());
                    return togetherHandler.sheetHandle(condition);
                case "SUM":
                    ExcelHandleRuler excelSumHandler = new ExcelSumHandler();
                    SheetSumDto sumCondition = new SheetSumDto();
                    sumCondition.setCellStyle(cellStyle);
                    sumCondition.setSheet(sheet);
                    sumCondition.setStartRow(startRow);
                    if (ruleRow < 0) {
                        sumCondition.setEndRow(lastRowNum + ruleRow);
                        sumCondition.setTargetRow(lastRowNum + ruleRow + 1);
                    } else {
                        sumCondition.setEndRow(ruleRow - 2);
                        sumCondition.setTargetRow(ruleRow - 1);
                    }
                    sumCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    sumCondition.setTargetColumn(ruleConfig.getRuleColumn() - 1);
                    return excelSumHandler.sheetHandle(sumCondition);
                case "LT":
                    ExcelHandleRuler lessThanHandler = new LessThanHandler();
                    SheetLessThanDto lessCondition = new SheetLessThanDto();
                    lessCondition.setCellStyle(cellStyle);
                    lessCondition.setSheet(sheet);
                    lessCondition.setTargetVal(new BigDecimal(ruleConfig.getParameter()));
                    if (ruleRow < 0) {
                        lessCondition.setStartRow(lastRowNum + ruleRow);
                        lessCondition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        lessCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            lessCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            lessCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        lessCondition.setStartRow(ruleRow - 1);
                        lessCondition.setEndRow(ruleRow - 1);
                    }
                    lessCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    return lessThanHandler.sheetHandle(lessCondition);
                case "GT":
                    ExcelHandleRuler graterThanHandler = new GraterThanHandler();
                    SheetGraterThanDto graterCondition = new SheetGraterThanDto();
                    graterCondition.setCellStyle(cellStyle);
                    graterCondition.setSheet(sheet);
                    graterCondition.setTargetVal(new BigDecimal(ruleConfig.getParameter()));
                    if (ruleRow < 0) {
                        graterCondition.setStartRow(lastRowNum + ruleRow);
                        graterCondition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        graterCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            graterCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            graterCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        graterCondition.setStartRow(ruleRow - 1);
                        graterCondition.setEndRow(ruleRow - 1);
                    }
                    graterCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    return graterThanHandler.sheetHandle(graterCondition);
                case "EQUAL":
                    EqualHandler equalHandler = new EqualHandler();
                    SheetEqualDto equalDto = new SheetEqualDto();
                    equalDto.setCellStyle(cellStyle);
                    equalDto.setSheet(sheet);
                    String cfgParam = ruleConfig.getParameter();
                    String[] params = cfgParam.split(",");
                    List<Integer> columns = new ArrayList<>();
                    for (String param : params) {
                        columns.add(Integer.parseInt(param) - 1);
                    }
                    equalDto.setComponentColumn(columns);
                    equalDto.setColumn(ruleConfig.getRuleColumn() - 1);
                    if (ruleRow < 0) {
                        equalDto.setStartRow(lastRowNum + ruleRow);
                        equalDto.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        equalDto.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            equalDto.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            equalDto.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        equalDto.setStartRow(ruleRow - 1);
                        equalDto.setEndRow(ruleRow - 1);
                    }
                    return equalHandler.sheetHandle(equalDto);
                case "SORT":
                    ExcelHandleRuler sortHandler = new SortHandler();
                    SheetSortDto sortCondition = new SheetSortDto();
                    sortCondition.setCellStyle(cellStyle);
                    sortCondition.setSheet(sheet);
                    if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        sortCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            sortCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            sortCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        log.info("sort rule error, rule row must be 99999.");
                        map.put("status", 4);
                        return map;
                    }
                    sortCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    return sortHandler.sheetHandle(sortCondition);
                case "IN":
                    ExcelHandleRuler inHandler = new InHandler();
                    SheetInDto inCondition = new SheetInDto();
                    inCondition.setCellStyle(cellStyle);
                    inCondition.setSheet(sheet);
                    if (ruleRow < 0) {
                        inCondition.setStartRow(lastRowNum + ruleRow);
                        inCondition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        inCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            inCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            inCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        inCondition.setStartRow(ruleRow - 1);
                        inCondition.setEndRow(ruleRow - 1);
                    }
                    inCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    List<String> matchList = null;
                    if (ruleConfig.getParameter().contains(",")) {
                        matchList = Arrays
                                .stream(ruleConfig.getParameter().split(","))
                                .collect(Collectors.toList());
                    } else if (ruleConfig.getParameter().contains("，")) {
                        matchList = Arrays
                                .stream(ruleConfig.getParameter().split("，"))
                                .collect(Collectors.toList());
                    }
                    inCondition.setMatchList(matchList);
                    return inHandler.sheetHandle(inCondition);
                case "NOTEMPTY":
                    ExcelHandleRuler notEmptyHandler = new NotEmptyHandler();
                    SheetNotEmptyDto notEmptyCondition = new SheetNotEmptyDto();
                    notEmptyCondition.setCellStyle(cellStyle);
                    notEmptyCondition.setSheet(sheet);
                    if (ruleRow < 0) {
                        notEmptyCondition.setStartRow(lastRowNum + ruleRow);
                        notEmptyCondition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        notEmptyCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            notEmptyCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            notEmptyCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        notEmptyCondition.setStartRow(ruleRow - 1);
                        notEmptyCondition.setEndRow(ruleRow - 1);
                    }
                    notEmptyCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    return notEmptyHandler.sheetHandle(notEmptyCondition);
                case "NOTLIKE":
                    ExcelHandleRuler notLikeHandler = new NotLikeHandler();
                    SheetNotLikeDto notLikeCondition = new SheetNotLikeDto();
                    notLikeCondition.setCellStyle(cellStyle);
                    notLikeCondition.setSheet(sheet);
                    if (ruleRow < 0) {
                        notLikeCondition.setStartRow(lastRowNum + ruleRow);
                        notLikeCondition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        notLikeCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            notLikeCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            notLikeCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        notLikeCondition.setStartRow(ruleRow - 1);
                        notLikeCondition.setEndRow(ruleRow - 1);
                    }
                    notLikeCondition.setRule(ruleConfig.getParameter());
                    notLikeCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    return notLikeHandler.sheetHandle(notLikeCondition);
                case "NUMBER":
                    ExcelHandleRuler numberHandler = new NumberHandler();
                    SheetNumberDto numCondition = new SheetNumberDto();
                    numCondition.setCellStyle(cellStyle);
                    numCondition.setSheet(sheet);
                    if (ruleRow < 0) {
                        numCondition.setStartRow(lastRowNum + ruleRow);
                        numCondition.setEndRow(lastRowNum + ruleRow + 1);
                    } else if ("99999".equals(ruleConfig.getRuleRow().toString())) {
                        numCondition.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            numCondition.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            numCondition.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        numCondition.setStartRow(ruleRow - 1);
                        numCondition.setEndRow(ruleRow - 1);
                    }
                    numCondition.setRule(ruleConfig.getParameter());
                    numCondition.setColumn(ruleConfig.getRuleColumn() - 1);
                    return numberHandler.sheetHandle(numCondition);
                case "UNIQUE":
                    ExcelHandleRuler uniqueHandler = new UniqueHandler();
                    SheetUniqueDto uniqueDto = new SheetUniqueDto();
                    uniqueDto.setCellStyle(cellStyle);
                    uniqueDto.setSheet(sheet);
                    String uniqueParams = ruleConfig.getParameter();
                    String[] paramsArr = uniqueParams.split(",");
                    List<Integer> temp = new ArrayList<>();
                    for (String param : paramsArr) {
                        temp.add(Integer.parseInt(param) - 1);
                    }
                    uniqueDto.setColumns(temp);
                    if ("99999".equals(ruleConfig.getRuleRow().toString())
                            && "99999".equals(ruleConfig.getRuleColumn().toString())) {
                        uniqueDto.setStartRow(startRow);
                        if (ruleConfig.getTitleEndRow() > 0) {
                            uniqueDto.setEndRow(ruleConfig.getTitleEndRow() - 2);
                        } else {
                            uniqueDto.setEndRow(lastRowNum + ruleConfig.getTitleEndRow());
                        }
                    } else {
                        throw new IllegalArgumentException("UNIQUE row column参数配置错误，只能配置99999.");
                    }
                    return uniqueHandler.sheetHandle(uniqueDto);

                default:
                    map.put("status", 0);
                    map.put("count", 0);
                    log.info("规则类型无法找到，比对失败!");
                    return map;
            }
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            map.put("status", 4);
            return map;
        }
    }
}
