package com.suke.czx.modules.aitrain.mapper;

import com.suke.czx.modules.aitrain.entity.ModelTrain;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ModelTrainMapper {

    /**
     * 获取全部的训练数据信息
     * @return
     */
    List<ModelTrain> getAll();

    /**
     * 获取训练名称列表
     * @return
     */
    List<String> getTrainNameList();

    /**
     * 保存训练数据信息
     * @param modelTrain
     * @return
     */
    int insert(ModelTrain modelTrain);

    /**
     * 批量插入训练数据信息
     * @param modelTrainList
     * @return
     */
    int batchInsert(List<ModelTrain> modelTrainList);

    /**
     * 更新训练数据信息
     * @param modelTrain
     * @return
     */
    int update(ModelTrain modelTrain);

    /**
     * 删除训练数据信息
     * @param id
     * @return
     */
    int deleteById(int id);

    /**
     * 根据训练名称查询训练数据信息
     * @param trainName
     * @return
     */
    List<ModelTrain> queryByTrainName(String trainName);

    /**
     * 查询全部训练名称
     * @return
     */
    List<String> queryAllTrainName();

    /**
     * 获取模型数量
     * @return
     */
    int getModelSum();

    /**
     * 根据训练名称获取模型名称
     * @param trainName
     * @return
     */
    List<String> getModelByTrainName(String trainName);

    /**
     * 根据训练名称获取测试得分
     * @param trainName
     * @return
     */
    List<Double> getScoreTestByTrainName(String trainName);

    /**
     * 根据训练名称获取训练时间
     * @param trainName
     * @return
     */
    List<Double> getFitTimeByTrainName(String trainName);
}
