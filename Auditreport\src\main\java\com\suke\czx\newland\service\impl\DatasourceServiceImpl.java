package com.suke.czx.newland.service.impl;

import com.alibaba.druid.util.Utils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.common.utils.CDESCrypt;
import com.suke.czx.newland.dao.mgrdbmapper.DatasourceDefMapper;
import com.suke.czx.newland.dto.DatasourceInfoDto;
import com.suke.czx.newland.service.DatasourceService;
import com.suke.czx.newland.util.CryptoUtil;
import com.suke.czx.newland.util.DataSourceUtil;
import com.suke.czx.newland.vo.DatasourceInfoQryVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.sql.DriverManager;
import java.util.List;

import static com.suke.czx.common.utils.AppBaseResult.KEY;

@Slf4j
@Service
public class DatasourceServiceImpl implements DatasourceService {

    private final DatasourceDefMapper datasourceDefMapper;


    public DatasourceServiceImpl(DatasourceDefMapper datasourceDefMapper) {
        this.datasourceDefMapper = datasourceDefMapper;
    }

    @Override
    public void addDatasource(DatasourceInfoDto dataBaseInfoBean) {
        try {
            testConnection(DataSourceUtil.getDriver(dataBaseInfoBean.getDatasourceType()), dataBaseInfoBean.getUrl(), dataBaseInfoBean.getDatasourceUsername(), CryptoUtil.decrypt(dataBaseInfoBean.getDatasourcePassword()));
            DatasourceInfoDto handledData = handleDatasourceInfo(dataBaseInfoBean);
            datasourceDefMapper.addDatasourceInfo(handledData);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }


    }

    @Override
    public void updateDatasource(DatasourceInfoDto dataBaseInfoBean) {
        DatasourceInfoDto handledData = handleDatasourceInfo(dataBaseInfoBean);
        datasourceDefMapper.updateDatasourceInfoByDatasourceId(handledData);
    }

    @Override
    public Object qryDatasourceInfoByPage(DatasourceInfoDto condition, int pageIndex, int pageSize) {

        return null;
    }

    @Override
    public void testDatasourceConnection(String datasourceId) {
        DatasourceInfoDto datasourceInfo = datasourceDefMapper.qryDatasourceInfoByDatasourceId(datasourceId);
        if (datasourceInfo == null) {
            throw new RuntimeException("数据源不存在.");
        }
        try {
            String driver = DataSourceUtil.getDriver(datasourceInfo.getDatasourceType());
            datasourceInfo.setDatasourceUsername(CDESCrypt.decryptString(datasourceInfo.getDatasourceUsername(), KEY));
            datasourceInfo.setDatasourcePassword(CryptoUtil.decrypt(datasourceInfo.getDatasourcePassword()));
            testConnection(driver, datasourceInfo.getUrl(), datasourceInfo.getDatasourceUsername(), datasourceInfo.getDatasourcePassword());
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            log.error("数据源测试连接失败.", e);
            throw new RuntimeException("数据源测试连接失败.", e);
        }
    }

    @Override
    public PageInfo<DatasourceInfoDto> qryDatasourceWithPageInfo(DatasourceInfoQryVo qryCondition, int pageIndex, int pageSize) {

        DatasourceInfoDto condition = new DatasourceInfoDto();

        condition.setDatasourceName(qryCondition.getDatasourceName());

        condition.setDatasourceType(qryCondition.getDatasourceType());

        condition.setStatus(qryCondition.getStatus());

        condition.setDatasourceUsername(qryCondition.getDatasourceUsername());

        PageHelper.startPage(pageIndex, pageSize);

        List<DatasourceInfoDto> res = datasourceDefMapper.qryDatasourceWithCondition(condition);
        try {
            for (DatasourceInfoDto dto : res) {
                dto.setDatasourceUsername(CDESCrypt.decryptString(dto.getDatasourceUsername(), KEY));
            }
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException("解密数据源信息失败.", e);
        }
        return new PageInfo<>(res);
    }

    private DatasourceInfoDto handleDatasourceInfo(DatasourceInfoDto param) {
        String username;
        try {
            username = CDESCrypt.encryptString(param.getDatasourceUsername(), KEY);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException("加密数据源信息失败.", e);
        }
        param.setDatasourceUsername(username);
        return param;
    }

    @Override
    public void deleteDatasourceByDatasourceId(String datasourceId) {
        datasourceDefMapper.deleteDatasourceInfoByDatasourceId(datasourceId);
    }

    public void testConnection(String driveClass, String url, String username, String password) {
        try {
            Class.forName(driveClass);
            DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException("测试连接失败，请重新填写有效的连接信息.");
        }
    }

    @Override
    public List<DatasourceInfoDto> qryAllDatasourceActived() {
        List<DatasourceInfoDto> res = datasourceDefMapper.qryAllDatasourceWithActivated();
        try {
            for (DatasourceInfoDto re : res) {
                re.setDatasourceUsername(CDESCrypt.decryptString(re.getDatasourceUsername(), KEY));
            }
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException("解密数据源信息失败.", e);
        }
        return res;
    }
}
