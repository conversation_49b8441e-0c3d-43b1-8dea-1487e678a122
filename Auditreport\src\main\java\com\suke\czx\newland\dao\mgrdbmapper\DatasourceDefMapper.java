package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.DatasourceInfoDto;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DatasourceDefMapper {

    void addDatasourceInfo(DatasourceInfoDto record);

    void updateDatasourceInfoByDatasourceId(DatasourceInfoDto record);

    List<DatasourceInfoDto> qryAllDatasourceWithActivated();

    List<DatasourceInfoDto> qryDatasourceWithCondition(DatasourceInfoDto params);


    @Delete({"delete datasource_def where DATA_SOURCE_ID = #{datasourceId}"})
    void deleteDatasourceInfoByDatasourceId(String datasourceId);

    DatasourceInfoDto qryDatasourceInfoByDatasourceId(String datasourceId);
}
