package com.suke.czx.schedule.impl;

import com.suke.czx.config.CronTaskRegistrar;
import com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper;
import com.suke.czx.newland.vo.audit.AuditConfigVo;
import com.suke.czx.newland.vo.audit.AuditDictVo;
import com.suke.czx.thread.SchedulingRunnable;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CronTaskInitRunner implements CommandLineRunner {

    @Autowired
    @Qualifier("taskScheduler")
    private TaskScheduler taskScheduler;

    @Autowired
    private CronTaskRegistrar cronTaskRegistrar;

    @Autowired
    private IAuditCfgMapper auditCfgMapper;

    @Override
    public void run(String... args) {
        cronTaskRegistrar.setTaskScheduler(taskScheduler);
        List<AuditDictVo> auditDictVoList = auditCfgMapper.qryAllAuditDictByActived();

        if (CollectionUtils.isNotEmpty(auditDictVoList)) {
            auditDictVoList.stream()
                    //不是正规cron  表达式不添加
                    .filter(e -> {
                        if (e.getCronExpression() == null) {
                            return false;
                        } else {
                            return CronExpression.isValidExpression(e.getCronExpression());
                        }
                    })
                    //对应字典不存在匹配的任务 不添加
                    .filter(e -> {
                        List<AuditConfigVo> res = auditCfgMapper.queryRptCheckCfgByRuleIdWithActived(e.getDictValue());
                        log.debug("dict name : {} has dict matched task : {}", e.getDictName(), res);
                        return !res.isEmpty();
                    })
                    .forEach(auditDictVo -> {
                        SchedulingRunnable schedulingRunnable = new SchedulingRunnable(auditDictVo.getDictId(), auditCfgMapper);
                        cronTaskRegistrar.addCronTaskWithDictId(schedulingRunnable, auditDictVo.getCronExpression());
                    });
            log.info("初始化库中配置定时任务信息");
        }
    }
}
