package com.suke.czx.modules.aitrain.controller;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo;
import com.suke.czx.modules.aitrain.entity.GenerateFileFromSqlVO;
import com.suke.czx.modules.aitrain.entity.ModelTrainInfo;
import com.suke.czx.modules.aitrain.entity.ModelTrainParam;
import com.suke.czx.modules.aitrain.entity.ParseVO;
import com.suke.czx.modules.aitrain.service.AiTrainService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/aiTrain")
public class AiTrainController {

    @Resource
    private AiTrainService aiTrainService;

    @PostMapping("generateTrainDataFile")
    public R generateTrainDataFileFromSql(@RequestBody GenerateFileFromSqlVO conditionVo, HttpServletResponse response, HttpServletRequest request) {
        ModelTrainInfo modelTrainInfo = aiTrainService.generateTrainDataFile(conditionVo, response, request);
        return R.ok(modelTrainInfo);
    }

    /**
     * 执行模型训练
     * @param param
     * @return
     */
    @PostMapping("train")
    public R modelTrain(@RequestBody ModelTrainParam param) {
        log.info("modelTrain param:{}", param);
        R res = aiTrainService.modelTrain(param);
        return R.ok(res);
    }

    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public R uploadModelTrainFile(@RequestParam("file") MultipartFile file, @RequestParam("fileType")String fileType) throws Exception {

        log.info("uploadModelTrainFile file:{}", file.getOriginalFilename());
        log.info("uploadModelTrainFile fileSize:{}", file.getSize());

        log.info("uploadModelTrainFile fileType:{}", fileType);
//        ModelTrainInfo modelTrainInfo = aiTrainService.uploadModelTrainFile(file, fileType);
        return R.ok(aiTrainService.uploadModelTrainFile(file, fileType));
    }

    /**
     * 解析模型训练文件（包括字段、行数、列数等）
     * @param parseVO
     * @return
     */
    @PostMapping("parse")
    public R parseModelTrainFile(@RequestBody ParseVO parseVO) {
        Map<String, Object> parseMap = aiTrainService.parseModelTrainFile(parseVO);
        return R.ok(parseMap);
    }

    @GetMapping("getFileName")
    public R getFileName() {
        Map<String,List<String>> fileNameListMap = aiTrainService.getRemoteNormalFileName();
        return R.ok(fileNameListMap);
    }
}
