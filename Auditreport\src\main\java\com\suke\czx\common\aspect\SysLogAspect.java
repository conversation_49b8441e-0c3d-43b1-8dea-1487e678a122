package com.suke.czx.common.aspect;

import com.alibaba.druid.util.Utils;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.HttpContextUtils;
import com.suke.czx.common.utils.IPUtils;
import com.suke.czx.modules.sys.entity.SysLogEntity;
import com.suke.czx.modules.sys.entity.SysUserEntity;
import com.suke.czx.modules.sys.service.SysLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Date;


/**
 * 系统日志，切面处理类
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年3月8日 上午11:07:35
 */
@Slf4j
@Aspect
@Component
public class SysLogAspect {
	@Autowired
	private SysLogService sysLogService;
	
	@Pointcut("@annotation(com.suke.czx.common.annotation.SysLog)")
	public void logPointCut() { 
		
	}

	@Around("logPointCut()")
	public Object around(ProceedingJoinPoint point) throws Throwable {
		long beginTime = System.currentTimeMillis();
		Object result = null;
		try {
			//执行方法
			result = point.proceed();
		} catch (Throwable e) {
			log.error(Utils.getStackTrace(e));
			//保存日志
			saveSysLog(point, 0L, true);
			return null;
		}
		//执行时长(毫秒)
		long time = System.currentTimeMillis() - beginTime;

		//保存日志
		saveSysLog(point, time,false);

		return result;
	}

	private void saveSysLog(ProceedingJoinPoint joinPoint, long time, Boolean flag) {
		MethodSignature signature = (MethodSignature) joinPoint.getSignature();
		Method method = signature.getMethod();

		SysLogEntity sysLog = new SysLogEntity();
		SysLog syslog = method.getAnnotation(SysLog.class);
		if(syslog != null){
		String operation = syslog.value();
		//对flag进行判断，判断是否是出现异常的情况
			if (flag) {
				operation += " ->请求异常";
			}
			//注解上的描述
			sysLog.setOperation(operation);
		}

		//请求的方法名
		String className = joinPoint.getTarget().getClass().getName();
		String methodName = signature.getName();
		sysLog.setMethod(className + "." + methodName + "()");

		//请求的参数
		Object[] args = joinPoint.getArgs();
		try{
			String params = new Gson().toJson(args[0]);
			sysLog.setParams(params);
		}catch (Exception e){

		}

		//获取request
		HttpServletRequest request = HttpContextUtils.getHttpServletRequest();
		//设置IP地址
		sysLog.setIp(IPUtils.getIpAddr(request));

		//用户名
		String username = "";
		// String username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUserName();
		if ((SecurityUtils.getSubject().getPrincipal()) == null) {
			// String json = new Gson().toJson(args[0]);
			// JSONObject jsonObject = JSONObject.parseObject(json);
			// // JSONArray jsonArray = jsonObject.getJSONArray("username");
			// // String name = jsonArray.getJSONObject(0).get("username").toString();
			// String name = jsonObject.get("username").toString();
			username = "其他用户";
		}else {
			username = ((SysUserEntity) SecurityUtils.getSubject().getPrincipal()).getUserName();
		}
		//判断是否为登录请求，登录请求在shiro中不存在用户信息
		// if (username == null) {
		// 	username = new Gson().toJson(args[0]);
		// }
		sysLog.setUsername(username);

		sysLog.setExecuteTime(time);
		sysLog.setCreateTime(LocalDateTime.now());
		//保存系统日志
		sysLogService.save(sysLog);
	}
}
