package com.suke.czx.newland.dao.mgrdbmapper;

import com.alibaba.fastjson.JSONObject;
import com.suke.czx.newland.dto.DbSystemInfoDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface SystemMapper {


    @Select({"SELECT SYS_CONTEXT('USERENV', 'CURRENT_USER') dbName,SYS_CONTEXT('USERENV', 'HOST') || '\\' || SYS_CONTEXT('USERENV', 'OS_USER') dbMachine,SYS_CONTEXT('USERENV', 'OS_USER') execPerson FROM DUAL"})
    DbSystemInfoDto qryDbSystemInfo();


    @Select("${sqlStr}")
    String executeSql(@Param(value = "sqlStr") String sqlStr);

    @Select("${sqlStr}")
    List<JSONObject> executeSqlForJSONObj(@Param(value = "sqlStr") String sqlStr);
}
