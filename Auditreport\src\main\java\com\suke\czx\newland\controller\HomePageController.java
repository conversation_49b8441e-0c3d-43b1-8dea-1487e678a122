package com.suke.czx.newland.controller;

import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.dto.AuditItemDto;
import com.suke.czx.newland.service.HomePageService;
import com.suke.czx.newland.service.IAuditService;
import com.suke.czx.newland.util.DataSourceUtil;
import com.suke.czx.newland.vo.echartstable.AuditItemCheckLogTableVo;
import com.suke.czx.newland.vo.echartstable.AuditItemTableVo;
import com.suke.czx.newland.vo.echartstable.CheckLogAnalysisVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/homePage")
public class HomePageController {

    private final HomePageService homePageService;

    private final DataSourceUtil dataSourceUtil;

    private final IAuditService iAuditService;

    public HomePageController(HomePageService homePageService, DataSourceUtil dataSourceUtil, IAuditService iAuditService) {
        this.homePageService = homePageService;
        this.dataSourceUtil = dataSourceUtil;
        this.iAuditService = iAuditService;
    }

    @PostMapping("/get7DaysAuditItems")
    public Result get7DaysAuditItems(@RequestParam(required = false) String datasourceId) {
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeToTargetDataSource(datasourceId);
        }
        List<AuditItemDto> createInfo = homePageService.qryAuditItemCreateInfoWithDuration(7);
        List<AuditItemDto> updateInfo = homePageService.qryAuditItemUpdateInfoWithDuration(7);
        List<AuditItemTableVo> dataRes = new ArrayList<>();

        for (AuditItemDto item : createInfo) {
            AuditItemTableVo temp = new AuditItemTableVo();
            temp.setDateTimeStr(item.getDayTimeStr());
            temp.setCreateAuditItemCount(item.getCountVal());
            dataRes.add(temp);
        }

        for (AuditItemDto item : updateInfo) {
            for (AuditItemTableVo itemTableVo : dataRes) {
                if (item.getDayTimeStr().equals(itemTableVo.getDateTimeStr())){
                    itemTableVo.setUpdateAuditItemCount(item.getCountVal());
                }
            }
        }

        List<AuditItemTableVo> res = dataRes.stream()
                .sorted((x, y) -> {
                    int xCreateCont = x.getCreateAuditItemCount();
                    int xUpdateCont = x.getUpdateAuditItemCount();
                    int yUpdateCont = y.getUpdateAuditItemCount();
                    int yCreateCont = y.getCreateAuditItemCount();
                    double xAve = (xCreateCont+xUpdateCont) / 2.0;
                    double yAve = (yUpdateCont+yCreateCont) / 2.0;
                    return xAve > yAve ? 1 : -1;
                })
                .collect(Collectors.toList());
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeDefaultDataSource();
        }
        return new Result().setCode(10000).setData(res.subList(0, Math.min(res.size(), 20)));
    }

    @PostMapping("/todayAuditResult")
    public Result todayAuditResult(@RequestParam(required = false) String datasourceId){
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeToTargetDataSource(datasourceId);
        }
        List<AuditItemCheckLogTableVo> dataRes = homePageService.qryAuditItemCheckLogInfo(LocalDate.now());
        List<AuditItemCheckLogTableVo> res = dataRes.stream()
                .sorted((x, y) -> {
                    int xNormal = x.getNormal();
                    int xUnNormal = x.getUnNormal();
                    int yNormal = y.getNormal();
                    int yUnNormal = y.getUnNormal();
                    double xAve = (xNormal + xUnNormal) / 2.0;
                    double yAve = (yNormal + yUnNormal) / 2.0;
                    return xAve > yAve ? -1 : 1;
                }).collect(Collectors.toList());
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeDefaultDataSource();
        }
        return new Result().setCode(10000).setData(res.subList(0, Math.min(res.size(), 20)));
    }

    /**
     * 首页度量数据汇总
     * @return
     */
    @PostMapping("/homepageMetrics")
    public Result homepageMetrics(@RequestParam(required = false) String datasourceId) {
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeToTargetDataSource(datasourceId);
        }
        Result res = iAuditService.homepageMetrics();
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeDefaultDataSource();
        }
        return res;
    }

    @PostMapping("/checkLogAnalysis")
    public Result checkLogAnalysis(@RequestBody CheckLogAnalysisVo checkLogAnalysisVo, @RequestParam(required = false) String datasourceId) {
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeToTargetDataSource(datasourceId);
        }
        Result res = iAuditService.checkLogAnalysis(checkLogAnalysisVo);
        if (datasourceId != null && datasourceId.length() > 0){
            dataSourceUtil.changeDefaultDataSource();
        }
        return res;
    }
}
