package com.suke.czx.newland.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.newland.bpm.camunda.tasks.service.AuditThread;
import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper;
import com.suke.czx.newland.dao.mgrdbmapper.RptCheckCfgMapper;
import com.suke.czx.newland.dao.mgrdbmapper.RptCheckLogMapper;
import com.suke.czx.newland.dto.AuditLogDownloadRecordDto;
import com.suke.czx.newland.dto.CheckLogAnalysisDto;
import com.suke.czx.newland.dto.RptCheckCfgDataDistributionDto;
import com.suke.czx.newland.po.RptCheckCfgPo;
import com.suke.czx.newland.service.IAuditService;
import com.suke.czx.newland.util.AuditUtils;
import com.suke.czx.newland.util.ThreadPoolUtil;
import com.suke.czx.newland.vo.AuditLogDownloadVo;
import com.suke.czx.newland.vo.EditAuditLogVo;
import com.suke.czx.newland.vo.LogIdsVo;
import com.suke.czx.newland.vo.audit.*;
import com.suke.czx.newland.vo.echartstable.CheckLogAnalysisTableVo;
import com.suke.czx.newland.vo.echartstable.CheckLogAnalysisVo;
import com.suke.czx.newland.vo.echartstable.Series;
import com.suke.czx.schedule.CronTaskMgmt;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AuditServiceImpl implements IAuditService {

    private Logger logger = LoggerFactory.getLogger(AuditServiceImpl.class);

    @Autowired
    private IAuditCfgMapper iAuditCfgMapper;

    @Autowired
    private CronTaskMgmt cronTaskMgmt;

    @Autowired
    private RptCheckLogMapper rptCheckLogMapper;

    @Autowired
    private RptCheckCfgMapper rptCheckCfgMapper;

    @Override
    public Result addAuditType(AuditDictTypeVo auditDictTypeVo) {
        try {
            iAuditCfgMapper.addAuditType(auditDictTypeVo);
        } catch (Exception e) {
            logger.error("[新增字典类型失败][message:{}]", e.getMessage());
            e.printStackTrace();
            return new Result().setCode(10001).setMsg("新增字典类型失败").setData(null);
        }
        return new Result().setCode(10000).setMsg("success");

    }

    @Override
    public Result queryAuditConfig(AuditQueryVo auditConfigVo) {
        List<AuditConfigVo> voList = null;
        try {
            voList = iAuditCfgMapper.queryAuditConfig(auditConfigVo);
        } catch (Exception e) {
            logger.error("[查询REPORT_AUDIT_CONFIG异常][message:{}]", e.getMessage());
            e.printStackTrace();
            return new Result().setCode(10001).setMsg("查询稽核配置异常").setData(null);
        }
        return new Result().setCode(10000).setMsg("success").setData(voList);
    }

    @Override
    public Result queryAuditCode() {
        Map<String, List<AuditDictVo>> map = new HashMap<>();
        try {
            map.put("value", iAuditCfgMapper.queryDictByType(10));
            map.put("center", iAuditCfgMapper.queryDictByType(11));
            map.put("modle", iAuditCfgMapper.queryDictByType(12));
        } catch (Exception e) {
            logger.error("[查询REPORT_AUDIT_CONFIG异常][message:{}]", e.getMessage());
            e.printStackTrace();
            return new Result().setCode(10001).setMsg("查询稽核项异常").setData(null);
        }
        return new Result().setCode(10000).setMsg("success").setData(map);
    }

    @Override
    public Result queryAuditCenter() {
        List<String> voList = null;
        try {

        } catch (Exception e) {
            logger.error("[查询REPORT_AUDIT_CONFIG异常][message:{}]", e.getMessage());
            e.printStackTrace();
            return new Result().setCode(10001).setMsg("查询稽核中心异常").setData(null);
        }
        return new Result().setCode(10000).setMsg("success").setData(voList);
    }

    @Override
    public Result eidtAuditConfig(AuditConfigVo auditConfigVo, boolean isEdit) {
        if (isEdit) {
            try {
                iAuditCfgMapper.bakAuditCfgByRuleId(auditConfigVo.getRuleId());
                iAuditCfgMapper.editAuditConfig(auditConfigVo);
            } catch (Exception e) {
                logger.error("[修改REPORT_AUDIT_CONFIG异常][message:{}]", e.getMessage());
                e.printStackTrace();
                return new Result().setCode(10001).setMsg("修改稽核配置异常").setData(null);
            }
        } else {
            try {
                long count = iAuditCfgMapper.selectCountByXuhao(auditConfigVo.getRuleId());
                auditConfigVo.setRuleId(count + 1);
                iAuditCfgMapper.addAuditConfig(auditConfigVo);
            } catch (Exception e) {
                logger.error("[新增REPORT_AUDIT_CONFIG异常][message:{}]", e.getMessage());
                e.printStackTrace();
                return new Result().setCode(10001).setMsg("新增稽核配置异常").setData(null);
            }
        }

        return new Result().setCode(10000).setMsg("success").setData(null);
    }

    @Override
    public Result delAuditConfig(String ruleId) {
        try {
            iAuditCfgMapper.delAuditConfig(ruleId);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[删除REPORT_AUDIT_CONFIG异常][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("删除稽核配置异常");
        }
        return new Result().setCode(10000).setMsg("success");
    }

    @Override
    public Result execProcess(List<AuditExecVo> cfgIds, String execPerson, String runDatasourceId) {
        if (cfgIds == null || cfgIds.isEmpty()) {
            return new Result().setCode(10001).setMsg("执行的稽核配置为空！");
        }

        for (AuditExecVo execEntity : cfgIds) {
            RptCheckCfgPo cfgPo = new RptCheckCfgPo();
            cfgPo.setRuleId(String.valueOf(execEntity.getId()));
            List<RptCheckCfgPo> res = rptCheckCfgMapper.qryAuditCfgWithCondition(cfgPo);
            if (res == null || res.isEmpty()) {
                return new Result().setCode(10001).setMsg("执行的稽核配置不存在.");
            }
            if (res.stream().anyMatch(e -> "0".equals(e.getStatus()))) {
                return new Result().setCode(10001).setMsg("存在状态为失效的稽核任务，请确认.");
            }
            //设置执行环境
            execEntity.setDatasourceId(res.get(0).getEnvType());
        }
        try {
            if (AuditUtils.getStauts() == -1) {
                AuditUtils.setStauts(1);
                ThreadPoolUtil.getInstance().getProcessPool().execute(new AuditThread(cfgIds, iAuditCfgMapper, execPerson));
            } else {
                System.out.println(AuditUtils.getStauts());
                throw new Exception("正在批量稽核中...");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[创建线程失败][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("启动批量执行失败");
        }
        return new Result().setCode(10000).setMsg("success");
    }

    @Override
    public Result queryDictType() {
        List<AuditQueryDictVo> queryVos = new ArrayList<>();
        try {
            List<AuditDictTypeVo> typeVos = iAuditCfgMapper.queryDictType();
            typeVos.forEach(vo -> {
                AuditQueryDictVo dictVo = new AuditQueryDictVo();
                dictVo.setTypeVo(vo);
                List<AuditDictVo> vos = iAuditCfgMapper.queryDictByType(vo.getDictTypeId());
                List<AuditDictQueryVo> queryVoList = new ArrayList<>();
                for (AuditDictVo auditDictVo : vos) {
                    AuditDictQueryVo query = new AuditDictQueryVo();
                    BeanUtils.copyProperties(auditDictVo, query);
                    int count = iAuditCfgMapper.selectAuditConfigCountByTypeId(query.getDictValue());
                    if (count > 0) {
                        query.setIsUsed(1);
                    } else {
                        query.setIsUsed(0);
                    }
                    queryVoList.add(query);
                }
                dictVo.setDictList(queryVoList);
                queryVos.add(dictVo);
            });
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[查询字典类别失败][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("查询字典类别失败").setData(null);
        }
        return new Result().setCode(10000).setMsg("success").setData(queryVos);
    }


    @Override
    public Result delAuditDictByDictId(Integer dictId) {
        if (dictId == null || dictId < 0) {
            return new Result().setCode(10001).setMsg("错误的删除项");
        }
        try {
            iAuditCfgMapper.delAuditDictByDictId(dictId);
            cronTaskMgmt.refreshTaskByDictId(dictId);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[删除字典表失败][dictId:{}][message:{}]", dictId, e.getMessage());
            return new Result().setCode(10001).setMsg("删除字典表失败");
        }
        return new Result().setCode(10000).setMsg("删除字典表成功");
    }

    @Override
    public Result addAuditDict(AuditDictVo dict) {
        try {
            int maxId = iAuditCfgMapper.selectMaxId();
            int dictValId = iAuditCfgMapper.selectDictValMaxId();
            dict.setDictId(maxId + 1);
            dict.setDictValue(dictValId+1);
            iAuditCfgMapper.addAuditDict(dict);
            cronTaskMgmt.refreshTaskByDictId(dict.getDictId());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[新增字典表失败][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("新增字典表失败");
        }
        return new Result().setCode(10000).setMsg("新增字典表成功");
    }

    @Override
    public Result editAuditDict(AuditDictVo dict) {
        try {
            iAuditCfgMapper.editAuditDict(dict);
            cronTaskMgmt.refreshTaskByDictId(dict.getDictId());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[修改字典表失败][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("修改字典表失败");
        }
        return new Result().setCode(10000).setMsg("修改字典表成功");
    }


    @Override
    public Result queryAuditLogWithPageInfo(AuditLogQryVo auditLogVo, int pageIndex, int pageSize) {
        List<AuditLogVo> logs;
        try {
            //开启分页
            PageHelper.startPage(pageIndex, pageSize);
            logs = iAuditCfgMapper.queryAuditLogByAll(auditLogVo, auditLogVo.getDatasourceId());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[修改字典表失败][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("查询日志异常");
        }
        return new Result().setCode(10000).setMsg("success").setData(new PageInfo<>(logs));
    }

    @Override
    public Result queryExecStatus() {
        Map execMap = new HashMap();
        execMap.put("status", AuditUtils.getStauts());
        return new Result().setCode(10000).setMsg("success").setData(execMap);
    }


    @Override
    public Result queryAuditSql(String ruleSql) {
        Integer count = -1;
        try {
            if (StringUtils.isEmpty(ruleSql) || !ruleSql.contains("select") || !ruleSql.contains("count")) {
                throw new Exception("SQL不正确");
            }
            count = iAuditCfgMapper.execSqlByRuleId(ruleSql);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[验证SQL错误][message:{}]", e.getMessage());
        }
        return new Result().setCode(10000).setData(count);
    }

    @Override
    public Result saveExecRemark(AuditLogVo logVo) {
        int count = -1;
        try {
            count = iAuditCfgMapper.saveExecRemarkByLogId(logVo);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[保存日志备注异常][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("error").setData(count);
        }
        return new Result().setCode(10000).setMsg("success").setData(count);
    }

//    @Override
//    public Result queryHisConfig(long ruleId) {
//        List<AuditConfigVo> configVos = null;
//        try {
//            configVos = iAuditCfgMapper.queryHisConfigByRuleId(ruleId);
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("[保存日志备注异常][message:{}]", e.getMessage());
//            return new Result().setCode(10001).setMsg("error").setData(null);
//        }
//        return new Result().setCode(10000).setMsg("success").setData(configVos);
//    }

    @Override
    public Result queryHisConfig(long ruleId, String modifyPerson, String ruleName) {
        List<AuditConfigVo> configVos = null;
        try {
            configVos = iAuditCfgMapper.queryHisConfigByRuleId(ruleId, modifyPerson, ruleName);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[保存日志备注异常][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("error").setData(null);
        }
        return new Result().setCode(10000).setMsg("success").setData(configVos);
    }

    @Override
    public Result saveAnalysisRes(AuditLogVo logVo) {
        int count = -1;
        try {
            count = iAuditCfgMapper.saveAnalysisResByLogId(logVo);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[变更分析结果][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("error").setData(count);
        }
        return new Result().setCode(10000).setMsg("success").setData(count);
    }


    @Override
    public Result queryJobStatus() {
        try {
            //获取当前时间年月日
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_YEAR, 1);
            Date date = calendar.getTime();

            String tomorrow = sdf.format(date);

            Map<String, Object> map = iAuditCfgMapper.queryJobStatus();

            if (!tomorrow.equals(map.get("NEXT_DATE"))) {
                return new Result().setCode(10001).setMsg("存储过程未启动或者正在执行中");
            } else if (tomorrow.equals(map.get("NEXT_DATE")) && "0".equals(map.get("FAILURES"))) {
                return new Result().setCode(10000).setMsg("存储过程执行完成并且执行成功");
            } else {
                return new Result().setCode(10002).setMsg("存储过程执行完成但是存在执行失败情况");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[查询存储过程状态异常][message:{}]", e.getMessage());
            return new Result().setCode(10001).setMsg("查询存储过程状态异常");
        }
    }

    @Override
    public Result checkLogAnalysis(CheckLogAnalysisVo checkLogAnalysisVo) {
        CheckLogAnalysisTableVo res = new CheckLogAnalysisTableVo();
        //正常 异常 和未分析
        List<CheckLogAnalysisDto> handleData = iAuditCfgMapper.qryAuditDateWithDuration(7);
        Map<String, List<CheckLogAnalysisDto>> modleNameMap = handleData.stream()
                .collect(Collectors.groupingBy(CheckLogAnalysisDto::getModleName));
        List<String> fieldsList = res.getDimensions();
        List<Series> series = res.getSource();
        List<String> dimensions = Arrays.stream(Series.class.getDeclaredFields())
                .map(Field::getName)
                .collect(Collectors.toList());
        fieldsList.addAll(dimensions);
        modleNameMap.forEach((key, val) -> {
            if (key == null || key.isEmpty()) {
                return;
            }
            Series temp = new Series();
            temp.setProduct(key);
            for (CheckLogAnalysisDto analysisDto : val) {
                if (analysisDto.getAnalysisRes() == null) {
                    temp.setNotAnalyzed(temp.getNotAnalyzed() + analysisDto.getCountVal());
                } else if ("有问题".equals(analysisDto.getAnalysisRes())) {
                    temp.setNormal(temp.getNormal() + analysisDto.getCountVal());
                } else if ("无问题".equals(analysisDto.getAnalysisRes())) {
                    temp.setUnNormal(temp.getUnNormal() + analysisDto.getCountVal());
                } else if ("待分析".equals(analysisDto.getAnalysisRes())) {
                    temp.setNotAnalyzed(temp.getNotAnalyzed() + analysisDto.getCountVal());
                } else {
                    temp.setNotAnalyzed(temp.getNotAnalyzed() + analysisDto.getCountVal());
                }
            }
            series.add(temp);
        });
        List<Series> sortedSeries = series.stream()
                .sorted((x, y) -> {
                    int xNormal = x.getNormal();
                    int xUnNormal = x.getUnNormal();
                    int xNotAnalyzed = x.getNotAnalyzed();
                    int yNormal = y.getNormal();
                    int yUnNormal = y.getUnNormal();
                    int yNotAnalyzed = y.getNotAnalyzed();
                    double xAve = (xNormal + xUnNormal + xNotAnalyzed) / 3.0;
                    double yAve = (yNormal + yUnNormal + yNotAnalyzed) / 3.0;
                    return xAve > yAve ? -1 : 1;
                })
                .collect(Collectors.toList());
        res.setSource(sortedSeries.subList(0, Math.min(sortedSeries.size(), 20)));
        return new Result().setCode(10000).setData(res);
    }


    @Override
    public Result homepageMetrics() {
        Map<String, Object> resultMap = new HashMap<>();
        try {
            //稽核规则分布
            List<RptCheckCfgDataDistributionDto> res = iAuditCfgMapper.queryDataDistribution();
            resultMap.put("distributionAuditRules", res);
            logger.info("res : {}", res);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("[数据汇总异常][message:{}]", e.getMessage());
        }
        return new Result().setCode(10000).setData(resultMap);
    }

    @Override
    public Result queryAuditConfigWithRuleIds(String[] ruleIds) {
        if (ruleIds.length == 0) {
            return new Result().setCode(10000);
        }
        List<AuditConfigVo> data = iAuditCfgMapper.qryAuditCfgWithRuleIds(ruleIds);
        return new Result().setCode(10000).setData(data);
    }


    @Override
    public Result queryAuditLogWithLogIds(String[] logIds) {
        if (logIds.length == 0) {
            return new Result().setCode(10000);
        }
        List<AuditLogVo> data = rptCheckLogMapper.qryRptCheckLogWithLogIds(logIds);
        return new Result().setCode(10000).setData(data);
    }

    @Override
    public Result editAuditLogAnalysisRes(EditAuditLogVo param) {
        Objects.requireNonNull(param, "param 不可为空");
        Objects.requireNonNull(param.getLogId(), "logId 不可为空");
        Objects.requireNonNull(param.getAnalysisRes(), "analysisRes 不可为空");
        rptCheckLogMapper.updateRptCheckLogAnalysisResWithLogId(param.getAnalysisRes(), param.getLogId());
        return new Result().setCode(10000);
    }

    @Override
    public List<AuditLogDownloadRecordDto> downloadAuditLogs(AuditLogDownloadVo param) {
        return rptCheckLogMapper.qryAuditLogWithLogIds(param);
    }
}
