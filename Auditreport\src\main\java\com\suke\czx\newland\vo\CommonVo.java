package com.suke.czx.newland.vo;


/**
 * 文件名称：CommonVo
 * 描    述：出账自动化_通用配置管理实体类
 * 作    者：jianggy
 * 创建日期：2020-06-18
 * 创建时间：11:30
 * 当前版本：1.0
 */
public class CommonVo
{
    private short runStatus;    // 1 运行（蓝色） 2 干预完成（亮绿色） 3 告警（黄色） 4 用户确认（紫色）
    private short dealStatus;   // 1 成功（绿色） 2 失败（红色）
    private boolean isBeenDeal; // 是否已经接收人工处理 或 节点跳转，如果是，则不执行finally

    private SmFlwTaskVo smFlwTaskVo;            // 任务管理表sm_flw_task实体类 执行任务
    private SmFlwTaskVo smFlwChkTaskVo;         // 任务管理表sm_flw_task实体类 检查任务
    private SmFlwTaskVo smFlwPickTaskVo;         // 任务管理表sm_flw_task实体类 采集任务
    private SmFlwResourceVo smFlwResourceVo;    // 资源配置表sm_flw_resource实体类 执行资源
    private SmFlwResourceVo smFlwChkResourceVo; // 资源配置表sm_flw_resource实体类 检查资源
    private SmFlwResourceVo smFlwFromResourceVo; // 资源配置表sm_flw_resource实体类 采集资源
    private SmFlwResourceVo smFlwSaveResourceVo; // 资源配置表sm_flw_resource实体类 采集资源

    // 归属于节点的属性（BPMN图内含有的信息）
    private String taskId;               // 任务Id
    private String taskName;             // 任务名称
    private String resId;                // 资源Id
    private short isTimeout;             // 是否需要超时预警 1、超时预警 2、不做超时预警
    private short errorType;         // 出错后，处理类型 1、回退重试 2、人工干预 3、忽略
    private long intervalFirst;          // 首次间隔
    private long intervalTime;           // 间隔时间
    private long maxWaitTime;            // 最大等待时间
    private long dazeTime;               // 进度静止告警时间
    private long checkInterventionTime;  // 人工干预检查时间（用于检测是否干预完成）
    private String colTaskId;            // 进程进度采集任务ID
    private String colResId;             // 进程进度采集任务资源
    private String colTaskName;          // 进程进度采集任务名称
    private String pickTaskId;          // 进程进度采集任务ID
    private String pickTaskName;          // 进程进度采集任务名称
    private String fromResId;          // 进程进度采集任务资源
    private String saveResId;          // 进程进度采集任务资源
    private String timingRule;         // 定时规则

    // 作业属性
    private short cityId;                // 归属地市ID
    private String cityName;             // 归属地市名称
    private String jobId;                // 出账自动化作业id
    private int flwType;                 // 作业类型
    private String isSpecifyStart;       // 是否指定节点启动
    private long currentDayZeroTime;     // 当前天零点毫秒数

    // 流程与节点的自带属性
    private String processInstanceId;    // 流程实例Id
    private String processDefinitionId;  // 流程定义Id
    private String nodeId;               // 当前节点Id
    private String processName;          // 流程名称

    public long getCurrentDayZeroTime()
    {
        return currentDayZeroTime;
    }

    public void setCurrentDayZeroTime(long currentDayZeroTime)
    {
        this.currentDayZeroTime = currentDayZeroTime;
    }

    public String getIsSpecifyStart()
    {
        return isSpecifyStart;
    }

    public void setIsSpecifyStart(String isSpecifyStart)
    {
        this.isSpecifyStart = isSpecifyStart;
    }

    public short getRunStatus()
    {
        return runStatus;
    }

    public void setRunStatus(short runStatus)
    {
        this.runStatus = runStatus;
    }

    public short getDealStatus()
    {
        return dealStatus;
    }

    public void setDealStatus(short dealStatus)
    {
        this.dealStatus = dealStatus;
    }

    public boolean isBeenDeal()
    {
        return isBeenDeal;
    }

    public void setBeenDeal(boolean beenDeal)
    {
        isBeenDeal = beenDeal;
    }

    public String getJobId()
    {
        return jobId;
    }

    public void setJobId(String jobId)
    {
        this.jobId = jobId;
    }

    public String getCityName()
    {
        return cityName;
    }

    public void setCityName(String cityName)
    {
        this.cityName = cityName;
    }

    public short getCityId()
    {
        return cityId;
    }

    public void setCityId(short cityId)
    {
        this.cityId = cityId;
    }

    public String getProcessInstanceId()
    {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId)
    {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessDefinitionId()
    {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId)
    {
        this.processDefinitionId = processDefinitionId;
    }

    public String getNodeId()
    {
        return nodeId;
    }

    public void setNodeId(String nodeId)
    {
        this.nodeId = nodeId;
    }

    public String getProcessName()
    {
        return processName;
    }

    public void setProcessName(String processName)
    {
        this.processName = processName;
    }

    public String getColTaskId()
    {
        return colTaskId;
    }

    public void setColTaskId(String colTaskId)
    {
        this.colTaskId = colTaskId;
    }

    public String getColResId()
    {
        return colResId;
    }

    public void setColResId(String colResId)
    {
        this.colResId = colResId;
    }

    public String getColTaskName()
    {
        return colTaskName;
    }

    public void setColTaskName(String colTaskName)
    {
        this.colTaskName = colTaskName;
    }

    public SmFlwTaskVo getSmFlwTaskVo()
    {
        return smFlwTaskVo;
    }

    public void setSmFlwTaskVo(SmFlwTaskVo smFlwTaskVo)
    {
        this.smFlwTaskVo = smFlwTaskVo;
    }

    public SmFlwResourceVo getSmFlwResourceVo()
    {
        return smFlwResourceVo;
    }

    public void setSmFlwResourceVo(SmFlwResourceVo smFlwResourceVo)
    {
        this.smFlwResourceVo = smFlwResourceVo;
    }

    public String getTaskId()
    {
        return taskId;
    }

    public void setTaskId(String taskId)
    {
        this.taskId = taskId;
    }

    public String getTaskName()
    {
        return taskName;
    }

    public void setTaskName(String taskName)
    {
        this.taskName = taskName;
    }

    public String getResId()
    {
        return resId;
    }

    public void setResId(String resId)
    {
        this.resId = resId;
    }

    public short getIsTimeout()
    {
        return isTimeout;
    }

    public void setIsTimeout(short isTimeout)
    {
        this.isTimeout = isTimeout;
    }

    public short getErrorType()
    {
        return errorType;
    }

    public void setErrorType(short errorType)
    {
        if (errorType == 0 || errorType == -1)
        {
            this.errorType = 2; // 人工干预
        }
        else
        {
            this.errorType = errorType;
        }
    }

    public long getIntervalFirst()
    {
        return intervalFirst;
    }

    public void setIntervalFirst(long intervalFirst)
    {
        if (intervalFirst == 0 || intervalFirst == -1)
        {
            this.intervalFirst = 30; // 单位秒
        }
        else
        {
            this.intervalFirst = intervalFirst;
        }
    }

    public long getIntervalTime()
    {
        return intervalTime;
    }

    public void setIntervalTime(long intervalTime)
    {
        if (intervalTime == 0 || intervalTime == -1)
        {
            this.intervalTime = 30; // 单位秒
        }
        else
        {
            this.intervalTime = intervalTime;
        }
    }

    public long getMaxWaitTime()
    {
        return maxWaitTime;
    }

    public void setMaxWaitTime(long maxWaitTime)
    {
        if (maxWaitTime == 0 || maxWaitTime == -1)
        {
            this.maxWaitTime = 60; // 单位秒
        }
        else
        {
            this.maxWaitTime = maxWaitTime;
        }
    }

    public long getDazeTime()
    {
        return dazeTime;
    }

    public void setDazeTime(long dazeTime)
    {
        this.dazeTime = dazeTime;
    }

    public long getCheckInterventionTime()
    {
        return checkInterventionTime;
    }

    public void setCheckInterventionTime(long checkInterventionTime)
    {
        if (checkInterventionTime == 0 || checkInterventionTime == -1)
        {
            this.checkInterventionTime = 60; // 单位秒
        }
        else
        {
            this.checkInterventionTime = checkInterventionTime;
        }
    }

    public SmFlwTaskVo getSmFlwChkTaskVo()
    {
        return smFlwChkTaskVo;
    }

    public void setSmFlwChkTaskVo(SmFlwTaskVo smFlwChkTaskVo)
    {
        this.smFlwChkTaskVo = smFlwChkTaskVo;
    }

    public SmFlwResourceVo getSmFlwChkResourceVo()
    {
        return smFlwChkResourceVo;
    }

    public void setSmFlwChkResourceVo(SmFlwResourceVo smFlwChkResourceVo)
    {
        this.smFlwChkResourceVo = smFlwChkResourceVo;
    }

    public int getFlwType() {
        return flwType;
    }

    public void setFlwType(int flwType) {
        this.flwType = flwType;
    }

    public SmFlwTaskVo getSmFlwPickTaskVo() {
        return smFlwPickTaskVo;
    }

    public void setSmFlwPickTaskVo(SmFlwTaskVo smFlwPickTaskVo) {
        this.smFlwPickTaskVo = smFlwPickTaskVo;
    }

    public SmFlwResourceVo getSmFlwFromResourceVo() {
        return smFlwFromResourceVo;
    }

    public void setSmFlwFromResourceVo(SmFlwResourceVo smFlwFromResourceVo) {
        this.smFlwFromResourceVo = smFlwFromResourceVo;
    }

    public SmFlwResourceVo getSmFlwSaveResourceVo() {
        return smFlwSaveResourceVo;
    }

    public void setSmFlwSaveResourceVo(SmFlwResourceVo smFlwSaveResourceVo) {
        this.smFlwSaveResourceVo = smFlwSaveResourceVo;
    }

    public String getPickTaskId() {
        return pickTaskId;
    }

    public void setPickTaskId(String pickTaskId) {
        this.pickTaskId = pickTaskId;
    }

    public String getPickTaskName() {
        return pickTaskName;
    }

    public void setPickTaskName(String pickTaskName) {
        this.pickTaskName = pickTaskName;
    }

    public String getFromResId() {
        return fromResId;
    }

    public void setFromResId(String fromResId) {
        this.fromResId = fromResId;
    }

    public String getSaveResId() {
        return saveResId;
    }

    public void setSaveResId(String saveResId) {
        this.saveResId = saveResId;
    }

    public String getTimingRule() {
        return timingRule;
    }

    public void setTimingRule(String timingRule) {
        this.timingRule = timingRule;
    }

    @Override
    public String toString() {
        return "CommonVo{" +
                "runStatus=" + runStatus +
                ", dealStatus=" + dealStatus +
                ", isBeenDeal=" + isBeenDeal +
                ", smFlwTaskVo=" + smFlwTaskVo +
                ", smFlwChkTaskVo=" + smFlwChkTaskVo +
                ", smFlwResourceVo=" + smFlwResourceVo +
                ", smFlwChkResourceVo=" + smFlwChkResourceVo +
                ", taskId='" + taskId + '\'' +
                ", taskName='" + taskName + '\'' +
                ", resId='" + resId + '\'' +
                ", isTimeout=" + isTimeout +
                ", errorType=" + errorType +
                ", intervalFirst=" + intervalFirst +
                ", intervalTime=" + intervalTime +
                ", maxWaitTime=" + maxWaitTime +
                ", dazeTime=" + dazeTime +
                ", checkInterventionTime=" + checkInterventionTime +
                ", colTaskId='" + colTaskId + '\'' +
                ", colResId='" + colResId + '\'' +
                ", colTaskName='" + colTaskName + '\'' +
                ", cityId=" + cityId +
                ", cityName='" + cityName + '\'' +
                ", jobId='" + jobId + '\'' +
                ", flwType=" + flwType +
                ", isSpecifyStart='" + isSpecifyStart + '\'' +
                ", currentDayZeroTime=" + currentDayZeroTime +
                ", processInstanceId='" + processInstanceId + '\'' +
                ", processDefinitionId='" + processDefinitionId + '\'' +
                ", nodeId='" + nodeId + '\'' +
                ", processName='" + processName + '\'' +
                '}';
    }
}
