package com.suke.czx.newland.vo.audit;

import java.util.Date;

public class AuditDictTypeVo {

    private int dictTypeId;
    private String dictTypeName;
    private String dictTypeDesc;
    private Date createDate;
    private String createPerson;
    private Date modifyDate;
    private String modifyPerson;

    public int getDictTypeId() {
        return dictTypeId;
    }

    public void setDictTypeId(int dictTypeId) {
        this.dictTypeId = dictTypeId;
    }

    public String getDictTypeName() {
        return dictTypeName;
    }

    public void setDictTypeName(String dictTypeName) {
        this.dictTypeName = dictTypeName;
    }

    public String getDictTypeDesc() {
        return dictTypeDesc;
    }

    public void setDictTypeDesc(String dictTypeDesc) {
        this.dictTypeDesc = dictTypeDesc;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreatePerson() {
        return createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifyPerson() {
        return modifyPerson;
    }

    public void setModifyPerson(String modifyPerson) {
        this.modifyPerson = modifyPerson;
    }

    @Override
    public String toString() {
        return "AuditDictTypeVo{" +
                "dictTypeId=" + dictTypeId +
                ", dictTypeName='" + dictTypeName + '\'' +
                ", dictTypeDesc='" + dictTypeDesc + '\'' +
                ", createDate=" + createDate +
                ", createPerson='" + createPerson + '\'' +
                ", modifyDate=" + modifyDate +
                ", modifyPerson='" + modifyPerson + '\'' +
                '}';
    }
}
