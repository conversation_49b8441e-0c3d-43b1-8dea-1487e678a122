package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetLessThanDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.math.BigDecimal;
import java.util.Map;

public class LessThan<PERSON>and<PERSON> extends ExcelHandleRuler{


    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetLessThanDto condition = (SheetLessThanDto) param;
        BigDecimal targetVal = condition.getTargetVal();
        int lessThanColumn = condition.getColumn();
        Sheet sheet = condition.getSheet();
        boolean res = true;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);

            Cell oneRowTargetCell = oneRow.getCell(lessThanColumn);
            if (!isCellNumeric(oneRowTargetCell)){
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }

            setCellStr(oneRowTargetCell);
            String oneRowNumVal = oneRowTargetCell.getStringCellValue();

            if (targetVal.compareTo(new BigDecimal(oneRowNumVal)) < 0 ){
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }
        }
        return res ? success() : fail();
    }
}
