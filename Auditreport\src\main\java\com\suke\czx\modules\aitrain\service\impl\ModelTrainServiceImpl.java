package com.suke.czx.modules.aitrain.service.impl;

import com.suke.czx.modules.aitrain.entity.ModelTrain;
import com.suke.czx.modules.aitrain.mapper.ModelTrainMapper;
import com.suke.czx.modules.aitrain.service.ModelTrainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ModelTrainServiceImpl implements ModelTrainService {

    @Resource
    private ModelTrainMapper modelTrainMapper;
    /**
     * 获取全部的训练数据信息
     *
     * @return
     */
    @Override
    public List<ModelTrain> getAllModelTrain() {
        return modelTrainMapper.getAll();
    }

    /**
     * 根据训练名称查询训练数据信息
     *
     * @param trainName
     * @return
     */
    @Override
    public List<ModelTrain> queryByTrainName(String trainName) {
        return modelTrainMapper.queryByTrainName(trainName);
    }

    /**
     * 查询全部训练名称
     *
     * @return
     */
    @Override
    public List<String> queryAllTrainName() {
        return modelTrainMapper.queryAllTrainName();
    }

    /**
     * 获取模型数量
     *
     * @return
     */
    @Override
    public int getModelSum() {
        return modelTrainMapper.getModelSum();
    }

    /**
     * 根据训练名称获取模型和得分
     *
     * @param trainName
     * @return
     */
    @Override
    public Map<String, Object> getModelAndScoreTest(String trainName) {
        Map<String,Object> map  = new HashMap<>();
        List<String> modelList = modelTrainMapper.getModelByTrainName(trainName);
        List<Double> scoreTestList = modelTrainMapper.getScoreTestByTrainName(trainName);
        List<Double> fitTimeList = modelTrainMapper.getFitTimeByTrainName(trainName);
        map.put("key",modelList);
        map.put("value1",scoreTestList);
        map.put("value2",fitTimeList);
        return map;
    }
}
