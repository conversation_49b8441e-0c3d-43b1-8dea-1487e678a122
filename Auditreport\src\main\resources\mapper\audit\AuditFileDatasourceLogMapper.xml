<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.AuditFileDatasourceLogMapper">
    <insert id="addBatchAuditFileDatasourceLog">
        insert into AUDIT_FILE_DATASOURCE_LOG (LOG_ID,FILE_ID,RULE_ID,AUDIT_SQL_CLOB,STATUS,CREATE_TIME,CREATE_BY,COST_TIME,DELETED,RUN_DATASOURCE_ID) values
        <foreach collection="list" item="item" index="index" separator=",">
            (audit_file_datasource_log_seq.NEXTVAL, #{item.fileId}, #{item.ruleId},
            #{item.auditSqlClob},#{item.status},sysdate,#{item.createBy}, #{item.costTime},'0',#{item.runDatasourceId})
        </foreach>
    </insert>
    <delete id="delBatchAuditFileDatasourceLog">
        update AUDIT_FILE_DATASOURCE_LOG set DELETED = '1'
        where LOG_ID in
        <foreach collection="list" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <select id="qryAuditFileDatasourceLogByCondition"
            resultType="com.suke.czx.newland.dto.AuditFileDatasourceLogResDto">
        select log.LOG_ID,
        log.RULE_ID,
        rule.RULE_NAME,
        log.FILE_ID,
        log.RUN_DATASOURCE_ID datasourceId,
        databasedef.DATA_SOURCE_NAME,
        upFile.FILE_NAME,
        upFile.FILE_SIZE,
        log.COST_TIME,
        log.STATUS,
        log.CREATE_TIME,
        log.CREATE_BY
        from AUDIT_FILE_DATASOURCE_LOG log
        inner join AUDIT_FILE_DATASOURCE_RULE rule on rule.ID = log.RULE_ID
        inner join AUDIT_UPLOAD_FILE upFile on upFile.ID = log.FILE_ID
        inner join DATASOURCE_DEF databasedef on databasedef.DATA_SOURCE_ID = log.RUN_DATASOURCE_ID
        where 1 = 1 and log.DELETED = '0'
        <if test="searchKey != null and searchKey != ''">
            and (rule.RULE_NAME like concat(concat('%',#{searchKey}),'%')
            or upFile.FILE_NAME like concat(concat('%',#{searchKey}),'%')
            or log.CREATE_BY like concat(concat('%',#{searchKey}),'%'))
        </if>
        <if test="status != null and status.size > 0">
            and log.STATUS in
            <foreach collection="status" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        order by CREATE_TIME DESC
    </select>
</mapper>