package com.suke.czx.datasources;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.util.Utils;
import com.suke.czx.common.utils.SpringContextUtils;
import com.suke.czx.newland.util.DataSourceUtil;
import org.apache.ibatis.mapping.Environment;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.lang.reflect.Field;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * 动态数据源
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017/8/19 1:03
 */
public class DynamicDataSource extends AbstractRoutingDataSource {
    private static final ThreadLocal<String> contextHolder = new ThreadLocal<>();

    // 把已有的数据源封装在Map里
    private Map<Object, Object> dynamicTargetDataSources = new HashMap<>();


    public DynamicDataSource(DataSource defaultTargetDataSource, Map<String, DataSource> targetDataSources) {
        super.setDefaultTargetDataSource(defaultTargetDataSource);
        super.setTargetDataSources(new HashMap<>(targetDataSources));
        super.afterPropertiesSet();
    }

    @Override
    protected Object determineCurrentLookupKey() {
        return getDataSource();
    }

    public static void setDataSource(String dataSource) {
        contextHolder.set(dataSource);
    }

    public static String getDataSource() {
        return contextHolder.get();
    }

    public static void clearDataSource() {
        contextHolder.remove();
    }

    public boolean changeDataSource(GenConfig genConfig) {
        String url = genConfig.url;
        String driverClassName = genConfig.driverClassName;
        String username = genConfig.username;
        String password = genConfig.password;
        String dbtype = genConfig.dbtype;
        String dbName;
        if ("oracle".equals(dbtype)) {
            dbName = url.substring(url.lastIndexOf(":") + 1);
        } else {
            dbName = url.substring(url.lastIndexOf("/") + 1);
        }
        // 测试连接
        this.testConnection(driverClassName, url, username, password);

        // 通过Druid数据库连接池连接数据库
        DruidDataSource dataSource = new DruidDataSource();
        //接收前端传递的参数并且注入进去
        dataSource.setName(dbName);
        dataSource.setUrl(url);
        dataSource.setUsername(username);
        dataSource.setPassword(password);
        dataSource.setDriverClassName(driverClassName);
        // 设置最大连接等待时间
        dataSource.setMaxWait(4000);

        // 数据源初始化
        try {
            dataSource.init();
        } catch (SQLException e) {
            // 创建失败则抛出异常
            throw new RuntimeException(e);
        }
        //获取当前数据源的键值对存入Map
        this.dynamicTargetDataSources.put(dbtype, dataSource);
        // 设置数据源
        this.setTargetDataSources(this.dynamicTargetDataSources);
        // 解析数据源
        super.afterPropertiesSet();
        // 切换数据源
        setDataSource(dbtype);
        /*
         ** 修改mybatis的数据源
         */
        SqlSessionFactory sqlSessionFactory = SpringContextUtils.getBean(SqlSessionFactory.class);
        Configuration configuration = sqlSessionFactory.getConfiguration();
        /*
         * ！！！重要，设置databaseId,用于在mapper.xml中找到对应的_databaseId,
         * 此处不设置databaseId，即使数据源切换了，但是在mapper.xml中还是对应不上
         */
        configuration.setDatabaseId(dbtype);
        Environment environment = configuration.getEnvironment();
        Field dataSourceField = null;
        try {
            dataSourceField = environment.getClass().getDeclaredField("dataSource");
            //跳过检验
            dataSourceField.setAccessible(true);
            //修改mybatis的数据源
            dataSourceField.set(environment, dataSource);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        //修改完成后所有线程使用此数据源
        return true;
    }

    // 测试数据源连接的方法
    public void testConnection(String driveClass, String url, String username, String password) {
        try {
            Class.forName(driveClass);
            DriverManager.getConnection(url, username, password);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}
