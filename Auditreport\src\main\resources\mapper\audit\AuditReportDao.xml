<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.audit.dao.AuditReportDao">
    <select id="queryObject" resultType="com.suke.czx.modules.audit.entity.AuditReport">
        select * from audit_report where id = #{value}
    </select>

    <select id="queryList" resultType="com.suke.czx.modules.audit.entity.AuditReport">
        select * from audit_report
        <where>
            <if test="name != null and name.trim() != ''">
                and name like concat(concat('%',#{name}),'%')
            </if>
            <if test="type != null and type.trim() != ''">
                and type = #{type}
            </if>
        </where>
        order by id desc
        <if test="offset != null and limit != null">
            offset #{offset} rows fetch next #{limit} rows only
        </if>
    </select>

    <select id="queryTotal" resultType="int">
        select count(*) from audit_report
        <where>
            <if test="name != null and name.trim() != ''">
                and name like concat('%',#{name},'%')
            </if>
        </where>
    </select>
    <select id="queryReportsById" resultType="com.suke.czx.modules.audit.entity.AuditReport">
        select * from audit_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <insert id="save" parameterType="list" useGeneratedKeys="false">
        insert into audit_report
        (
            id,
            name,
            report_size,
            type,
            suffix,
            url,
            upload_user_id
        )
        select AUDIT_REPORT_SEQ.NEXTVAL,a.* from (
            <foreach collection="list" item="report" separator="union">
                        select #{report.name},
                               #{report.reportSize},
                               #{report.type},
                               #{report.suffix},
                               #{report.url},
                               #{report.uploadUserId}
                        from dual
            </foreach>
            ) a
    </insert>

    <update id="update" parameterType="com.suke.czx.modules.audit.entity.AuditReport">
        update audit_report
        <set>
            <if test="name != null">name = #{name}, </if>
            <if test="suffix != null">suffix = #{suffix}, </if>
        </set>
        where id = #{userId}
    </update>
    <update id="updateById">
        update audit_report set type = #{reportType} where id = #{reportId}
    </update>


    <delete id="deleteBatch">
        delete from audit_report where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>