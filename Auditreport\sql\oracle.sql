-- Create table
create table accounting.sys_menu
(
  menu_id            number(10) not null,
  parent_id          number(10),
  name               <PERSON><PERSON><PERSON><PERSON><PERSON>(64),
  url                VARCHAR2(64),
  perms              VARCHAR2(255),
  type               VA<PERSON>HAR2(64),
  icon               VARCHAR2(64),
  order_num          number(10)
)
tablespace D_MM_DATA_01number
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  

create table accounting.sys_user
(
  user_id            number(10) not null,
  username           VA<PERSON>HAR2(64),
  password           VARCHAR2(64),
  salt               VARCHAR2(64),
  email              VARCHAR2(64),
  mobile             VARCHAR2(64),
  status             number(10),
  create_user_id     number(10),
  create_time        DATE
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
  create table accounting.sys_user_token
(
  user_id           number(10) not null,
  token             VARCHAR2(64),
  expire_time       DATE,
  update_time       DATE
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
    create table accounting.sys_role
(
  role_id           number(10) not null,
  role_name         VARCHAR2(64),
  remark            VARCHAR2(64),
  create_user_id    number(10),
  create_time       DATE
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
      create table accounting.sys_user_role
(
  id           number(10) not null,
  user_id      number(10),
  role_id      number(10)
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );


   create table accounting.sys_role_menu
(
  id           number(10) not null,
  menu_id      number(10),
  role_id      number(10)
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
     create table accounting.sys_config
(
  id          number(10) not null,
  key         VARCHAR2(64),
  value       VARCHAR2(64),
  status      number(10),
  remark      VARCHAR2(64)
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
    create table accounting.sys_log
(
  id          number(20) not null,
  username    VARCHAR2(64),
  operation   VARCHAR2(64),
  method      VARCHAR2(64),
  params      VARCHAR2(64),
  time        number(20),
  ip          VARCHAR2(64),
  create_date date
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
      create table accounting.schedule_job
(
  job_id         number(20) not null,
  bean_name      VARCHAR2(64),
  method_name    VARCHAR2(64),
  params         VARCHAR2(64),
  cron_expression VARCHAR2(64),
  status          number(20),
  remark          VARCHAR2(64),
  create_time  date
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );
  
  
   
      create table accounting.schedule_job_log
(

  job_id         number(20) not null,
  log_id         number(20),
  bean_name      VARCHAR2(64),
  method_name    VARCHAR2(64),
  params         VARCHAR2(64),
  cron_expression VARCHAR2(64),
  status          number(20),
  error           number(20),
  times          VARCHAR2(64),
  create_time  date
)
tablespace D_MM_DATA_01
  pctfree 10
  initrans 3
  maxtrans 255
  storage
  (
    initial 80K
    next 1M
    minextents 1
    maxextents unlimited
  );


-- 初始数据 
INSERT INTO accounting.sys_user (user_id, username, password, salt, email, mobile, status, create_user_id, create_time) VALUES ('1', 'admin', '9ec9750e709431dad22365cabc5c625482e574c74adaebba7dd02f1129e4ce1d', 'YzcmCZNvbXocrsz9dm8e', '<EMAIL>', '***********', '1', '1', sysdate;
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('1', '0', '系统管理', NULL, NULL, '0', 'fa fa-cog', '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('2', '1', '管理员列表', 'modules/sys/user.html', NULL, '1', 'fa fa-user', '1');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('3', '1', '角色管理', 'modules/sys/role.html', NULL, '1', 'fa fa-user-secret', '2');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('4', '1', '菜单管理', 'modules/sys/menu.html', NULL, '1', 'fa fa-th-list', '3');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('5', '1', 'SQL监控', 'druid/sql.html', NULL, '1', 'fa fa-bug', '4');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('15', '2', '查看', NULL, 'sys:user:list,sys:user:info', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('16', '2', '新增', NULL, 'sys:user:save,sys:role:select', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('17', '2', '修改', NULL, 'sys:user:update,sys:role:select', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('18', '2', '删除', NULL, 'sys:user:delete', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('19', '3', '查看', NULL, 'sys:role:list,sys:role:info', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('20', '3', '新增', NULL, 'sys:role:save,sys:menu:list', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('21', '3', '修改', NULL, 'sys:role:update,sys:menu:list', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('22', '3', '删除', NULL, 'sys:role:delete', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('23', '4', '查看', NULL, 'sys:menu:list,sys:menu:info', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('24', '4', '新增', NULL, 'sys:menu:save,sys:menu:select', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('25', '4', '修改', NULL, 'sys:menu:update,sys:menu:select', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('26', '4', '删除', NULL, 'sys:menu:delete', '2', NULL, '0');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('27', '1', '参数管理', 'modules/sys/config.html', 'sys:config:list,sys:config:info,sys:config:save,sys:config:update,sys:config:delete', '1', 'fa fa-sun-o', '6');
INSERT INTO accounting.sys_menu (menu_id, parent_id, name, url, perms, type, icon, order_num) VALUES ('29', '1', '系统日志', 'modules/sys/log.html', 'sys:log:list', '1', 'fa fa-file-text-o', '7');



