spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName:  oracle.jdbc.driver.OracleDriver
        dbType: oracle
        druid:
            first:  #数据源1
#              #                非云库
                url: **********************************************
                username: <PERSON><PERSON>(3IAhPnpsgzdLV0PE34CuI+erjDhtcSLt)
                password: <PERSON><PERSON>(bAYfwxvn9lD650883P9jT4aphuKC3n87)
            second:  #数据源2
                #                非云库
                url: **********************************************
                username: <PERSON><PERSON>(3IAhPnpsgzdLV0PE34CuI+erjDhtcSLt)
                password: E<PERSON>(bAYfwxvn9lD650883P9jT4aphuKC3n87)
            initial-size: 10
            max-active: 100
            min-idle: 10
            max-wait: 60000
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
            time-between-eviction-runs-millis: 60000
            min-evictable-idle-time-millis: 300000
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            stat-view-servlet:
                enabled: true
                url-pattern: /druid/*
                #login-username: admin
                #login-password: admin
            filter:
                stat:
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: false
                wall:
                    config:
                        multi-statement-allow: true

# 文件上传的服务器信息配置
# 预出账开发
ftp:
    host: *************
    port: 22
    user: ENC(CfVFjXaRG8IVsjp2sFpEtENaY6jVHjbZ)
    password: ENC(rW2cpOSGt68YFY5PFfi3Cd4Yb0ueHzlC)
    basePath: /home/<USER>/audit_report_tools/report/
    comPath: /home/<USER>/audit_report_tools/comparison
    rulePath: /home/<USER>/audit_report_tools/rule
    aiPath: /home/<USER>/audit_report_tools/ai
    pythonbasePath: /home/<USER>/audit_report_tools/Audit_AI/pythonProject1
    #    输出文件的位置
    logPath: /home/<USER>/audit_report_tools/
    logName: nohup.out
    containerName: audit_project
upload:
    path: ./upload/
