package com.suke.czx.newland.vo.audit;

import java.util.Date;

public class AuditLogVo {

    private long    logId;             //日志编号
    private String  dbName;            //执行DB用户名
    private String  dbMachine;         //执行主目路径
    private long    ruleId;            //规则编号
    private String  modifyPerson;      //规则最近修改人
    private Date    modifyDate;        //规则最近修改时间
    private String  chkTypeId;        //规则项编号
    private String  chkTypeName;      //规则项名称
    private String  centerId;          //业务中心编号
    private String  centerName;        //业务中心名称
    private String  modleId;           //业务模块编号
    private String  modleName;        //业务模块名
    private String  ruleName;          //规则名称
    private String  ruleSql;           //规则SQL
    private String  limitValue;        //规则阈值
    private String  sqlValue;          //规则SQL值
    private short   status;             //状态
    private String  conclusion;         //执行结果
    private long    execMsec;          //执行时间（毫秒）
    private String  execRemark;        //执行备注
    private Date    createDate;        //日志执行时间
    private String  createPerson;      //日志执行人
    private Date    execDate;
    private String  execPerson;

    // TODO 新增
    private Date endTime;
    private String analysisRes;

    @Override
    public String toString() {
        return "AuditLogVo{" +
                "logId=" + logId +
                ", dbName='" + dbName + '\'' +
                ", dbMachine='" + dbMachine + '\'' +
                ", ruleId=" + ruleId +
                ", modifyPerson='" + modifyPerson + '\'' +
                ", modifyDate=" + modifyDate +
                ", chkTypeId='" + chkTypeId + '\'' +
                ", chkTypeName='" + chkTypeName + '\'' +
                ", centerId='" + centerId + '\'' +
                ", centerName='" + centerName + '\'' +
                ", modleId='" + modleId + '\'' +
                ", modleName='" + modleName + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", ruleSql='" + ruleSql + '\'' +
                ", limitValue='" + limitValue + '\'' +
                ", sqlValue='" + sqlValue + '\'' +
                ", status=" + status +
                ", conclusion='" + conclusion + '\'' +
                ", execMsec=" + execMsec +
                ", execRemark='" + execRemark + '\'' +
                ", createDate=" + createDate +
                ", createPerson='" + createPerson + '\'' +
                ", execDate=" + execDate +
                ", execPerson='" + execPerson + '\'' +
                ", endTime=" + endTime +
                ", analysisRes='" + analysisRes + '\'' +
                '}';
    }

    public String getAnalysisRes() {
        return analysisRes;
    }

    public void setAnalysisRes(String analysisRes) {
        this.analysisRes = analysisRes;
    }

    public long getLogId() {
        return logId;
    }

    public void setLogId(long logId) {
        this.logId = logId;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getDbMachine() {
        return dbMachine;
    }

    public void setDbMachine(String dbMachine) {
        this.dbMachine = dbMachine;
    }

    public long getRuleId() {
        return ruleId;
    }

    public void setRuleId(long ruleId) {
        this.ruleId = ruleId;
    }

    public String getModifyPerson() {
        return modifyPerson;
    }

    public void setModifyPerson(String modifyPerson) {
        this.modifyPerson = modifyPerson;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getChkTypeId() {
        return chkTypeId;
    }

    public void setChkTypeId(String chkTypeId) {
        this.chkTypeId = chkTypeId;
    }

    public String getChkTypeName() {
        return chkTypeName;
    }

    public void setChkTypeName(String chkTypeName) {
        this.chkTypeName = chkTypeName;
    }

    public String getCenterId() {
        return centerId;
    }

    public void setCenterId(String centerId) {
        this.centerId = centerId;
    }

    public String getCenterName() {
        return centerName;
    }

    public void setCenterName(String centerName) {
        this.centerName = centerName;
    }

    public String getModleId() {
        return modleId;
    }

    public void setModleId(String modleId) {
        this.modleId = modleId;
    }

    public String getModleName() {
        return modleName;
    }

    public void setModleName(String modleName) {
        this.modleName = modleName;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getRuleSql() {
        return ruleSql;
    }

    public void setRuleSql(String ruleSql) {
        this.ruleSql = ruleSql;
    }

    public String getLimitValue() {
        return limitValue;
    }

    public void setLimitValue(String limitValue) {
        this.limitValue = limitValue;
    }

    public String getSqlValue() {
        return sqlValue;
    }

    public void setSqlValue(String sqlValue) {
        this.sqlValue = sqlValue;
    }

    public short getStatus() {
        return status;
    }

    public void setStatus(short status) {
        this.status = status;
    }

    public String getConclusion() {
        return conclusion;
    }

    public void setConclusion(String conclusion) {
        this.conclusion = conclusion;
    }

    public long getExecMsec() {
        return execMsec;
    }

    public void setExecMsec(long execMsec) {
        this.execMsec = execMsec;
    }

    public String getExecRemark() {
        return execRemark;
    }

    public void setExecRemark(String execRemark) {
        this.execRemark = execRemark;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreatePerson() {
        return createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
    }

    public Date getExecDate() {
        return execDate;
    }

    public void setExecDate(Date execDate) {
        this.execDate = execDate;
    }

    public String getExecPerson() {
        return execPerson;
    }

    public void setExecPerson(String execPerson) {
        this.execPerson = execPerson;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

}
