package com.suke.czx;

import com.suke.czx.modules.aitrain.async.ModelTrainAsync;
import com.suke.czx.modules.aitrain.entity.ModelTrain;
import com.suke.czx.modules.aitrain.mapper.ModelTrainMapper;
import com.suke.czx.modules.aitrain.utils.ModelTrainUtil;
import com.suke.czx.modules.app.utils.JwtUtils;
import org.jasypt.encryption.StringEncryptor;
import org.jasypt.util.text.BasicTextEncryptor;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;


@RunWith(SpringRunner.class)
@SpringBootTest
public class JwtTest {

    @Resource
    private ModelTrainAsync modelTrainAsync;

    @Autowired
    private JwtUtils jwtUtils;

    @Resource
    private ModelTrainUtil modelTrainUtil;

    @Resource
    private ModelTrainMapper modelTrainMapper;

    @Test
    public void test() {
        String token = jwtUtils.generateToken(1);

        System.out.println(token);
    }

    @Test
    public void test1() {
        String path = "D:\\PycharmWorkspace\\pythonProject1\\custom_leaderboard.csv";
        List<ModelTrain> modelTrainList = modelTrainAsync.parseTrainResultCsv(path,"");
        modelTrainList.forEach(System.out::println);
    }

    @Test
    public void test2() {
//        modelTrainUtil.trainModel("pumpkin","","");
        ModelTrain modelTrain = new ModelTrain();
        modelTrain.setTrainName("pumpkin");
        modelTrain.setModel("pumpkin");
        modelTrain.setFitTime(46.78);
        int insert = modelTrainMapper.insert(modelTrain);
        System.out.println(insert);
        int id = modelTrain.getId();
        System.out.println(id);

    }

    @Test
    public void test3() {
        String filePath = "/home/<USER>/liuning/audit/normalpath/customer.xlsx";

        // 查找最后一个斜杠的位置
        int lastSlashIndex = filePath.lastIndexOf('/');

        // 如果找到了斜杠，则截取从斜杠之后的部分
        String fileName;
        if (lastSlashIndex != -1) {
            fileName = filePath.substring(lastSlashIndex + 1);
        } else {
            // 如果没有找到斜杠，则整个字符串都是文件名
            fileName = filePath;
        }

        System.out.println("File name: " + fileName);  // 输出: customer.xlsx
    }

}
