package com.suke.czx.modules.sys.service;

import com.suke.czx.modules.sys.entity.SysMenuEntity;

import java.util.List;
import java.util.Map;


/**
 * 菜单管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:42:16
 */
public interface SysMenuService {
	
	/**
	 * 根据父菜单，查询子菜单
	 * @param parentId 父菜单ID
	 * @param menuIdList  用户菜单ID
	 */
	List<SysMenuEntity> queryListParentId(Long parentId, List<Long> menuIdList);

	/**
	 * 根据父菜单，查询子菜单
	 * @param parentId 父菜单ID
	 */
	List<SysMenuEntity> queryListParentId(Long parentId);
	
	/**
	 * 获取不包含按钮的菜单列表
	 */
	List<SysMenuEntity> queryNotButtonList();
	
	/**
	 * 获取用户菜单列表
	 * @param userId
	 */
	List<SysMenuEntity> getUserMenuList(String userId);
	
	/**
	 * 查询菜单
	 */
	SysMenuEntity queryObject(Long menuId);
	
	/**
	 * 查询菜单列表
	 */
	List<SysMenuEntity> queryList(Map<String, Object> map);
	
	/**
	 * 查询总数
	 */
	int queryTotal(Map<String, Object> map);
	
	/**
	 * 保存菜单
	 */
	void save(SysMenuEntity menu);
	
	/**
	 * 修改
	 */
	void update(SysMenuEntity menu);
	
	/**
	 * 批量删除
	 */
	void deleteBatch(Long[] menuIds);

	/**
	 * 删除
	 * @param menuId
	 */
	void delete(Long menuId);
	
	/**
	 * 查询用户的权限列表
	 */
	List<SysMenuEntity> queryUserList(String userId);

	/**
	 * 根据menu的id查询menu的父id
	 * @param id
	 * @return
	 */
    Long queryMenuParentId(Long id);
}
