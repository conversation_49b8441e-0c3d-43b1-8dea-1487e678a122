package com.suke.czx.modules.audit.service;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.audit.entity.AuditReport;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 报表信息
 */
public interface AuditReportService {

    AuditReport queryObject(Long id);

    List<AuditReport> queryList(Map<String, Object> map);

    int queryTotal(Map<String, Object> map);

    void save(AuditReport auditReport);

    void update(AuditReport auditReport);

    void deleteBatch(Long[] Ids) throws Exception;

    /**
     * 批量保存报表
     * @param file
     * @return
     */
    R saveBatch(MultipartFile[] file, HttpServletRequest request,String userId) throws Exception;

    /**
     * 修改报表信息
     * @param reportId
     * @param reportType
     */
    void updateById(Long reportId, String reportType);
}
