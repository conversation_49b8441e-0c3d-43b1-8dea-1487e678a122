package com.suke.czx.modules.rule.service.impl;

import com.alibaba.druid.util.Utils;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.rule.dao.AuditRuleConfigDao;
import com.suke.czx.modules.rule.dao.AuditRuleDao;
import com.suke.czx.modules.rule.entity.AuditRule;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import com.suke.czx.modules.rule.service.AuditRuleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 规则父表实现类
 */
@Slf4j
@Service
public class AuditRuleServiceImpl implements AuditRuleService {

    public static final String NORMAL_TEXT = "正文";
    public static final int NORMAL_TEXT_INT_VLA = 99999;

    @Autowired
    private AuditRuleDao auditRuleDao;

    @Autowired
    private AuditRuleConfigDao auditRuleConfigDao;

    /**
     * 将规则文件存入父表(包括存储到子表)
     *
     * @param file
     * @param userId
     * @return
     */
    @Override
    @Transactional
    public R saveAuditRule(MultipartFile file, String userId) {
        //获取时间序列作为父规则id
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeSequence = sdf.format(new Date());
        //获取file的名称和后缀
        String completeName = file.getOriginalFilename();
        //获取文件名
        String fileName = completeName.substring(0, completeName.indexOf("."));
        //判断规则父表中是否存在此条规则
        int total = auditRuleDao.queryTotal(fileName);
        if (total != 0) {
            return R.error("此条规则已经存在!");
        }
        //先存储到子表(进一步解析excel)
        List<AuditRuleConfig> auditRuleConfigList = resolveRules(file, timeSequence);
        if (auditRuleConfigList.size() == 0) {
            //说明解析异常
            return R.error(-2, "excel解析异常!");
        }
        //将规则文件信息存储到父表中
        AuditRule auditRule = new AuditRule();
        auditRule.setId(timeSequence);
        auditRule.setName(fileName);
        auditRule.setCreateUserId(userId);
        auditRule.setTitleRow(auditRuleConfigList.get(0).getTitleRow());
        try {
            auditRuleConfigList.forEach(item -> {
                //在规则子表中保存
                auditRuleConfigDao.saveRuleConfig(item);
            });
            //先向子表中插入，若发生异常父表中不会插入数据
            auditRuleDao.add(auditRule);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }
        return R.ok("规则文件信息保存成功!");
    }

    /**
     * 查询全部规则父表
     *
     * @return
     */
    @Override
    public List<AuditRule> queryList(String id) {
        return auditRuleDao.queryAllRule(id);
    }

    /**
     * 根据id更新规则名称
     *
     * @param id
     * @param name
     * @return
     */
    @Override
    @Transactional
    public Integer updateRuleById(String id, String name, String updateUserId) {
        //在更新前 1、确认规则父表中是否存在相同规则名称
        int total = auditRuleDao.queryTotal(name);
        if (total > 0) {
            //不存在相同名称
            return -1;
        } else {
            //不存在相同名称
            //2、最后更新规则父表
            return auditRuleDao.updateRuleById(id, name, updateUserId, LocalDateTime.now());
        }
    }

    /**
     * 根据id删除规则父表
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Integer deleteRuleById(String id) {
        //先查询子表中是否存在数据
        int total = auditRuleConfigDao.queryTotal(id);
        if (total > 0) {
            // //1、先删除规则子表中的数据
            // Integer res = auditRuleConfigDao.deleteByFaId(id);
            // if (res > 0) {
            //     //再删除父表中的规则
            //     return auditRuleDao.delete(id);
            // }else {
            //     //删除子表失败了
            //     return -1;
            // }
            //说明存在子规则
            return -1;
        } else {
            //不存在关联数据，直接删除父表规则
            return auditRuleDao.delete(id);
        }
    }

    @Override
    public void add(AuditRule auditRule) {
        auditRuleDao.add(auditRule);
    }

    @Override
    public List<AuditRule> queryAuditRule(String name) {
        return auditRuleDao.queryAuditRule(name);
    }

    @Override
    public Map<String, Object> queryAuditRuleUrl(Integer url_id) {
        return auditRuleDao.queryAuditRuleUrl(url_id);
    }

    @Override
    public List<AuditRule> qryAuditRuleWithKeyword(String keyword) {
        return auditRuleDao.queryAuditRuleWithParam(keyword);
    }

    /**
     * 解析excel表格中的每个单元格，然后构成AuditRuleConfig对象
     *
     * @param file
     * @return
     */
    public List<AuditRuleConfig> resolveRules(MultipartFile file, String timeSequence) {
        try {
            //创建工作簿
            Workbook workbook = WorkbookFactory.create(file.getInputStream());
            //获取索引为0的sheet，即第一个sheet
            Sheet sheet = workbook.getSheetAt(0);
            //获取全部的行数(返回的是最后一行数据的下标)
            int lastRowSubscript = sheet.getLastRowNum();
            //构建一个AuditRuleConfig的List
            List<AuditRuleConfig> ruleConfigList = new ArrayList<>();
            for (int i = 1; i <= lastRowSubscript; i++) {
                //获取到内容行
                Row currentRow = sheet.getRow(i);
                //给子表设置相关属性
                AuditRuleConfig auditRuleConfig = new AuditRuleConfig();
                auditRuleConfig.setFaId(timeSequence);
                for (int j = 0; j < currentRow.getLastCellNum(); j++) {
                    Cell currentRowCell = currentRow.getCell(j);
                    if (currentRowCell == null) {
                        continue;
                    }
                    switch (j) {
                        case 0:
                            if (CellType.NUMERIC.equals(currentRowCell.getCellType())) {
                                auditRuleConfig.setRuleRow(Integer.valueOf(getSetCellStrAndRmFloat(currentRowCell)));
                            } else if (CellType.STRING.equals(currentRowCell.getCellType())) {
                                String cellStrVal = getSetCellStrAndRmFloat(currentRowCell);
                                if (NORMAL_TEXT.equals(cellStrVal)){
                                    auditRuleConfig.setRuleRow(NORMAL_TEXT_INT_VLA);
                                }
                            }
                            break;
                        case 1:
                            if (CellType.NUMERIC.equals(currentRowCell.getCellType())) {
                                auditRuleConfig.setRuleColumn(Integer.valueOf(getSetCellStrAndRmFloat(currentRowCell)));
                            }else if (CellType.STRING.equals(currentRowCell.getCellType())) {
                                String cellStrVal = getSetCellStrAndRmFloat(currentRowCell);
                                if (NORMAL_TEXT.equals(cellStrVal)){
                                    auditRuleConfig.setRuleColumn(NORMAL_TEXT_INT_VLA);
                                }
                            }
                            break;
                        case 2:
                            if (CellType.STRING.equals(currentRowCell.getCellType())) {
                                String name = currentRowCell.getStringCellValue();
                                auditRuleConfig.setName(name);
                            } else {
                                break;
                            }
                            break;
                        case 3:
                            if (CellType.STRING.equals(currentRowCell.getCellType())) {
                                String type = currentRowCell.getStringCellValue();
                                auditRuleConfig.setType(type);
                            } else {
                                break;
                            }
                            break;
                        case 4:
                            setCellStr(currentRowCell);
                            auditRuleConfig.setParameter(currentRowCell.getStringCellValue());
                            break;
                        case 5:
                            if (CellType.STRING.equals(currentRowCell.getCellType())) {
                                String remarks = currentRowCell.getStringCellValue();
                                auditRuleConfig.setRemarks(remarks);
                            }
                            break;
                        case 6:
                            if (CellType.NUMERIC.equals(currentRowCell.getCellType())) {
                                auditRuleConfig.setTitleRow(Integer.parseInt(getSetCellStrAndRmFloat(currentRowCell)));
                            }
                            break;
                        case 7:
                            auditRuleConfig.setTitleEndRow(Integer.parseInt(getSetCellStrAndRmFloat(currentRowCell)));
                            break;
                    }
                }
                ruleConfigList.add(auditRuleConfig);
            }
            return ruleConfigList;
        } catch (IOException e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }


    private void setCellStr(Cell cell) {
        cell.setCellType(CellType.STRING);
    }

    private String getSetCellStrAndRmFloat(Cell cell) {
        cell.setCellType(CellType.STRING);
        String content = cell.getStringCellValue();
        if (content.indexOf(".") > 0) {
            content = content.replaceAll("[.]0+?$", "");//去掉后面无用的零
        }
        return content;
    }
}
