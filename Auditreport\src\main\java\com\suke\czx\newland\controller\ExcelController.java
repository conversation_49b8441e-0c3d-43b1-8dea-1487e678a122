package com.suke.czx.newland.controller;


import com.suke.czx.newland.common.RespInfo;
import com.suke.czx.newland.service.IExcelService;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;


@RestController
@RequestMapping("/excel")
public class ExcelController
{
    private static final Logger logger = LoggerFactory.getLogger(ExcelController.class);

    @Autowired
    private IExcelService iExcelService;

    /**
     * <AUTHOR>
     * @param multipartFile
     * @return
     *  TODO 新增
     */
    @RequestMapping(value = "/resultImport", method = RequestMethod.POST)
    public RespInfo resultImport(@RequestPart("file") MultipartFile multipartFile) {
        logger.info("进入稽核结果上传功能");
        if (!checkFileValid(multipartFile)) {
            return new RespInfo("500", "上传文件不能为空，且大小不能超过20M,且为xlsx或xls类型！");
        }
        try {
            Workbook wb;
//            wb = new HSSFWorkbook(multipartFile.getInputStream());//根据页面index 获取sheet页
            try {
                wb = new XSSFWorkbook(multipartFile.getInputStream());
            } catch (Exception ex) {
                wb = new HSSFWorkbook(multipartFile.getInputStream());
            }
            iExcelService.importResult(wb);
        } catch (Exception e) {
            logger.error("导入配置失败" , e);
            return new RespInfo("400", "导入配置失败");
        }
        return  new RespInfo("200", "上传成功");
    }

    /**
     * <AUTHOR>
     * @param multipartFile
     * @return
     * TODO 新增
     */
    private boolean checkFileValid(MultipartFile multipartFile) {
        logger.info("上传的文件的大小为===========：{}", multipartFile.getSize());
        if (multipartFile.isEmpty() || multipartFile.getSize() > 2000000 || !multipartFile.getOriginalFilename().contains("xlsx")) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * <AUTHOR>
     * @param multipartFile
     * @return
     * TODO 新增
     */
    @RequestMapping(value = "/auditImport", method = RequestMethod.POST)
    public RespInfo auditImport(@RequestPart("file") MultipartFile multipartFile) {
        logger.info("进入稽核规则上传功能");
        if (!checkFileValid(multipartFile)) {
            return new RespInfo("500", "上传文件不能为空，且大小不能超过20M,且为xlsx或xls类型！");
        }
        try {
            Workbook wb;
//            wb = new HSSFWorkbook(multipartFile.getInputStream());//根据页面index 获取sheet页
            try {
                wb = new XSSFWorkbook(multipartFile.getInputStream());
            } catch (Exception ex) {
                wb = new HSSFWorkbook(multipartFile.getInputStream());
            }
            iExcelService.importAudit(wb);
        }catch (IllegalArgumentException ille){
            logger.error("导入配置失败" , ille);
            return new RespInfo("400", "导入配置失败:"+ille.getMessage());
        }
        catch (Exception e) {
            logger.error("导入配置失败" , e);
            return new RespInfo("400", "导入配置失败");
        }
        return  new RespInfo("200", "上传成功");
    }
}
