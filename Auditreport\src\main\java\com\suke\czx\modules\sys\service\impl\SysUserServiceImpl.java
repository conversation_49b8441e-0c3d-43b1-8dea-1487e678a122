package com.suke.czx.modules.sys.service.impl;

import com.suke.czx.modules.sys.dao.SysRoleDao;
import com.suke.czx.modules.sys.dao.SysUserDao;
import com.suke.czx.modules.sys.entity.SysUserAddVo;
import com.suke.czx.modules.sys.entity.SysUserEntity;
import com.suke.czx.modules.sys.entity.UserListQryVo;
import com.suke.czx.modules.sys.service.SysRoleService;
import com.suke.czx.modules.sys.service.SysUserRoleService;
import com.suke.czx.modules.sys.service.SysUserService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;


/**
 * 系统用户
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:46:09
 */
@Service("sysUserService")
public class SysUserServiceImpl implements SysUserService {
	@Autowired
	private SysUserDao sysUserDao;
	@Autowired
	private SysUserRoleService sysUserRoleService;
	@Autowired
	private SysRoleService sysRoleService;

	@Autowired
	private SysRoleDao sysRoleDao;

	@Override
	public List<String> queryAllPerms(String userId) {
		return sysUserDao.queryAllPerms(userId);
	}

	@Override
	public List<Long> queryAllMenuId(String userId) {
		return sysUserDao.queryAllMenuId(userId);
	}

	@Override
	public SysUserEntity queryByUserName(String username) {
		return sysUserDao.queryByUserName(username);
	}

	@Override
	public SysUserEntity queryObject(String userId) {
		return sysUserDao.queryObject(userId);
	}

	@Override
	public List<SysUserEntity> queryList(UserListQryVo condition){
		SysUserEntity param = new SysUserEntity();
		String[] roleIds = null;
		if (condition != null){
			param.setUserName(condition.getUserName());
			roleIds = condition.getRoleIds();
		}
		return sysUserDao.qryUserListByCondition(param, roleIds);
	}

	@Override
	public int queryTotal(Map<String, Object> map) {
		return sysUserDao.queryTotal(map);
	}

	@Override
	@Transactional
	public void save(SysUserAddVo user) {
		SysUserEntity userInfo = constructUser(user);
		SysUserEntity sysUserInfo = sysUserDao.queryByUserName(user.getUserName());
		if (sysUserInfo != null){
            throw new IllegalArgumentException("用户名称已存在,请更换用户名称.");
        }
		sysUserDao.save(userInfo);
		//保存用户与角色关系
        Long[] roles = user.getRole().stream().map(Integer::longValue).toArray(Long[]::new);
        sysUserRoleService.saveUserRole(userInfo.getUserName(), roles);
	}

	private SysUserEntity constructUser(SysUserAddVo reqUserInfo){
		SysUserEntity res = new SysUserEntity();
		if (reqUserInfo == null){
			return res;
		}
		res.setUserId(reqUserInfo.getUserName());
		res.setUserName(reqUserInfo.getUserName());
		res.setPwStr(reqUserInfo.getPassword());
		res.setMobile(reqUserInfo.getMobile());
        res.setCreateTime(new Date());
		res.setPassword(reqUserInfo.getPassword());
		return res;
	}

	@Override
	@Transactional
	public void update(SysUserEntity user) {
		if(StringUtils.isBlank(user.getPassword())){
			user.setPassword(null);
		}else{
			user.setPassword(user.getPassword());
		}
		sysUserDao.update(user);

		//检查角色是否越权
		// checkRole(user);

		//保存用户与角色关系
		// sysUserRoleService.saveOrUpdate(user.getUserId(), user.getRoleIdList());
	}

	@Override
	@Transactional
	public void deleteBatch(String[] userId) {
		boolean hasAdminFlag = false;
		for (String userID : userId) {
			if (isAdminRole(userID)){
				hasAdminFlag = true;
				break;
			}
		}
		if (hasAdminFlag){
			throw new IllegalArgumentException("权限不支持删除管理员用户");
		}


		sysUserDao.delAutoUserInfoByUserIds(userId);
		sysUserDao.delAutoUserRoleByUserIds(userId);
	}

	private boolean isAdminRole(String userId){
		Set<String> userRoleIds = sysUserDao.qryUserRoleByUserId(userId);
		List<String> roleNameList = sysRoleDao.qryRoleNameByRoleIds(userRoleIds);
		return roleNameList != null && roleNameList.contains("ROLE_ADMIN");
	}

	@Override
	public int updatePassword(String userId, String password, String newPassword) {
		Map<String, Object> map = new HashMap<>();
		map.put("userId", userId);
		map.put("password", password);
		map.put("newPassword", newPassword);
		return sysUserDao.updatePassword(map);
	}

	/**
	 * 检查角色是否越权
	 */
	private void checkRole(SysUserEntity user){
		//如果不是超级管理员，则需要判断用户的角色是否自己创建
		// if(user.getCreateUserId() == Constant.SUPER_ADMIN){
		// 	return ;
		// }

		//查询用户创建的角色列表
		// List<Long> roleIdList = sysRoleService.queryRoleIdList(user.getCreateUserId());

		//判断是否越权
		// if(!roleIdList.containsAll(user.getRoleIdList())){
		// 	throw new RRException("新增用户所选角色，不是本人创建");
		// }
	}
}
