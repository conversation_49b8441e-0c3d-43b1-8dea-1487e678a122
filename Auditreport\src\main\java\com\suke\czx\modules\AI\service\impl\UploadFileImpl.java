package com.suke.czx.modules.AI.service.impl;

import com.alibaba.druid.util.Utils;
import com.suke.czx.config.FtpProperties;
import com.suke.czx.modules.AI.util.FTPAiUtils;
import com.suke.czx.modules.AI.dao.AiFileDao;
import com.suke.czx.modules.AI.entity.AiFile;
import com.suke.czx.modules.AI.service.UploadFile;
import com.suke.czx.modules.audit.util.Ftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.boot.ApplicationHome;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.util.*;
@Slf4j
@Service
public class UploadFileImpl implements UploadFile {


    private final AiFileDao aiFileDao;
    private final FtpProperties ftpProperties;

    public UploadFileImpl(AiFileDao aiFileDao, FtpProperties ftpProperties) {
        this.aiFileDao = aiFileDao;
        this.ftpProperties = ftpProperties;
    }

    public String getAiUploadPath() {
        ApplicationHome applicationHome = new ApplicationHome(this.getClass());
        return applicationHome.getSource().getParentFile().getParentFile().getAbsolutePath() + "\\src\\main\\resources\\Audit_Ai\\pythonProject1";
    }

    //把python运行结果写入excel文件
    public void insertIntoExcel(String path, ArrayList data) {
        try {
            //  读取Excel文件
            FileInputStream file = new FileInputStream(new File(path));
            Workbook workbook = new XSSFWorkbook(file);
            Sheet sheet = workbook.getSheetAt(0);


            //  在第一列前新增一列
            int columnToInsert = 0;
            for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                Cell cell = row.createCell(columnToInsert);
                cell.setCellValue((String) data.get(i));
            }

            //  写入修改后的Excel文件
            FileOutputStream outFile = new FileOutputStream(path);
            workbook.write(outFile);
            outFile.close();
            log.info("Excel文件处理完毕。");
        } catch (IOException e) {
            log.error(Utils.getStackTrace(e));
        }
    }

    public void convertCSVToExcel(String csvFilePath, String excelFilePath) {
        try {
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("Sheet1");
            String line;
            BufferedReader reader = new BufferedReader(new FileReader(csvFilePath));
            int rowNumber = 0;
            while ((line = reader.readLine()) != null) {
                String[] data = line.split(",");
                Row row = sheet.createRow(rowNumber++);
                for (int i = 0; i < data.length; i++) {
                    row.createCell(i).setCellValue(data[i]);
                }
            }
            FileOutputStream fileOut = new FileOutputStream(excelFilePath);
            workbook.write(fileOut);
            fileOut.close();
            reader.close();
            log.info("CSV  file  converted  to  Excel  successfully!");
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
        }
    }

    /**
     * 上传ai稽核文件，待稽核文件类型是0，把上传的文件列表保存到数据库
     *
     * @param file
     * @param request
     * @param userId
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public String upLoadFile(MultipartFile[] file, HttpServletRequest request, String userId) throws Exception {
        List<AiFile> aiFilestList = new ArrayList<>();
        Ftp ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
        String uploadPath = getAiUploadPath();
        for (MultipartFile item : file) {
            if (item.isEmpty()) {
                return "文件为空";
            }
            byte[] bytes = item.getBytes();
            String filename = item.getOriginalFilename();
            List<AiFile> aiFileList = aiFileDao.qryAuditAiFileWithName(filename);
            if (aiFileList != null && !aiFileList.isEmpty()) {
                throw new IllegalArgumentException("上传文件名称在数据库已存在.");
            }
            String url = item.getOriginalFilename();
            String downloadUrl = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort() + request.getContextPath() + "/aiTools/AITool/download?fileName=" + url;
            AiFile aiFile = new AiFile();
            aiFile.setName(filename);
            aiFile.setType("0");
            aiFile.setUrl(downloadUrl);
            aiFile.setReportSize(item.getSize());
            aiFile.setUploadUserId(userId);
            aiFilestList.add(aiFile);
            ftp.sshSftp(bytes, filename, ftpProperties.getAiPath());
            if (!aiFilestList.isEmpty()) {
                aiFileDao.save(aiFilestList);
            } else {
                uploadPath = "已存在该文件，请重新上传！";
            }
        }
        return uploadPath;
    }

    @Override
    public void auditAi(String auditFileId) {
        //先从远程主机上下载文件
        try {
            AiFile auditAiFile = aiFileDao.qyrAuditAiFileWithId(auditFileId);
            if (auditAiFile == null) {
                throw new IllegalArgumentException("稽核数据文件不存在.");
            }
            Ftp ftp;
            ftp = new Ftp(ftpProperties.getHost(), ftpProperties.getPort(), ftpProperties.getUser(), ftpProperties.getPassword());
            Vector v = ftp.listFiles(ftpProperties.getAiPath());
            for (int i = 0; i < v.size(); i++) {
                String fileNames = v.get(i).toString();
                if (fileNames.contains(".csv")) {
                    String[] strings = fileNames.split(" ");
                    for (int j = 0; j < strings.length; j++) {
                        if (strings[j].contains(".csv")) {
                            String fileName = strings[j];
                            String exePath = ftpProperties.getPythonbasePath();
                            exePath = exePath.replace("/", "\\");
                            String pyPath = exePath + "\\model\\autoglu_pre.py";
                            String filePath = "data/" + fileName;
                            String cmd1 = "cmd.exe /c dir && cd " + exePath + " && python " + pyPath + " " + ftpProperties.getHost() + " " + ftpProperties.getPort() + " " + ftpProperties.getUser() + " " + ftpProperties.getPassword() + " " + ftpProperties.getAiPath() + "/" + fileName + " " + filePath;
                            Process process = Runtime.getRuntime().exec(cmd1);
                            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "gbk"));
                            StringBuilder result = new StringBuilder();
                            String line;
                            while ((line = reader.readLine()) != null) {
                                result.append(line).append("\n");
                                log.info(line);
                            }
                            reader.close();
                            process.waitFor();
                            String substringResult = result.substring(result.indexOf("["));
                            List<String> resultList = Arrays.asList(substringResult.split(","));
                            ArrayList<String> arrayList = new ArrayList<>(resultList);
                            arrayList.add(0, "result");

                            //csv-excel
                            String excelPath = ftpProperties.getPythonbasePath() + "\\data\\" + fileName.replace(".csv", "result.xlsx");
                            String csvPath = ftpProperties.getPythonbasePath() + "\\data\\" + fileName;
                            convertCSVToExcel(csvPath, excelPath);
                            // 把python执行结果存放到excle文件里
                            insertIntoExcel(excelPath, arrayList);
                            //把文件上传回服务器
                            File file = new File(excelPath);
                            byte[] bytes = Files.readAllBytes(file.toPath());
                            String upexcelName = fileName.replace(".csv", "result.xlsx");
                            ftp.sshSftp(bytes, upexcelName, ftpProperties.getAiPath());
                        }
                    }
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

