package com.suke.czx.newland.service;

import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.dto.AuditLogDownloadRecordDto;
import com.suke.czx.newland.vo.AuditLogDownloadVo;
import com.suke.czx.newland.vo.EditAuditLogVo;
import com.suke.czx.newland.vo.LogIdsVo;
import com.suke.czx.newland.vo.audit.*;
import com.suke.czx.newland.vo.echartstable.CheckLogAnalysisVo;

import java.io.OutputStream;
import java.util.List;

public interface IAuditService {
    Result addAuditType(AuditDictTypeVo auditDictTypeVo);
    Result queryAuditConfig(AuditQueryVo auditConfigVo);
    Result queryAuditCode();
    Result queryAuditCenter();
    Result eidtAuditConfig(AuditConfigVo auditConfigVo, boolean isEdit);
    Result delAuditConfig(String ruleId);
    Result execProcess(List<AuditExecVo> cfgIds, String execPerson, String runDatasourceId);
    Result queryDictType();
    Result delAuditDictByDictId(Integer dictId);
    Result addAuditDict(AuditDictVo dict);
    Result editAuditDict(AuditDictVo dict);
    Result queryAuditLogWithPageInfo(AuditLogQryVo condition , int pageIndex, int pageSize);
    Result queryExecStatus();
    Result queryAuditSql(String ruleSql);
    Result saveExecRemark(AuditLogVo logVo);
//    Result queryHisConfig(long ruleId);

    // TODO ����
    Result queryHisConfig(long ruleId, String modifyPerson, String ruleName);

    Result saveAnalysisRes(AuditLogVo logVo);

    Result queryJobStatus();
    Result homepageMetrics();

    Result checkLogAnalysis(CheckLogAnalysisVo checkLogAnalysisVo);

    Result queryAuditConfigWithRuleIds(String[] ruleIds);

    Result queryAuditLogWithLogIds(String[] logIds);

    Result editAuditLogAnalysisRes(EditAuditLogVo param);

    List<AuditLogDownloadRecordDto> downloadAuditLogs(AuditLogDownloadVo param);
}

