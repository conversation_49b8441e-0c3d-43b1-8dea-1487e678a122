package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetSortDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.Map;

@Slf4j
public class SortHandler extends ExcelHandleRuler {
    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetSortDto condition = (SheetSortDto) param;
        boolean res = true;
        Sheet sheet = condition.getSheet();
        int sortColumn = condition.getColumn();
        Cell preCell = null;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);
            Cell oneRowTargetCell = oneRow.getCell(sortColumn);
            setCellStr(oneRowTargetCell);

            if (i == condition.getStartRow() && !"1".equals(oneRowTargetCell.getStringCellValue())) {
                log.info("开始行{}序号开始值不等于1", i);
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                setCellComment(sheet, oneRowTargetCell, "序号开始值不等于1");
                res = false;
                break;
            }

            if (preCell != null) {
                setCellStr(preCell);
                String preCellStrVal = preCell.getStringCellValue();
                String curCellStr = oneRowTargetCell.getStringCellValue();
                if (compareTwoCellStrVal(preCellStrVal, curCellStr)) {
                    setCellRed(oneRowTargetCell, condition.getCellStyle());
                    setCellComment(sheet, oneRowTargetCell, "此处排序开始出现问题，后续行不再检查.");
                    res = false;
                    break;
                }
            }
            preCell = oneRowTargetCell;
        }
        return res ? success() : fail();
    }

    private boolean compareTwoCellStrVal(String param1, String param2) {
        if (param1 == null || param1.isEmpty()) {
            return false;
        }

        if (param2 == null || param2.isEmpty()) {
            throw new IllegalArgumentException("param2 is null or empty.");
        }

        int val1 = Integer.parseInt(param1);
        int val2 = Integer.parseInt(param2);
        return val1 > val2;
    }
}
