package com.suke.czx.modules.aitrain.utils;

import com.alibaba.fastjson.JSONObject;
import com.suke.czx.modules.aitrain.entity.ModelTrainInfo;
import com.suke.czx.modules.audit.util.Ftp;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * 执行excel相关操作，比如将数据写入xlsx中
 */
@Component
@Slf4j
public class ExcelHandleUtil {

    public ModelTrainInfo coverToExcel(List<JSONObject> param, Ftp ftp, String fileName, String baseFilePath, HttpServletResponse response) {
        try {
            Objects.requireNonNull(param);
            Objects.requireNonNull(fileName);
            //获取数据中的第一个对象
            JSONObject rowOne = param.get(0);
            //获取第一对象中的元素，即获取表头
            Set<String> heads = rowOne.keySet();
            //所有的标头
            List<String> headList = Lists.newArrayList(heads);
            //创建xlsx表格
            XSSFWorkbook workbook = new XSSFWorkbook();
            //创建工作表
            XSSFSheet sheet = workbook.createSheet();
            XSSFRow row = sheet.createRow(0);
            //依次创建列，然后写入数据
            int rowIndex = 1;
            int fieldNumRowNum = 0;
            int fieldNullCount = 0;
            //总的空单元格个数
            int totalNullCount = 0;
            //判断是字符串的个数
            int stringCount = 0;
            //判断是数字类型的个数
            int numCount = 0;
            for (int i = 0; i < headList.size(); i++) {
                row.createCell(i).setCellValue(headList.get(i));
            }
            log.info("xlsx表头：{}",headList);
            for (JSONObject item : param) {
                List<String> values = new ArrayList<>();
                //每行数据
                for (String header : headList) {
                    // 尝试从JSONObject中获取值，如果键不存在则填充空字符串或进行其他处理
                    String value = item.getString(header);
                    if (value == null || value.isEmpty()) {
                        //说明为空字段
                        fieldNullCount ++;
                        totalNullCount ++;
                    }
                    // 检查是否为整数  检查是否为浮点数（包括整数）
                    if (value.matches("\\d+") || value.matches("-?\\d+(\\.\\d+)?")) {
                        numCount++;
//                        log.info("获取到的数字类型的值：{}",value);
                    }else {
                        stringCount++;
//                        log.info("获取到的字符串类型的值：{}",value);
                    }
                    if (item.containsKey(header)) {
                        values.add(value);
                    } else {
                        values.add("");
                    }
                }
                double tmpRes = (double)fieldNullCount / headList.size();
//                log.info("第{}行的空字段比例：{}",rowIndex,tmpRes);
                if (tmpRes >= 0.5) {
                    //说明此行的空字段比例大于等于50%
                    fieldNumRowNum++;
                }
                //重置
                fieldNullCount = 0;
                XSSFRow dataRow = sheet.createRow(rowIndex);
                //向列中写入数据
                for (int i = 0; i < values.size(); i++) {
                    String res = values.get(i);
                    XSSFCell dataRowCell = dataRow.createCell(i);
                    dataRowCell.setCellValue(res);
                }
                rowIndex++;
            }
            //构建返回信息
            ModelTrainInfo modelTrainInfo = new ModelTrainInfo();
            //获取文件总的列数
            modelTrainInfo.setTotalColumnNum(headList.size());
            //获取文件总的行数
//            int lastRowNum = rowIndex;
            modelTrainInfo.setTotalRowNum(rowIndex);
            //存在50%以上字段为空的行数
            modelTrainInfo.setNullCellsOfRowNum(fieldNumRowNum);
            //字符串类型的单元格个数（用统计出来的字符串类型的个数减去空单元格的个数）
            modelTrainInfo.setStringTypeNum(stringCount - totalNullCount);
            //数字类型的单元格个数
            modelTrainInfo.setNumberTypeNum(numCount);
            //文件名
            modelTrainInfo.setFileName(fileName);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            //上传
            ftp.sshSftp(outputStream.toByteArray(), fileName, baseFilePath);
            workbook.close();
            //构建返回结果List<Map<String,Integer>>
            Map<String,Integer> map = new HashMap<>();
            for (int i = 0; i < headList.size(); i++) {
                map.put(headList.get(i),i);
            }
            //设置文件字段map
            modelTrainInfo.setFileField(map);
            return modelTrainInfo;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
