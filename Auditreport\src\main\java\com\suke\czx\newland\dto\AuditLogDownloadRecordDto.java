package com.suke.czx.newland.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class AuditLogDownloadRecordDto {
    private String logId;
    private String dbName;
    private String dbMachine;
    private String ruleId;
    private String modifyPerson;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String modifyDate;
    private String chkTypeId;
    private String chkTypeName;
    private String centerId;
    private String centerName;
    private String modleId;
    private String modleName;
    private String ruleName;
    private String ruleSql;
    private String limitValue;
    private String sqlValue;
    private String status;
    private String conclusion;
    private String execMsec;
    private String execRemark;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String createDate;
    private String createPerson;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String execDate;
    private String execPerson;
    private String analysisRes;
}
