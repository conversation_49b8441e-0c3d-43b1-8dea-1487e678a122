package com.suke.czx.modules.rule.controller;

import com.alibaba.druid.util.Utils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.rule.entity.AuditRule;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import com.suke.czx.modules.rule.entity.RuleIds;
import com.suke.czx.modules.rule.service.AuditRuleConfigService;
import com.suke.czx.modules.rule.service.AuditRuleService;
import com.suke.czx.modules.rule.util.DownloadExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 规则子表
 */
@Slf4j
@RestController
@RequestMapping("/audit/rule/config")
public class AuditRuleConfigController extends AbstractController{

    private static List<AuditRuleConfig> userList = new ArrayList<>();

    @Autowired
    private AuditRuleConfigService auditRuleConfigService;

    @Autowired
    private AuditRuleService auditRuleService;
//    /**
//     * 根据规则父表的id查询规则子表
//     * @param faId
//     * @param type
//     * @return
//     */
    @RequestMapping(value = "/list",method = RequestMethod.GET)
    public R getRuleConfigList(@RequestParam(value = "faId",defaultValue = "") String faId,
                               @RequestParam(value = "type",defaultValue = "") String type,
                               @RequestParam(defaultValue = "1") Integer current,
                               @RequestParam(defaultValue = "10") Integer pageSize) {
        System.out.println("faId:" + faId + "===type:" + type);
        //开启分页
        PageHelper.startPage(current,pageSize);
        List<AuditRuleConfig> auditRuleConfigList = auditRuleConfigService.queryRuleConfigList(faId,type);
        //封装
        PageInfo<AuditRuleConfig> configPageInfo = new PageInfo<>(auditRuleConfigList);
        long total = configPageInfo.getTotal();

        return Objects.requireNonNull(R.ok().put("ruleConfigList", auditRuleConfigList)).put("total",total);
    }

//    @RequestMapping(value = "/list",method = RequestMethod.POST)
//    public R getRuleConfigList(@RequestBody AuditRuleConfig auditRuleConfig) {
//        //开启分页
//        PageHelper.startPage(auditRuleConfig.getCurrent(),auditRuleConfig.getPageSize());
//        List<AuditRuleConfig> auditRuleConfigList = auditRuleConfigService.queryRuleConfigList(auditRuleConfig.getFaId(),auditRuleConfig.getType());
//        //封装
//        PageInfo<AuditRuleConfig> configPageInfo = new PageInfo<>(auditRuleConfigList);
//        long total = configPageInfo.getTotal();
//
//        return Objects.requireNonNull(R.ok().put("ruleConfigList", auditRuleConfigList)).put("total",total);
//    }

    /**
     * 更新规则信息(子表)
     * @param auditRuleConfig
     * @return
     */
    @SysLog("更新稽核子规则")
    @RequestMapping(value = "/update",method = RequestMethod.PUT)
    public R updateRuleConfig(@RequestBody AuditRuleConfig auditRuleConfig) {
        // System.out.println("===" + auditRuleConfig.toString());
        Integer res = auditRuleConfigService.updateRuleConfig(auditRuleConfig);
        if (res > 0) {
            return R.ok().put("result","子规则信息更新成功!");
        }else {
            return R.error().put("result","更新失败");
        }
    }

    /**
     * 根据id删除规则文件(子表)
     * @param id
     * @return
     */
    @SysLog("删除稽核子规则")
    @RequestMapping(value = "/delRuleById/{id}",method = RequestMethod.DELETE)
    public R delRuleConfig(@PathVariable("id") Long id) {
        Integer res = auditRuleConfigService.delRuleConfigByid(id);
        if (res > 0) {
            return R.ok().put("result","子规则信息更删除成功!");
        }else {
            return R.error().put("result","子规则信息删除失败!");
        }
    }

    @DeleteMapping("/delRuleByIds")
    public R delRuleByIds(@RequestBody RuleIds ruleIds){
        try {
            auditRuleConfigService.delRuleConfigByIds(ruleIds.getRuleIds());
        }catch (Exception e){
            log.error(Utils.getStackTrace(e));
            return R.error().put("result", "子规则信息批量删除失败.");
        }
        return R.ok().put("result","子规则信息批量删除成功!");
    }

    /**
     * 根据id获取规则的详细信息
     * @param id
     * @return
     */
    @RequestMapping(value = "/getRuleInfo/{id}",method = RequestMethod.GET)
    public R getRuleConfigInfoById(@PathVariable("id") Long id) {

        AuditRuleConfig auditRuleConfig = auditRuleConfigService.queryRuleConfigInfoById(id);
        if (auditRuleConfig != null) {
            return R.ok().put("auditRuleConfig",auditRuleConfig);
        }else {
            return R.error().put("result","规则信息获取失败!");
        }
    }

    /**
     * 保存单条规则信息，在页面点击添加规则按钮（向规则子表中）
     * @param auditRuleConfig
     * @return
     */
    @SysLog("添加稽核子规则")
    @RequestMapping(value = "/addRuleConfig",method = RequestMethod.POST)
    public R addRuleConfig(@RequestBody AuditRuleConfig auditRuleConfig) {

        try {
            List<AuditRule> auditRules = auditRuleService.queryAuditRule(auditRuleConfig.getFaName());
            //先查询父规则是否存在，若存在，不添加
            if (auditRules.size() == 0 || auditRules == null) {
                //自动生成faid
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                String timeSequence = sdf.format(new Date());
                auditRuleConfig.setFaId(timeSequence);
                //插入父规则表
                AuditRule auditRule = new AuditRule();
                auditRule.setId(timeSequence);
                auditRule.setName(auditRuleConfig.getFaName());
                auditRule.setCreateUserId(getUserId());
                auditRule.setTitleRow(auditRuleConfig.getTitleRow());
                auditRuleService.add(auditRule);
            }else{
                auditRuleConfig.setFaId(auditRules.get(0).getId());
            }

            //插入子规则表
            auditRuleConfigService.addRuleConfig(auditRuleConfig);

            return R.ok().put("result","规则新增成功!");
        }catch (Exception e) {
            e.printStackTrace();
            System.out.println("规则新增失败!"+e.getMessage());
            return R.error().put("result","规则新增失败!");
        }
    }

    @RequestMapping(value ="/queryFaRule/{id}",method = RequestMethod.GET)
    public R queryUserById(@PathVariable String id) throws IOException {
        List<AuditRuleConfig> users = auditRuleConfigService.getAuditRuleById(id); // 假设这里返回的是用户列表，根据实际情况调整
        userList.clear();
        if (users == null || users.isEmpty()) {
            return R.error("规则数据为空，无法导出");
        }
        userList = users;
        return R.ok();
    }

    @RequestMapping(value ="/downloadFaRule/{id}",method = RequestMethod.GET)
    public ResponseEntity<Object> exportUserById(@PathVariable String id) throws IOException {
        System.out.println("id=========" + id);
        byte[] excelBytes = DownloadExcelUtil.exportUsersToExcel(userList); // 将用户数据导出为Excel字节数组
        log.info("download infos : {}", userList);
        String filename = id+".xlsx";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename="+ URLEncoder.encode(filename,"UTF-8")); // 设置附件名和下载属性
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE); // 设置内容类型为二进制流，以支持文件下载

        return new ResponseEntity<>(excelBytes, headers, HttpStatus.OK);
    }

}
