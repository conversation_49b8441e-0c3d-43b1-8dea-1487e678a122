package com.suke.czx.modules.AI.controller;

import com.github.pagehelper.PageInfo;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.AI.entity.AIFileVo;
import com.suke.czx.modules.AI.entity.AiFile;
import com.suke.czx.modules.AI.entity.AuditAiFileQryCondition;
import com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo;
import com.suke.czx.modules.AI.service.AIAuditService;
import com.suke.czx.modules.AI.service.UploadFile;
import com.suke.czx.modules.AI.util.FTPAiUtils;
import com.suke.czx.modules.audit.controller.AbstractController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@RestController
@RequestMapping("/aiTools/AITool")
public class AIController extends AbstractController {
    @Autowired
    UploadFile uploadFile;

    @Autowired
    AIAuditService aiAuditService;

    /**
     * @param file
     * @return 保存文件路径
     * 上传ai预测工具的文件
     */

    @RequestMapping(value = "/uploadFile", method = RequestMethod.POST)
    public String uploadAiFile(@RequestParam("file") MultipartFile[] file, HttpServletRequest request) throws Exception {
        String res = uploadFile.upLoadFile(file, request, getUserId());
        return res;
    }

    @RequestMapping(value = "/auditAi", method = RequestMethod.GET)
    public R AI(@RequestParam("auditAiFileId") String auditAiFileId,@RequestParam("modelName") String modelName) {
        aiAuditService.auditFileByFileId(auditAiFileId,modelName);
        return R.ok();
    }

    @RequestMapping(value = "/download", method = RequestMethod.GET)
    public void downloadAiSourceFile(@RequestParam("fileName") String fileName, HttpServletResponse response) throws Exception {
        FTPAiUtils.getFile(fileName, response);
    }

    @GetMapping("/downloadAuditedFile")
    public void downloadAiFile(@RequestParam("sourceFileId") String sourceFileId, HttpServletResponse response) {
        aiAuditService.downloadAuditedFile(sourceFileId, response);
    }


    @PostMapping("/listAuditAiFile")
    public R listAuditAiFile(@RequestBody AuditAiFileQryCondition condition) {
        return R.ok().put("data", aiAuditService.qryAuditAiFile(condition));
    }

    @PostMapping("/generateAIAuditFileFromSql")
    public R generateAIAuditFileFromSql(@RequestBody GenerateAIFileConditionVo condition, HttpServletRequest request) {
        aiAuditService.generateAIAuditFile(condition, request, getUserId());
        return R.ok();
    }

    @PostMapping("/delAuditAiFile")
    public R delAuditAiFile(@RequestBody List<String> ids) {
        aiAuditService.updateAuditFileDeleted(ids);
        return R.ok();
    }
}
