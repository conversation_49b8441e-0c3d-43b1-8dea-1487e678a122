package com.suke.czx.modules.aitrain.service;

import com.suke.czx.common.utils.R;
import com.suke.czx.modules.AI.entity.GenerateAIFileConditionVo;
import com.suke.czx.modules.aitrain.entity.GenerateFileFromSqlVO;
import com.suke.czx.modules.aitrain.entity.ModelTrainInfo;
import com.suke.czx.modules.aitrain.entity.ModelTrainParam;
import com.suke.czx.modules.aitrain.entity.ParseVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface AiTrainService {

    /**
     * 通过查询sql生成xlsx文件
     * @param condition
     * @param request
     */
    ModelTrainInfo generateTrainDataFile(GenerateFileFromSqlVO condition, HttpServletResponse response, HttpServletRequest request);

    /**
     * 上传训练数据（包含正常和异常训练数据）
     * @param file
     * @param fileType 0：代表正常，1：代表异常
     * @return
     */
    R uploadModelTrainFile(MultipartFile file,String fileType);

    /**
     * 执行模型训练
     * @param modelTrainParam
     * @return
     */
    R modelTrain(ModelTrainParam modelTrainParam);

    /**
     * 获取远程中的正常训练数据文件名
     * @return
     */
    Map<String,List<String>> getRemoteNormalFileName();

    /**
     * 解析模型训练文件（字段、行数、列数等）
     * @param parseVO
     */
    Map<String,Object> parseModelTrainFile(ParseVO parseVO);

    /**
     * 获取远程中的异常训练数据文件名
     * @return
     */
//    List<String> getRemoteAbnormalFileName();
}
