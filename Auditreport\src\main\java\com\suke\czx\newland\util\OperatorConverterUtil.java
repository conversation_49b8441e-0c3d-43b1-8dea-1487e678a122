package com.suke.czx.newland.util;

import static com.suke.czx.newland.consts.OperatorConsts.*;

public class OperatorConverterUtil {

    public static String convert(String s) {
        if (s == null || s.isEmpty()) {
            return "";
        }
        if (s.contains("not_equal")) {
            s = s.replace("not_equal", NOT_EQUAL.getValue());
            return s;
        }  else if (s.contains("greater_than_equal")) {
            s = s.replace("greater_than_equal", GREATER_THAN_EQUAL.getValue());
            return s;
        } else if (s.contains("less_than_equal")) {
            s = s.replace("less_than_equal", LESS_THAN_EQUAL.getValue());
            return s;
        } else if (s.contains("greater_than")) {
            s = s.replace("greater_than", GREATER_THAN.getValue());
            return s;
        } else if (s.contains("less_than")) {
            s = s.replace("less_than", LESS_THAN.getValue());
            return s;
        }else if (s.contains("equal")) {
            s = s.replace("equal", EQUAL.getValue());
            return s;
        }
        return s;
    }
}
