<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.RptCheckLogMapper">


    <select id="qryRptCheckLogWithCondition" resultType="com.suke.czx.newland.vo.audit.AuditLogVo">
        select
            LOG_ID logId,
            DB_NAME dbName,
            DB_MACHINE dbMachine,
            RULE_ID ruleId,
            MODIFY_PERSON modifyPerson,
            MODIFY_DATE modifyDate,
            CHK_TYPE_ID chkTypeId,
            CHK_TYPE_NAME chkTypeName,
            CENTER_ID centerId,
            CENTER_NAME centerName,
            MODLE_ID modleId,
            MODLE_NAME modleName,
            RU<PERSON>_NAME ruleName,
            RULE_SQL ruleSql,
            LIMIT_VALUE limitValue,
            SQL_VALUE sqlValue,
            STATUS status,
            CONCLUSION conclusion,
            EXEC_MSEC execMsec,
            EXEC_REMARK execRemark,
            CREATE_DATE createDate,
            CREATE_PERSON createPerson,
            EXEC_DATE execDate,
            EXEC_PERSON execPerson,
            ANALYSIS_RES analysisRes
        from RPT_CHECK_LOG where
        1 = 1
        <if test="status != null">
            and STATUS = #{status}
        </if>
        <if test="ruleId != null">
            and RULE_ID = #{ruleId}
        </if>
    </select>
    <select id="qryRptCheckLogWithLogIds" resultType="com.suke.czx.newland.vo.audit.AuditLogVo">
        select
        LOG_ID logId,
        DB_NAME dbName,
        DB_MACHINE dbMachine,
        RULE_ID ruleId,
        MODIFY_PERSON modifyPerson,
        MODIFY_DATE modifyDate,
        CHK_TYPE_ID chkTypeId,
        CHK_TYPE_NAME chkTypeName,
        CENTER_ID centerId,
        CENTER_NAME centerName,
        MODLE_ID modleId,
        MODLE_NAME modleName,
        RULE_NAME ruleName,
        RULE_SQL ruleSql,
        LIMIT_VALUE limitValue,
        SQL_VALUE sqlValue,
        STATUS status,
        CONCLUSION conclusion,
        EXEC_MSEC execMsec,
        EXEC_REMARK execRemark,
        CREATE_DATE createDate,
        CREATE_PERSON createPerson,
        EXEC_DATE execDate,
        EXEC_PERSON execPerson,
        ANALYSIS_RES analysisRes
        from RPT_CHECK_LOG where
        LOG_ID in
        <foreach collection="array" item="logId" open="(" close=")" separator=",">
            #{logId}
        </foreach>
    </select>
    <select id="qryAuditLogWithLogIds" resultType="com.suke.czx.newland.dto.AuditLogDownloadRecordDto">
        select
        LOG_ID logId,
        DB_NAME dbName,
        DB_MACHINE dbMachine,
        RULE_ID ruleId,
        MODIFY_PERSON modifyPerson,
        MODIFY_DATE modifyDate,
        CHK_TYPE_ID chkTypeId,
        CHK_TYPE_NAME chkTypeName,
        CENTER_ID centerId,
        CENTER_NAME centerName,
        MODLE_ID modleId,
        MODLE_NAME modleName,
        RULE_NAME ruleName,
        RULE_SQL ruleSql,
        LIMIT_VALUE limitValue,
        SQL_VALUE sqlValue,
        STATUS status,
        CONCLUSION conclusion,
        EXEC_MSEC execMsec,
        EXEC_REMARK execRemark,
        CREATE_DATE createDate,
        CREATE_PERSON createPerson,
        EXEC_DATE execDate,
        EXEC_PERSON execPerson,
        ANALYSIS_RES analysisRes
        from RPT_CHECK_LOG log where 1 = 1
        <if test="ruleName != null and ruleName != ''">
            and log.RULE_NAME like concat(concat('%',#{ruleName, jdbcType=VARCHAR}),'%')
        </if>
        <if test="centerId != null and centerId != ''">
            and log.CENTER_ID = #{centerId}
        </if>
        <if test="modleId != null and modleId != ''">
            and log.MODLE_ID = #{modleId}
        </if>
        <if test="chkTypeId != null and chkTypeId != ''">
            and log.CHK_TYPE_ID = #{chkTypeId}
        </if>
        <if test="createDate != null and endTime != null" >
            and to_char(log.EXEC_DATE,'yyyyMMddhh24miss') between to_char(#{createDate,jdbcType = TIMESTAMP},'yyyyMMddhh24miss')  and  to_char(#{endTime,jdbcType = TIMESTAMP},'yyyyMMddhh24miss')
        </if>
        <if test="conclusion != null and conclusion != ''">
            and log.CONCLUSION = #{conclusion,jdbcType = VARCHAR}
        </if>
        <if test="analysisRes != null and analysisRes != ''">
            and log.ANALYSIS_RES = #{analysisRes}
        </if>
        <if test="datasourceId != null and datasourceId != ''">
            and cfg.ENV_TYPE = #{datasourceId}
        </if>
        <choose>
            <when test="selectAll == true and reverseLogIds.length > 0">
                and 1 = 1 and
                LOG_ID not in
                <foreach collection="reverseLogIds" item="logId" open="(" close=")" separator=",">
                    #{logId}
                </foreach>
            </when>
            <when test="selectAll == true">
                and 1 = 1
            </when>
            <when test="reverseAll == true and logIds.length > 0 and reverseLogIds.length == 0">
                and LOG_ID in
                <foreach collection="logIds" item="logId" open="(" close=")" separator=",">
                    #{logId}
                </foreach>
            </when>
            <when test="reverseAll == true and reverseLogIds.length > 0">
                and LOG_ID not in
                <foreach collection="reverseLogIds" item="logId" open="(" close=")" separator=",">
                    #{logId}
                </foreach>
            </when>
            <otherwise>
                and LOG_ID in
                <foreach collection="logIds" item="logId" open="(" close=")" separator=",">
                    #{logId}
                </foreach>
            </otherwise>
        </choose>
        order by LOG_ID desc
    </select>

    <insert id="insertRptCheckLog" >
        insert into RPT_CHECK_LOG(
            LOG_ID,
            DB_NAME,
            DB_MACHINE,
            RULE_ID,
            MODIFY_PERSON,
            MODIFY_DATE,
            CHK_TYPE_ID,
            CHK_TYPE_NAME,
            CENTER_ID,
            CENTER_NAME,
            MODLE_ID,
            MODLE_NAME,
            RULE_NAME,
            RULE_SQL,
            LIMIT_VALUE,
            SQL_VALUE,
            STATUS,
            CONCLUSION,
            EXEC_MSEC,
            EXEC_REMARK,
            CREATE_DATE,
            CREATE_PERSON,
            EXEC_DATE,
            EXEC_PERSON,
            ANALYSIS_RES
        )values (RPT_CHECK_LOG_SEQ.nextval,
                 #{dbName},
                 #{dbMachine},
                 #{ruleId},
                 #{modifyPerson},
                 #{modifyDate},
                 #{chkTypeId},
                 #{chkTypeName},
                 #{centerId},
                 #{centerName},
                 #{modleId},
                 #{modleName},
                 #{ruleName},
                 #{ruleSql},
                 #{limitValue},
                 #{sqlValue},
                 #{status},
                 #{conclusion},
                 #{execMsec},
                 #{execRemark},
                 #{createDate},
                 #{createPerson},
                 #{execDate},
                 #{execPerson},
                 #{analysisRes})
    </insert>
</mapper>
