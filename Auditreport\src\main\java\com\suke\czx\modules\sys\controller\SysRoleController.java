package com.suke.czx.modules.sys.controller;

import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.Constant;
import com.suke.czx.common.utils.PageUtils;
import com.suke.czx.common.utils.Query;
import com.suke.czx.common.utils.R;
import com.suke.czx.common.validator.ValidatorUtils;
import com.suke.czx.modules.sys.entity.SysMenuEntity;
import com.suke.czx.modules.sys.service.SysMenuService;
import com.suke.czx.modules.sys.service.SysRoleMenuService;
import com.suke.czx.modules.sys.service.SysRoleService;
import com.suke.czx.modules.sys.entity.SysRoleEntity;
import com.suke.czx.modules.sys.service.SysUserRoleService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 角色管理
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年11月8日 下午2:18:33
 */
@RestController
@RequestMapping("/audit/role")
public class SysRoleController extends AbstractController {

	@Autowired
	private SysRoleService sysRoleService;

	@Autowired
	private SysRoleMenuService sysRoleMenuService;

	@Autowired
	private SysUserRoleService sysUserRoleService;

	@Autowired
	private SysMenuService sysMenuService;
	
	/**
	 * 角色列表
	 */
	@RequestMapping(value = "/list",method = RequestMethod.POST)
	// @RequiresPermissions("sys:role:list")
	public R list(@RequestParam Map<String, Object> params){
		//如果不是超级管理员，则只查询自己创建的角色列表
		if(!Objects.equals(getUserId(), Constant.SUPER_ADMIN)){
			params.put("createUserId", getUserId());
		}
		
		//查询列表数据
		Query query = new Query(params);
		List<SysRoleEntity> list = sysRoleService.queryList(query);
		int total = sysRoleService.queryTotal(query);
		
		PageUtils pageUtil = new PageUtils(list, total, query.getLimit(), query.getPage());
		
		return R.ok().put("page", pageUtil);
	}
	
	/**
	 * 角色列表
	 */
	// @SysLog("查询全部角色列表")
	@RequestMapping(value = "/select",method = RequestMethod.GET)
	// @RequiresPermissions("sys:role:select")
	public R select(){
		Map<String, Object> map = new HashMap<>();
		
		//如果不是超级管理员，则只查询自己所拥有的角色列表
		if(!Objects.equals(getUserId(), Constant.SUPER_ADMIN)){
			map.put("createUserId", getUserId());
		}
		List<SysRoleEntity> list = sysRoleService.queryList(map);
		
		return R.ok().put("list", list);
	}
	
	/**
	 * 角色信息
	 */
	@SysLog("根据角色id查询角色信息")
	@RequestMapping(value = "/info/{id}",method = RequestMethod.GET)
	// @RequiresPermissions("sys:role:info")
	public R info(@PathVariable("id") Long id){
		SysRoleEntity role = sysRoleService.queryObject(id);
		
		//查询角色对应的菜单
		// List<Long> menuIdList = sysRoleMenuService.queryMenuIdList(id);

		//给角色设置菜单
		// role.setMenuIdList(menuIdList);
		
		return R.ok().put("role", role);
	}
	
	/**
	 * 保存角色
	 */
	@SysLog("保存角色信息")
	@RequestMapping(value = "/save",method = RequestMethod.POST)
	// @RequiresPermissions("sys:role:save")
	public R save(@RequestBody SysRoleEntity role){
		ValidatorUtils.validateEntity(role);
		//判断角色名称是否在开头加了ROLE_
		if (!role.getRoleName().startsWith("ROLE_")) {
			role.setRoleName("ROLE_" + role.getRoleName());
		}
		role.setCreateUserId(getUserId());
		//查询是否已经存在相同的角色
		Integer res = sysRoleService.queryByRoleName(role.getRoleName());
		if (res > 0) {
			return R.error("角色名称已经存在!");
		}
		sysRoleService.save(role);
		
		return R.ok().put("role",role);
	}
	
	/**
	 * 修改角色
	 */
	@SysLog("修改角色信息")
	@RequestMapping(value = "/update",method = RequestMethod.PUT)
	// @RequiresPermissions("sys:role:update")
	public R update(@RequestBody SysRoleEntity role){
		ValidatorUtils.validateEntity(role);

		//判断角色名称是否在开头加了ROLE_
		if (!role.getRoleName().startsWith("ROLE_")) {
			role.setRoleName("ROLE_" + role.getRoleName());
		}
		role.setCreateUserId(getUserId());
		//根据id查询角色最初的信息
		SysRoleEntity roleInfo = sysRoleService.queryObject(role.getId());
		//最初的角色名称和传递过来的角色名称不同，说明发生了改变
		if (!roleInfo.getRoleName().equals(role.getRoleName())) {
			//根据传递过来的角色名称进行查找是否已经存在此名称
			Integer res = sysRoleService.queryByRoleName(role.getRoleName());
			if (res > 0) {
				return R.error("已经存在此角色名称");
			}
		}
		sysRoleService.update(role);
		
		return R.ok();
	}
	
	/**
	 * 删除角色
	 */
	@SysLog("删除角色信息")
	@RequestMapping(value = "/delete",method = RequestMethod.DELETE)
	// @RequiresPermissions("sys:role:delete")
	public R delete(@RequestParam(value = "id") Long id){
		//先判断此角色下是否存在关联用户，若存在用户，则不允许删除
		Integer sum = sysUserRoleService.queryRoleById(id);
		if (sum != 0) {
			return R.error().put("result","该角色下还存在关联用户，不允许删除!");
		}
		//再判断此角色下是否存在绑定的菜单，若都不存在，则进行删除
		Integer res = sysRoleMenuService.queryRoleById(id);
		if (res != 0) {
			return R.error().put("result","该角色下还存在关联菜单，不允许删除!");
		}
		Long[] roleIds = new Long[1];
		roleIds[0] = id;
		sysRoleService.deleteBatch(roleIds);
		return R.ok();
	}

	/**
	 * 根据角色id查询菜单信息
	 * @param id
	 * @return
	 */
	@RequestMapping(value = "queryMenuIds",method = RequestMethod.GET)
	public R getMenusByRoleId(@RequestParam(value = "id") Long id) {
		//查询角色对应的菜单
		List<Long> menuIdList = sysRoleMenuService.queryMenuIdList(id);
		//返回之前全部去掉一级菜单的id展示
		List<SysMenuEntity> queryList = sysMenuService.queryList(new HashMap<>());
		List<Long> parentIds = queryList.stream().filter(item -> item.getParentId() == 0).map(SysMenuEntity::getId).collect(Collectors.toList());
		for (int i = 0; i < parentIds.size(); i++) {
			int finalI = i;
			//去除掉父id
			menuIdList.removeIf(item -> item.equals(parentIds.get(finalI)));
		}
		return R.ok().put("result",menuIdList);
	}

	/**
	 * 在角色菜单关联表中保存权限菜单信息
	 * @param ids
	 * @return
	 */
	@SysLog("在关联表中保存权限菜单信息")
	@RequestMapping(value = "assignMenu/{roleId}",method = RequestMethod.POST)
	public R assignMenu(@PathVariable(name = "roleId") Long roleId, @RequestBody Long[] ids) {
		// System.out.println("roleId:" + roleId);
		// 使用set集合达到去重的目的
		Set<Long> allMenuId = new HashSet<>();
		//根据ids查询每一个菜单的父id
		for (int i = 0; i < ids.length; i++) {
			Long parentId = sysMenuService.queryMenuParentId(ids[i]);
			//不是根节点0的加入set中
			if (parentId != 0) {
				allMenuId.add(parentId);
				allMenuId.add(ids[i]);
			}
		}
		// System.out.println("set:" + allMenuId);
		//完整的菜单id
		Long[] menuIds = new Long[allMenuId.size()];
		for (int i = 0; i < allMenuId.size(); i++) {
			menuIds[i] = (Long) allMenuId.toArray()[i];
		}
		//先根据roleId将关联表中的数据删除再进行添加操作(在serviceImpl层完成)
		//然后再进行保存操作
		// sysRoleMenuService.saveOrUpdate(roleId,menuIdList);

		sysRoleMenuService.saveRoleMenu(roleId,menuIds);
		// System.out.println("res:" + Arrays.toString(menuIds));
		return R.ok();
	}
}
