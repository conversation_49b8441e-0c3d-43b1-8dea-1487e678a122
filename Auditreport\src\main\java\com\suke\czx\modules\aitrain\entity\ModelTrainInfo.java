package com.suke.czx.modules.aitrain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 模型训练实体类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ModelTrainInfo {

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 训练数据文件的字段map
     */
    private Map<String,Integer> fileField;

    /**
     * 文件总的列数
     */
    private int totalColumnNum;

    /**
     * 文件总的行数
     */
    private int totalRowNum;

    /**
     * 存在50%以上字段为空的行数
     */
    private int nullCellsOfRowNum;

    /**
     * 字符串类型的单元格个数
     */
    private int stringTypeNum;

    /**
     * 数字类型的单元格个数
     */
    private int numberTypeNum;

}
