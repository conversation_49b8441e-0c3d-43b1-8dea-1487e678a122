package com.suke.czx.newland.vo.audit;

import lombok.Data;

@Data
public class AuditExecVo {

    private int id;
    private String name;
    private String status;
    private String datasourceId;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getDatasourceId() {
        return datasourceId;
    }

    public void setDatasourceId(String datasourceId) {
        this.datasourceId = datasourceId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "AuditExecVo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}