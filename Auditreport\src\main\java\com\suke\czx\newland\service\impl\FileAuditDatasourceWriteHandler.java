package com.suke.czx.newland.service.impl;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.AbstractCellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.suke.czx.newland.dto.ExcelColorDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class FileAuditDatasourceWriteHandler extends AbstractCellWriteHandler {

    private final Map<String, IndexedColors> cellColorMap;

    public FileAuditDatasourceWriteHandler(List<ExcelColorDto> excelColors) {
        cellColorMap = excelColors
                .stream()
                .collect(Collectors.toMap(e -> String.valueOf(e.getRowNo()) +
                        e.getColumnNo(), ExcelColorDto::getColor));
    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        int rowNum = cell.getRow().getRowNum();
        int column = cell.getColumnIndex();
        String index = rowNum + String.valueOf(column);
        if (cellColorMap.containsKey(index)) {
            IndexedColors color = cellColorMap.get(index);
            // 获取工作簿对象
            Workbook workbook = writeSheetHolder.getSheet().getWorkbook();

            // 创建新的单元格样式
            CellStyle cellStyle = workbook.createCellStyle();


            cellStyle.setFillForegroundColor(color.getIndex());
            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 将样式应用到当前单元格
            cell.setCellStyle(cellStyle);
        }


    }
}
