package com.suke.czx.modules.AI.dao;

import com.suke.czx.modules.AI.entity.AiFile;
import com.suke.czx.modules.AI.entity.AuditAiFileQryCondition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AiFileDao extends BaseDao<AiFile> {
    List<AiFile> qyrAuditAiFileWithCondition(AuditAiFileQryCondition condition);

    AiFile qyrAuditAiFileWithId(String id);

    List<AiFile> qryAuditAiFileWithName(String name);

    List<AiFile> qyrAuditAiFileWithIds(@Param("ids") List<String> ids);

    void delByIds(@Param("ids") List<String> ids);

    AiFile qryAuditedAiFileBySourceFileId(@Param("sourceFileId") String sourceFileId);

    void updateAiFileTypesByFileId(String id);

    void insertAuditedAIFileInfo(AiFile aiFile);
}
