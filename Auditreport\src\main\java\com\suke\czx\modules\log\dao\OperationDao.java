package com.suke.czx.modules.log.dao;

import com.suke.czx.modules.log.entity.OperationEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface OperationDao extends BaseDao<OperationEntity>{
    /**
     * 查询所有日志列表
     * @return
     */
    List<OperationEntity> queryLogList(@Param("userName") String userName, @Param("operation") String operation);
}
