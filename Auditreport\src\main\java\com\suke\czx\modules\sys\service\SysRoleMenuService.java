package com.suke.czx.modules.sys.service;

import java.util.List;



/**
 * 角色与菜单对应关系
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:42:30
 */
public interface SysRoleMenuService {
	
	void saveOrUpdate(Long roleId, List<Long> menuIdList);
	
	/**
	 * 根据角色ID，获取菜单ID列表
	 */
	List<Long> queryMenuIdList(Long roleId);

	/**
	 * 根据角色id查询角色角色菜单管理表中是否存在关联数据，若存在，则不允许删除
	 * @param id
	 * @return
	 */
    Integer queryRoleById(Long id);

	/**
	 * 保存角色菜单信息
	 * @param roleId
	 * @param menuIds
	 */
	void saveRoleMenu(Long roleId, Long[] menuIds);

	/**
	 * 根据菜单id查询角色菜单关联表
	 * @param menuId
	 * @return
	 */
	Integer queryTotal(Long menuId);
}
