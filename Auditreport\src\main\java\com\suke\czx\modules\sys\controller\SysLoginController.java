package com.suke.czx.modules.sys.controller;

import com.google.code.kaptcha.Constants;
import com.google.code.kaptcha.Producer;
import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.utils.MD5;
import com.suke.czx.common.utils.R;
import com.suke.czx.common.utils.ShiroUtils;
import com.suke.czx.modules.sys.dto.LoginDto;
import com.suke.czx.modules.sys.entity.SysUserEntity;
import com.suke.czx.modules.sys.service.SysUserService;
import com.suke.czx.modules.sys.service.SysUserTokenService;
import com.suke.czx.util.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.authc.LockedAccountException;
import org.apache.shiro.authc.UnknownAccountException;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.crypto.hash.Sha256Hash;
import org.apache.shiro.crypto.hash.Sha384Hash;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.time.Instant;
import java.util.Locale;
import java.util.Map;

/**
 * 登录相关
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年11月10日 下午1:15:31
 */
@Slf4j
@RestController
public class SysLoginController extends AbstractController {
	@Autowired
	private Producer producer;
	@Autowired
	private SysUserService sysUserService;
	@Autowired
	private SysUserTokenService sysUserTokenService;

	@Resource
	private JwtUtil jwtUtil;

	/**
	 * 验证码
	 */
	@RequestMapping("captcha.jpg")
	public void captcha(HttpServletResponse response)throws ServletException, IOException {
		response.setHeader("Cache-Control", "no-store, no-cache");
		response.setContentType("image/jpeg");

		//生成文字验证码
		String text = producer.createText();
		//生成图片验证码
		BufferedImage image = producer.createImage(text);
		//保存到shiro session
		ShiroUtils.setSessionAttribute(Constants.KAPTCHA_SESSION_KEY, text);

		ServletOutputStream out = response.getOutputStream();
		ImageIO.write(image, "jpg", out);
		IOUtils.closeQuietly(out);
	}

	/**
	 * 登录
	 */
	@SysLog("登录系统")
	@RequestMapping(value = "/sys/login", method = RequestMethod.POST)
	public Map<String, Object> login(@RequestBody LoginDto loginDto)throws IOException {
		//本项目已实现，前后端完全分离，但页面还是跟项目放在一起了，所以还是会依赖session
		//如果想把页面单独放到nginx里，实现前后端完全分离，则需要把验证码注释掉(因为不再依赖session了)
		// String kaptcha = ShiroUtils.getKaptcha(Constants.KAPTCHA_SESSION_KEY);
		// if(!captcha.equalsIgnoreCase(kaptcha)){
		// 	return R.error("验证码不正确");
		// }

		String username = loginDto.getUsername();
		String password = loginDto.getPassword();
		//用户信息
		SysUserEntity user = sysUserService.queryByUserName(username);

		//账号不存在、密码错误
		if(user == null || user.getPassword() == null) {
			return R.error("账号或密码不正确");
		}


		// SysUserEntity user = new SysUserEntity();
		//
		// try {
		// 	String username = loginDto.getUsername();
		// 	String password = loginDto.getPassword();
		//
		// 	Subject subject = SecurityUtils.getSubject();
		 //	UsernamePasswordToken token = new UsernamePasswordToken(username, password);
		// 	subject.login(token);
		//
		// 	Object principal = subject.getPrincipal();
		//
		// 	if (principal instanceof SysUserEntity) {
		// 		user = (SysUserEntity) principal;
		// 	} else {
		// 		throw new RuntimeException("获取用户信息失败");
		// 	}
		// }catch(UnknownAccountException uae){
		// 	return R.error("未知账户");
		// 	// redirectAttributes.addFlashAttribute("message", "未知账户");
		// }catch(IncorrectCredentialsException ice){
		// 	return R.error("账户或者密码错误");
		// 	//redirectAttributes.addFlashAttribute("message", "密码不正确");
		// }catch(LockedAccountException lae) {
		// 	return R.error("账户已经锁定");
		// }catch (Exception e){
		// 	return R.error(e.getMessage());
		// }


		//账号不存在、密码错误
//		if(user == null || !password.equals(MD5.md5(user.getPwStr()))) {
//			return R.error("账号或密码不正确");
//		}
		//账号不存在、密码错误
		if(user == null){
			return R.error("账号和密码不能为空");
		}
		if(!password.equals(user.getPassword()) || !username.equals(user.getUserName())) {
			return R.error("账号或密码不正确");
		}

		R r = sysUserTokenService.createToken(user.getUserId());
		return r;
	}

	/**
	 * 星斗跳转验证url地址中的token
	 * @param token
	 * @return
	 */
	@GetMapping("/sys/sso")
	public Map<String, Object> sso(@RequestParam String token) {
		log.info("星斗跳转验证url地址中的token:{}", token);
		Map<String, Object> map = jwtUtil.parseToken(token);
		//验证是否过期
		boolean expired = isTokenExpired(map);
		//验证用户是否是huxy
		boolean validUser = isValidUser(map);
		log.info("验证是否过期:{}", expired);
		log.info("验证用户是否是huxy:{}", validUser);
		if (expired || !validUser) {
			return R.error("token无效");
		}
		//创建token
        return sysUserTokenService.createToken("liuning");
	}


	/**
	 * 退出
	 */
	@SysLog("退出系统")
	@RequestMapping(value = "/sys/logout", method = RequestMethod.POST)
	public R logout() {
		sysUserTokenService.logout(getUserId());
		return R.ok();
	}

	/**
	 * 判断 token 是否过期
	 */
	private boolean isTokenExpired(Map<String, Object> tokenClaims) {
		long expirationTime = getExpirationTime(tokenClaims);
		return expirationTime < Instant.now().getEpochSecond();
	}

	/**
	 * 获取 token 的过期时间
	 */
	private long getExpirationTime(Map<String, Object> tokenClaims) {
		Object expObj = tokenClaims.get("exp");
		if (expObj instanceof Integer) {
			return ((Integer) expObj).longValue();
		} else if (expObj instanceof Long) {
			return (Long) expObj;
		} else {
			log.warn("Unexpected exp type: {}", expObj != null ? expObj.getClass() : "null");
			return 0;
		}
	}

	/**
	 * 判断用户名是否正确
	 */
	private boolean isValidUser(Map<String, Object> tokenClaims) {
		Object userObj = tokenClaims.get("user");
		if (userObj instanceof String) {
			return "liuning".equals(userObj);
		}
		return false;
	}
	
}
