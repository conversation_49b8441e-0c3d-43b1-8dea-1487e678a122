package com.suke.czx.modules.audit.service.impl;

import com.alibaba.druid.util.Utils;
import com.suke.czx.modules.audit.dao.AuditComparisonDao;
import com.suke.czx.modules.audit.entity.AuditComparison;
import com.suke.czx.modules.audit.util.ComparisonUtil;
import com.suke.czx.modules.audit.util.FTPUtil;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 异步调用的比对方法
 */
@Slf4j
@Component
public class ComparisonService {

    @Autowired
    private AuditComparisonDao auditComparisonDao;

    /**
     * 解析inputStream，并在工具类中进行比对
     *
     * @param inputStream    输入流
     * @param ruleConfigList 需要比对的规则列表
     * @param fileName       报表名称
     * @param request        请求（未用到）
     * @param response       响应（未用到）
     * @throws InterruptedException 中断异常
     */
    @Async("taskExecutor")
    public void resolveReport(InputStream inputStream, List<AuditRuleConfig> ruleConfigList, String fileName, HttpServletRequest request, HttpServletResponse response) throws InterruptedException {
        log.info("当前线程：{}", Thread.currentThread().getName());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("status", 0); //对比初始状态 未对比
        resultMap.put("count", 0);
        try {
            //创建工作簿
            Workbook workbook = WorkbookFactory.create(inputStream);
            //获取第一个sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 创建公式求值器
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            //创建单元格的样式（背景是红色）在工具类中进行背景设置
            CellStyle cellStyle = workbook.createCellStyle();
            //根据fileName查询比对信息
            AuditComparison auditComparison = auditComparisonDao.queryObject(fileName);
            //比对状态 0：未比对，1：比对中，2：比对完成，3：出现异常，4：比对异常
            auditComparison.setStatus(1);
            LocalDateTime startTime = LocalDateTime.now();
            auditComparison.setCreateTime(startTime);
            System.out.println("比对开始前更新比对信息：" + auditComparison);
            //更新比对状态和开始比对时间
            auditComparisonDao.update(auditComparison);
            List<Integer> statusInfo = new ArrayList<>();

            for (AuditRuleConfig item : ruleConfigList) {
                //调用工具类进行比对
                Map<String, Object> map = ComparisonUtil.compareAndComparison(sheet, item, resultMap, cellStyle);
                statusInfo.add((Integer) map.get("status"));
            }
            if (statusInfo.contains(3)) {
                //出现异常
                resultMap.put("status", 3);
            } else if (statusInfo.contains(4)) {
                //比对异常
                resultMap.put("status", 4);
            } else {
                resultMap.put("status", 2);
            }

            auditComparison.setStatus((int) resultMap.get("status"));
            auditComparison.setComparisonTimes(Long.valueOf(resultMap.get("count").toString()));
            LocalDateTime endTime = LocalDateTime.now();
            auditComparison.setEndTime(endTime);
            Duration duration = Duration.between(startTime, endTime);
            long spendTime = duration.toMillis();
            auditComparison.setSpendTime(spendTime);
            System.out.println("比对完成后更新比对信息：" + auditComparison);
            //更新比对信息
            auditComparisonDao.update(auditComparison);

            //返回比对结果
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            workbook.write(os);
            byte[] bytes = os.toByteArray();
            //比对后的workbook上传到服务器
            FTPUtil.uploadComparisonFile(bytes, fileName);

            System.out.println("==========字节的大小:" + bytes.length);
        } catch (Exception e) {
            log.error(Utils.getStackTrace(e));
            //比对异常
            resultMap.put("status", 4);
        }
    }
}
