package com.suke.czx.newland.controller;

import com.suke.czx.modules.rule.util.DownloadExcelUtil;
import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.dto.AuditLogDownloadRecordDto;
import com.suke.czx.newland.service.IAuditService;
import com.suke.czx.newland.util.OperatorConverterUtil;
import com.suke.czx.newland.vo.AuditLogDownloadVo;
import com.suke.czx.newland.vo.EditAuditLogVo;
import com.suke.czx.newland.vo.LogIdsVo;
import com.suke.czx.newland.vo.RuleIdsVo;
import com.suke.czx.newland.vo.audit.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;

@RestController
@RequestMapping("/audit")
public class AuditController {

    @Autowired
    private IAuditService iAuditService;

    /**
     * 查询稽核配置项
     *
     * @param auditDictTypeVo
     * @return
     */
    @PostMapping("/addAuditType")
    public Result addAuditType(@RequestBody AuditDictTypeVo auditDictTypeVo) {
        return iAuditService.addAuditType(auditDictTypeVo);
    }


    /**
     * 查询稽核配置项
     *
     * @param auditConfigVo
     * @return
     */
    @PostMapping("/queryAuditConfig")
    public Result queryAuditConfig(@RequestBody AuditQueryVo auditConfigVo) {
        return iAuditService.queryAuditConfig(auditConfigVo);
    }

    @PostMapping("/queryAuditConfigWithRuleIds")
    public Result queryAuditConfigWithRuleIds(@RequestBody RuleIdsVo ruleIds) {
        return iAuditService.queryAuditConfigWithRuleIds(ruleIds.getRuleIds());
    }

    /**
     * 查询稽核项Code
     *
     * @return
     */
    @PostMapping("/queryAuditCode")
    public Result queryAuditCode() {
        return iAuditService.queryAuditCode();
    }

    @PostMapping("/saveExecRemark")
    public Result saveExecRemark(@RequestBody AuditLogVo logVo) {
        return iAuditService.saveExecRemark(logVo);
    }

//    @GetMapping("/queryHisConfig")
//    public Result queryHisConfig(long ruleId) {
//        return iAuditService.queryHisConfig(ruleId);
//    }

    @PostMapping("/saveAnalysisRes")
    public Result saveAnalysisRes(@RequestBody AuditLogVo logVo) {
        return iAuditService.saveAnalysisRes(logVo);
    }

    /**
     * 查询稽核中心
     *
     * @return
     */
    @PostMapping("/queryAuditCenter")
    public Result queryAuditCenter() {
        return iAuditService.queryAuditCenter();
    }

    /**
     * 删除稽核配置项
     *
     * @return
     */
    @PostMapping("/delAuditCenter")
    public Result delAuditCenter(String ruleId) {
        return iAuditService.delAuditConfig(ruleId);
    }


    /**
     * 新增稽核配置项
     *
     * @return
     */
    @PostMapping("/addAuditConfig")
    public Result addAuditConfig(@RequestBody AuditConfigVo auditConfigVo) {
        operatorConvert(auditConfigVo);
        return iAuditService.eidtAuditConfig(auditConfigVo, false);
    }
    private void operatorConvert(AuditConfigVo auditConfigVo){
        if (auditConfigVo == null){
            return;
        }
        auditConfigVo.setLimitSign(OperatorConverterUtil.convert(auditConfigVo.getLimitSign()));
        auditConfigVo.setLimitValue(OperatorConverterUtil.convert(auditConfigVo.getLimitValue()));
    }

    /**
     * 修改稽核配置项
     *
     * @return
     */
    @PostMapping("/editAuditConfig")
    public Result editAuditConfig(@RequestBody AuditConfigVo auditConfigVo) {
        operatorConvert(auditConfigVo);
        return iAuditService.eidtAuditConfig(auditConfigVo, true);
    }

    /**
     * 批量执行稽核
     *
     * @return
     */
    @PostMapping("/execAuditProcess")
    public Result execAuditProcess(@RequestBody AuditExecInfoVo execInfoVo) {
        return iAuditService.execProcess(execInfoVo.getCfgIds(), execInfoVo.getExecPerson(), execInfoVo.getRunDatasourceId());
    }

    /**
     * 批量执行稽核
     *
     * @return
     */
    @PostMapping("/queryExecStatus")
    public Result queryExecStatus() {
        return iAuditService.queryExecStatus();
    }

    /**
     * 查询字典
     *
     * @return
     */
    @PostMapping("/queryDictType")
    public Result queryDictType() {
        return iAuditService.queryDictType();
    }


    /**
     * 删除字典
     *
     * @return
     */
    @PostMapping("/delAuditDictByDictId")
    public Result delAuditDictByDictId(Integer dictId) {
        return iAuditService.delAuditDictByDictId(dictId);
    }

    /**
     * 新增字典
     *
     * @return
     */
    @PostMapping("/addAuditDict")
    public Result addAuditDict(@RequestBody AuditDictVo dict) {
        return iAuditService.addAuditDict(dict);
    }

    /**
     * 新增字典
     *
     * @return
     */
    @PostMapping("/editAuditDict")
    public Result editAuditDict(@RequestBody AuditDictVo dict) {
        return iAuditService.editAuditDict(dict);
    }

    /**
     * 查询日志
     *
     * @return
     */
    @PostMapping("/queryAuditLogWithPageInfo")
    public Result queryAuditLogWithPageInfo(@RequestBody AuditLogQryVo condition, @RequestParam int pageIndex, @RequestParam int pageSize) {
        return iAuditService.queryAuditLogWithPageInfo(condition, pageIndex, pageSize);
    }

    @PostMapping("/queryAuditLogWithLogIds")
    public Result queryAuditLogWithLogIds(@RequestBody LogIdsVo logIdsVo){
        return iAuditService.queryAuditLogWithLogIds(logIdsVo.getLogIds());
    }

    @PostMapping("/downloadAuditLogs")
    public ResponseEntity<Object> downloadAuditLogs(@RequestBody AuditLogDownloadVo logIdsVo) throws UnsupportedEncodingException {
        List<AuditLogDownloadRecordDto> res = iAuditService.downloadAuditLogs(logIdsVo);
        byte[] resBytes = DownloadExcelUtil.exportAuditLogToExcel(res);
        String filename = "稽核日志信息.xlsx";
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode(filename, "UTF-8")); // 设置附件名和下载属性
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE); // 设置内容类型为二进制流，以支持文件下载
        return new ResponseEntity<>(resBytes, headers, HttpStatus.OK);
    }

    @PostMapping("/downloadAuditRules")
    public ResponseEntity<Object> downloadAuditRules(@RequestBody RuleIdsVo ruleIds) throws UnsupportedEncodingException {
        Result res = iAuditService.queryAuditConfigWithRuleIds(ruleIds.getRuleIds());
        List<AuditConfigVo> data = (List<AuditConfigVo>) res.getData();
        byte[] resBytes = DownloadExcelUtil.exportAuditRuleToExcel(data);
        HttpHeaders headers = new HttpHeaders();
        headers.add(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + URLEncoder.encode("稽核规则.xlsx", "UTF-8")); // 设置附件名和下载属性
        headers.add(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE); // 设置内容类型为二进制流，以支持文件下载
        return new ResponseEntity<>(resBytes, headers, HttpStatus.OK);
    }

    @PostMapping("/editAuditLogAnalysisRes")
    public Result editAuditLogAnalysisRes(@RequestBody EditAuditLogVo param) {
        return iAuditService.editAuditLogAnalysisRes(param);
    }


    /**
     * 查询日志
     *
     * @return
     */
    @PostMapping("/queryAuditSql")
    public Result queryAuditSql(@RequestBody AuditConfigVo auditConfigVo) {
        return iAuditService.queryAuditSql(auditConfigVo.getRuleSql());
    }


    /**
     * @param ruleId
     * @param modifyPerson
     * @param ruleName
     * @return TODO 新增
     * <AUTHOR>
     */
    @GetMapping("/queryHisConfig")
    public Result queryHisConfig(@RequestParam("ruleId") long ruleId, @RequestParam(defaultValue = "") String modifyPerson, @RequestParam(defaultValue = "") String ruleName) {
        return iAuditService.queryHisConfig(ruleId, modifyPerson, ruleName);
    }

    @PostMapping("/queryJobStatus")
    public Result queryJobStatus() {
        return iAuditService.queryJobStatus();
    }

}
