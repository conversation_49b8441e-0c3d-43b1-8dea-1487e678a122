<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.suke.czx.modules.sys.dao.SysUserDao">
	<!-- <select id="queryObject" resultType="com.suke.czx.modules.sys.entity.SysUserEntity"> -->
	<!-- 	select * from auto_user where user_id = #{value} -->
	<!-- </select> -->

	<select id="queryObject" resultMap="userInfo">
		select au.user_id,
			   au.user_name,
			   au.password,
			   au.create_time,
			   au.pw_str,
			   au.platform_name,
			   au.platform_create_id,
			   au.org_id,
			   au.org_name,
			   au.parent_org_id,
			   au.systemid,
			   ar.id,
			   ar.role_name,
			   ar.remark,
			   ar.create_user_id
		from auto_user au
				 left join audit_user_role ur on au.user_id=ur.user_id
				 left join audit_role ar on ur.role_id=ar.id
		where au.user_id = #{value}
	</select>

	<resultMap id="userInfo" type="com.suke.czx.modules.sys.entity.SysUserEntity">
		<id column="user_id" property="userId"/>
		<result column="user_name" property="userName"/>
		<result column="password" property="password"/>
		<result column="create_time" property="createTime"/>
		<result column="pw_str" property="pwStr"/>
		<result column="platform_name" property="platformName"/>
		<result column="platform_create_id" property="platformCreateId"/>
		<result column="org_id" property="orgId"/>
		<result column="org_name" property="orgName"/>
		<result column="parent_org_id" property="parentOrgId"/>
		<result column="systemid" property="systemId"/>
		<collection property="role" ofType="com.suke.czx.modules.sys.entity.SysRoleEntity">
			<id column="id" property="id"/>
			<result column="role_name" property="roleName"/>
			<result column="remark" property="remark"/>
			<result column="create_user_id" property="createUserId"/>
		</collection>
	</resultMap>

	<select id="queryList" resultType="com.suke.czx.modules.sys.entity.SysUserEntity">
		select * from auto_user
		<where>
			<if test="username != null and username.trim() != ''">
				and user_name like concat(concat('%',#{username}),'%')
			</if>
		</where>
		order by user_id
		<!-- <if test="offset != null and limit != null"> -->
		<!-- 	offset #{offset} rows fetch next #{limit} rows only -->
		<!-- </if> -->
	</select>
	
	<select id="queryTotal" resultType="int">
		select count(*) from auto_user
		<where>
			<if test="username != null and username.trim() != ''">
				and user_name like concat('%',#{username},'%')
			</if>
		</where>
	</select>
	
	<!-- 查询用户的所有权限 -->
	<select id="queryAllPerms" resultType="string">
		select m.perms from audit_user_role ur
			LEFT JOIN audit_role_menu rm on ur.role_id = rm.role_id
			LEFT JOIN audit_menu m on rm.menu_id = m.id
		where ur.user_id = #{userId}
	</select>
	
	<!-- 查询用户的所有菜单ID --> 
	<select id="queryAllMenuId" resultType="long">
		select distinct rm.menu_id from audit_user_role ur
			LEFT JOIN audit_role_menu rm on ur.role_id = rm.role_id
		where ur.user_id = #{userId}
	</select>
	
	<select id="queryByUserName" resultType="com.suke.czx.modules.sys.entity.SysUserEntity">
		select * from auto_user where user_name = #{username}
	</select>
	<select id="qryUserListByCondition" resultType="com.suke.czx.modules.sys.entity.SysUserEntity">
		select distinct au.* from auto_user au inner join audit_user_role ur on au.user_id = ur.user_id
		where 1 = 1
		<if test="param.userName != null and param.userName != ''">
			and user_name = #{param.userName}
		</if>
		<if test="roleIds != null and roleIds.length > 0">
			and ur.ROLE_ID in
			<foreach collection="roleIds" separator="," open="(" close=")" item="item">
				#{item}
			</foreach>
		</if>
		order by au.CREATE_TIME desc
	</select>
	<select id="qryUserRoleByUserId" resultType="java.lang.String">
		select ROLE_ID from audit_user_role where user_id = #{userId}
	</select>

	<insert id="save" parameterType="com.suke.czx.modules.sys.entity.SysUserEntity" useGeneratedKeys="true" keyProperty="userId">
		insert into auto_user
		(
			user_id,
			user_name,
			password,
			pw_str,
			mobile,
			CREATE_TIME
		)
		values
		(
			#{userId}, 
			#{userName},
			#{password},
			#{pwStr},
			#{mobile},
		 	#{createTime}
		)
	</insert>
	 
	<update id="update" parameterType="com.suke.czx.modules.sys.entity.SysUserEntity">
		update auto_user
		<set> 
			<if test="username != null">user_name = #{username}, </if>
			<if test="password != null">password = #{password}, </if>
		</set>
		where user_id = #{userId} 
	</update>
	
	<update id="updatePassword" parameterType="map">
		update auto_user set password = #{newPassword}
			where user_id = #{userId} and password = #{password}
	</update>
	
	<delete id="deleteBatch">
		delete from auto_user where user_id in
		<foreach item="userId" collection="array" open="(" separator="," close=")">
			#{userId}
		</foreach>
		;
		delete from audit_user_role where user_id in
		<foreach item="userId" collection="array" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</delete>
	<delete id="delAutoUserInfoByUserIds">
		delete from auto_user where user_id in
		<foreach item="userId" collection="array" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</delete>
	<delete id="delAutoUserRoleByUserIds">
		delete from audit_user_role where user_id in
		<foreach item="userId" collection="array" open="(" separator="," close=")">
			#{userId}
		</foreach>
	</delete>

</mapper>