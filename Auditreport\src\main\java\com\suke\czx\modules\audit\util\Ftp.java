package com.suke.czx.modules.audit.util;

import com.alibaba.druid.util.Utils;
import com.jcraft.jsch.*;
import com.jcraft.jsch.ChannelSftp.LsEntry;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;

import java.io.*;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

public class Ftp {
    //打印log日志
    private static final Log logger = LogFactory.getLog(Ftp.class);
    private static Date last_push_date = null;
    private Session sshSession;
    private ChannelSftp channel;
    private String host;//服务器连接ip
    private String username;//用户名
    private String password;//密码
    private int port = 22;//端口号

    public Ftp(String host, int port, String username, String password) throws Exception {
        JSch jsch = new JSch();
        jsch.getSession(username, host, port);
        //根据用户名，密码，端口号获取session
        sshSession = jsch.getSession(username, host, port);
        this.host = host;
        this.username = username;
        this.password = password;

        sshSession.setPassword(password);
        //修改服务器/etc/ssh/sshd_config 中 GSSAPIAuthentication的值yes为no，解决用户不能远程登录
        sshSession.setConfig("userauth.gssapi-with-mic", "no");
        //为session对象设置properties,第一次访问服务器时不用输入yes
        sshSession.setConfig("StrictHostKeyChecking", "no");
        sshSession.connect();
        //获取sftp通道
        channel = (ChannelSftp) sshSession.openChannel("sftp");
        channel.connect();
        logger.info("连接sftp成功!" + sshSession);
    }

    /**
     * 获取远程指定目录下的所有文件
     * @param normalPath 正常文件路径
     * @param abnormalPath 异常文件路径
     * @return
     */
    public Map<String,List<String>> getRemoteFileName(String normalPath,String abnormalPath) {
        Map<String,List<String>> map = new HashMap<>();
        try {
            //获取正常文件夹下的列表
            channel.cd(normalPath);
            Vector<ChannelSftp.LsEntry> normalList = channel.ls("*");
            // 逆序排序，最新修改的文件在前
            Collections.sort(normalList,new Comparator<ChannelSftp.LsEntry>() {
                @Override
                public int compare(LsEntry o1, LsEntry o2) {
                    return Long.compare(o2.getAttrs().getMTime(),o1.getAttrs().getMTime());
                }
            });
            List<String> fileList1 = new ArrayList<>();
            for (LsEntry entry : normalList) {
                int mTime = entry.getAttrs().getMTime();
//                logger.info("文件名称1：" + entry.getFilename() + " 修改时间：" + mTime);
                fileList1.add(entry.getFilename());
                map.put("normal",fileList1);
            }
            //获取异常文件夹下的列表
            channel.cd(abnormalPath);
            Vector<ChannelSftp.LsEntry> abNormalList = channel.ls("*");
            Collections.sort(abNormalList,new Comparator<ChannelSftp.LsEntry>() {
                @Override
                public int compare(LsEntry o1, LsEntry o2) {
                    return Long.compare(o2.getAttrs().getMTime(),o1.getAttrs().getMTime());
                }
            });
            List<String> fileList2 = new ArrayList<>();
            for (LsEntry entry : abNormalList) {
                int mTime = entry.getAttrs().getMTime();
//                logger.info("文件名称2：" + entry.getFilename() + " 修改时间：" + mTime);
                fileList2.add(entry.getFilename());
                map.put("abnormal",fileList2);
            }
            return map;
        } catch (SftpException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 是否已连接
     *
     * @return
     */
    private boolean isConnected() {
        return null != channel && channel.isConnected();
    }


    /**
     * 关闭通道
     *
     * @throws Exception
     */
    public void closeChannel() {
        if (null != channel) {
            try {
                channel.disconnect();
            } catch (Exception e) {
                logger.error("关闭SFTP通道发生异常:", e);
            }
        }
        if (null != sshSession) {
            try {
                sshSession.disconnect();
            } catch (Exception e) {
                logger.error("SFTP关闭 session异常:", e);
            }
        }
    }

    /**
     * 情况对应路径下的文件
     *
     * @param uploadFile 本地文件路径（不带文件名）
     */
    public void removeFile(String uploadFile) {
        List<File> files = getFiles(uploadFile, new ArrayList<File>());
        for (int i = 0; i < files.size(); i++) {
            File file = files.get(i);
            if (file.exists()) {
                boolean b = file.delete();
                logger.info(file.getName() + "删除标识:" + b);
            }
        }
    }

    /**
     * @param directory  上传ftp的目录
     * @param uploadFile 本地文件目录
     */
    public void upload(String directory, String uploadFile) throws Exception {
        try {
            //执行列表展示ls 命令
            channel.ls(directory);
            logger.info("进入对方服务器文件夹：");
            //执行盘符切换cd 命令
            channel.cd(directory);
            logger.info("切换盘符：" + directory);
            List<File> files = getFiles(uploadFile, new ArrayList<File>());
            for (int i = 0; i < files.size(); i++) {
                File file = files.get(i);
                InputStream input = new BufferedInputStream(new FileInputStream(file));
                channel.put(input, file.getName());
                try {
                    if (input != null) input.close();
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error(file.getName() + "关闭文件时.....异常!" + e.getMessage());
                }
                if (file.exists()) {
                    boolean b = file.delete();
                    logger.info(file.getName() + "文件上传完毕!删除标识:" + b);
                }
            }
        } catch (Exception e) {
            logger.info("报错信息：" + e.getMessage());
            logger.error("【子目录创建中】：", e);
            //创建子目录
            channel.mkdir(directory);
        }
    }

    //获取文件
    public List<File> getFiles(String realpath, List<File> files) {
        File realFile = new File(realpath);
        if (realFile.isDirectory()) {
            File[] subfiles = realFile.listFiles(new FileFilter() {
                @Override
                public boolean accept(File file) {
                    if (null == last_push_date) {
                        return true;
                    } else {
                        long modifyDate = file.lastModified();
                        return modifyDate > last_push_date.getTime();
                    }
                }
            });
            for (File file : subfiles) {
                if (file.isDirectory()) {
                    getFiles(file.getAbsolutePath(), files);
                } else {
                    files.add(file);
                }
                if (null == last_push_date) {
                    last_push_date = new Date(file.lastModified());
                } else {
                    long modifyDate = file.lastModified();
                    if (modifyDate > last_push_date.getTime()) {
                        last_push_date = new Date(modifyDate);
                    }
                }
            }
        }
        return files;
    }

    /**
     * 复制单个文件（无文件删除）
     *
     * @param oldPath       String 原文件路径 如：c:/fqf.txt
     * @param newPath       String 复制后路径 如：f:/fqf.txt
     * @param copyDirectory String 准备复制的文件目录，没有则进行创建
     * @return boolean
     */
    public static void copyFileBBOSS(String oldPath, String newPath, String copyDirectory) {
        try {
            File file = new File(copyDirectory);
            //如果文件夹不存在则创建
            if (!file.exists() && !file.isDirectory()) {
                System.out.println("不存在文件夹：" + newPath + "进创建！");
                file.mkdirs();
            }

            int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldPath);
            if (oldfile.exists()) { //文件存在时
                InputStream inStream = new FileInputStream(oldPath); //读入原文件
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                int length;
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; //字节数 文件大小
                    fs.write(buffer, 0, byteread);
                }
                inStream.close();
            }
        } catch (Exception e) {
            logger.info("复制单个文件操作出错");
            e.printStackTrace();

        }

    }

    /**
     * 复制单个文件
     *
     * @param oldPath       String 原文件路径 如：c:/fqf.txt
     * @param newPath       String 复制后路径 如：f:/fqf.txt
     * @param copyDirectory String 准备复制的文件目录，没有则进行创建
     * @return boolean
     */
    public static void copyFile(String oldPath, String newPath, String copyDirectory) {
        InputStream inStream = null;
        FileOutputStream fs = null;
        try {
            File file = new File(copyDirectory);
            //如果文件夹不存在则创建
            if (!file.exists() && !file.isDirectory()) {
                System.out.println("不存在文件夹：" + newPath + "进创建！");
                file.mkdirs();
            }

            int bytesum = 0;
            int byteread = 0;
            File oldfile = new File(oldPath);
            if (oldfile.exists()) { //文件存在时
                inStream = new FileInputStream(oldPath); //读入原文件
                fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1444];
                int length;
                while ((byteread = inStream.read(buffer)) != -1) {
                    bytesum += byteread; //字节数 文件大小
                    fs.write(buffer, 0, byteread);
                }
                boolean b = oldfile.delete();
                logger.info(oldfile.getName() + "备份文件完成!删除原文件路径标识:" + b);
            }
        } catch (Exception e) {
            logger.info("复制单个文件操作出错");
            e.printStackTrace();

        } finally {
            try {
                if (fs != null) {
                    fs.close();
                }
                if (inStream != null) {
                    inStream.close();
                }
            } catch (IOException e) {
                logger.info("流关闭异常");
                e.printStackTrace();
            }
        }

    }

    /**
     * 批量下载文件
     *
     * @param remotePath：远程下载目录(以路径符号结束,可以为相对路径eg:/assess/sftp/jiesuan_2/2014/)
     * @param localPath：本地保存目录(以路径符号结束,D:\Duansha\sftp\)
     * @param del：下载后是否删除sftp文件
     * @return
     */
    public List<String> batchDownLoadFile(String remotePath, String localPath, boolean del) {
        List<String> filenames = new ArrayList<String>();
        try {
            Vector v = listFiles(remotePath);
            if (v.size() > 0) {
                System.out.println("本次处理文件个数不为零,开始下载...fileSize=" + v.size());
                Iterator it = v.iterator();
                while (it.hasNext()) {
                    LsEntry entry = (LsEntry) it.next();
                    String filename = entry.getFilename();
                    SftpATTRS attrs = entry.getAttrs();
                    if (!attrs.isDir()) {
                        boolean flag = false;
                        String localFileName = localPath + filename;

                        flag = downloadFile(remotePath, filename, localPath, filename);
                        if (flag) {
                            filenames.add(localFileName);
                            if (flag && del) {
                                deleteSFTP(remotePath, filename);
                            }
                        }
                    }
                }
            }
            System.out.println("download file is success:remotePath=" + remotePath
                    + "and localPath=" + localPath + ",file size is"
                    + v.size());
        } catch (SftpException e) {
            e.printStackTrace();
        } finally {
        }
        return filenames;
    }

    /**
     * 下载单个文件
     *
     * @param remotePath：远程下载目录(以路径符号结束)
     * @param remoteFileName：下载文件名
     * @param localPath：本地保存目录(以路径符号结束)
     * @param localFileName：保存文件名
     * @return
     */
    public boolean downloadFile(String remotePath, String remoteFileName, String localPath, String localFileName) {
        FileOutputStream fieloutput = null;
        try {
            File file = new File(localPath + localFileName);
            //判断本地路径是否存在
            File localPathfile = new File(localPath);
            if (!localPathfile.exists() && !localPathfile.isDirectory()) {
                System.out.println("下载不存在文件夹：" + localPathfile + "进创建！");
                localPathfile.mkdirs();
            }
            fieloutput = new FileOutputStream(file);
            channel.get(remotePath + remoteFileName, fieloutput);
            System.out.println("===DownloadFile:" + remoteFileName + " success from sftp.");
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != fieloutput) {
                try {
                    fieloutput.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    /**
     * 删除stfp文件
     *
     * @param directory：要删除文件所在目录
     * @param deleteFile：要删除的文件
     */
    public void deleteSFTP(String directory, String deleteFile) {
        try {
            channel.rm(directory + deleteFile);
            System.out.println("delete file success from sftp");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void uploadNoDelete(String remotePath, List<String> fileNamesWithPath) throws SftpException {
        try {
            //执行列表展示ls 命令
            channel.ls(remotePath);
            System.out.println("进入对方服务器文件夹：");
            //执行盘符切换cd 命令
            channel.cd(remotePath);
            System.out.println("切换盘符：" + remotePath);

            List<File> files = fileNamesWithPath.stream()
                    .map(File::new)
                    .collect(Collectors.toList());

            for (File file : files) {
                InputStream input = new BufferedInputStream(Files.newInputStream(file.toPath()));
                channel.put(input, file.getName());
                if (input != null) {
                    try {
                        input.close();
                    } catch (IOException e) {
                        logger.error(file.getName() + "关闭文件时.....异常!" + e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            System.out.println("报错信息：" + e.getMessage());
            logger.error("【子目录创建中】：", e);
            //创建子目录
            channel.mkdir(remotePath);
        }
    }

    /**
     * 推送指定文件 异常全部抛出 等待上层方法处理
     *
     * @param remotePath
     * @param filePath   带路径的文件名
     * @param
     * @throws SftpException
     */
    public void uploadFileNoDelete(String remotePath, String filePath) throws Exception {

        //执行列表展示ls 命令
        channel.ls(remotePath);
        System.out.println("进入对方服务器文件夹：");
        //执行盘符切换cd 命令
        channel.cd(remotePath);
        System.out.println("切换盘符：" + remotePath);

        List<File> files = getFiles(filePath, new ArrayList<File>());
        for (int i = 0; i < files.size(); i++) {
            File file = files.get(i);

            InputStream input = new BufferedInputStream(Files.newInputStream(file.toPath()));
            channel.put(input, file.getName());
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    logger.error(file.getName() + "关闭文件时.....异常!" + e.getMessage());
                    throw new RuntimeException("关闭文件时.....异常!");
                }
            }
        }
    }

    public void uploadOneFileNoDelete(String remotePath, String filePath, String fileName) throws Exception {

        //执行列表展示ls 命令
        channel.ls(remotePath);
        System.out.println("进入对方服务器文件夹：");
        //执行盘符切换cd 命令
        channel.cd(remotePath);
        System.out.println("切换盘符：" + remotePath);

        File file = new File(filePath + fileName);
        InputStream input = new BufferedInputStream(Files.newInputStream(file.toPath()));
        channel.put(input, file.getName());
        if (input != null) {
            try {
                input.close();
            } catch (IOException e) {
                logger.error(file.getName() + "关闭文件时.....异常!" + e.getMessage());
                throw new RuntimeException("关闭文件时.....异常!");
            }
        }
    }

    /**
     * 发送文件，不删除源文件
     *
     * @param directory
     * @param uploadFile
     * @throws Exception
     */
    public void uploadNoDelete(String directory, String uploadFile) throws Exception {
        try {
            //执行列表展示ls 命令
            channel.ls(directory);
            System.out.println("进入对方服务器文件夹：");
            //执行盘符切换cd 命令
            channel.cd(directory);
            System.out.println("切换盘符：" + directory);
            List<File> files = getFiles(uploadFile, new ArrayList<File>());
            for (int i = 0; i < files.size(); i++) {
                File file = files.get(i);
                InputStream input = new BufferedInputStream(new FileInputStream(file));
                channel.put(input, file.getName());
                try {
                    if (input != null) {
                        input.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error(file.getName() + "关闭文件时.....异常!" + e.getMessage());
                }
            }
        } catch (Exception e) {
            System.out.println("报错信息：" + e.getMessage());
            logger.error("【子目录创建中】：", e);
            //创建子目录
            channel.mkdir(directory);
        }
    }


    /**
     * 上传文件到服务器
     * @param bytes
     * @param fileName
     * @throws Exception
     */
    public void sshSftp(byte[] bytes, String fileName, String basePath) throws Exception {
        try {
            //进入服务器指定的文件夹
            channel.cd(basePath);
            //实现从本地上传一个文件到服务器，如果要实现下载，对换一下流就可以
            OutputStream outputStream = channel.put(fileName);
            outputStream.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
//            logger.error(Utils.getStackTrace(e));
//            channel.mkdir(basePath);
//            sshSftp(bytes,fileName,basePath);
        }
    }

    /**
     * 根据文件路径获取文件workbook
     * @param remoteFilePath
     * @return
     * @throws JSchException
     */
    public Workbook getWorkbook(String remoteFilePath) throws JSchException {
        Workbook workbook = null;
        try {
//            if (remoteFilePath.endsWith("xls")) {
//                file = File.createTempFile("final-" + prefix, "." + suffix);
//            } else if (remoteFilePath.endsWith("xlsx")) {
//                file = File.createTempFile("final-" + prefix,"." + suffix);
//            }
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            channel.get(remoteFilePath,bos);
            bos.flush();
//            FileOutputStream fileOutputStream = new FileOutputStream(file);
//            fileOutputStream.write(bos.toByteArray());
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            if (remoteFilePath.endsWith("xls")) {
                workbook = new HSSFWorkbook(bis);
            } else if (remoteFilePath.endsWith("xlsx")) {
                workbook = new XSSFWorkbook(bis);
            }
            //关闭资源
//            channel.exit();
//            channel.getSession().disconnect();
            return workbook;
        } catch (IOException | SftpException e) {
            throw new RuntimeException(e);
        }
    }


    



    /**
     * 列出目录下的文件
     *
     * @param directory：要列出的目录
     * @return
     * @throws SftpException
     */
    public Vector listFiles(String directory) throws SftpException {
        return channel.ls(directory);
    }

    public String getHost() {
        return host;
    }

    @Value("${ftp.host}")
    public void setHost(String host) {
        this.host = host;
    }

    public String getUsername() {
        return username;
    }

    @Value("${ftp.user}")
    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    @Value("${ftp.password}")
    public void setPassword(String password) {
        this.password = password;
    }

    public int getPort() {
        return port;
    }

    @Value("${ftp.port}")
    public void setPort(int port) {
        this.port = port;
    }

    public ChannelSftp getSftp() {
        return channel;
    }

    public void setSftp(ChannelSftp sftp) {
        this.channel = sftp;
    }
}

