package com.suke.czx.newland.vo.audit;

import java.util.Date;

public class AuditDictQueryVo {
    private int dictId;
    private int dictTypeId;
    private int dictValue;
    private String dictName;
    private String dictDesc;
    private int status;
    private Date createDate;
    private String createPerson;
    private Date modifyDate;
    private String modifyPerson;
    private int isUsed;

    private String cronExpression;

    public String getCronExpression() {
        return cronExpression;
    }

    public void setCronExpression(String cronExpression) {
        this.cronExpression = cronExpression;
    }

    public int getDictId() {
        return dictId;
    }

    public void setDictId(int dictId) {
        this.dictId = dictId;
    }

    public int getDictTypeId() {
        return dictTypeId;
    }

    public void setDictTypeId(int dictTypeId) {
        this.dictTypeId = dictTypeId;
    }

    public int getDictValue() {
        return dictValue;
    }

    public void setDictValue(int dictValue) {
        this.dictValue = dictValue;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getDictDesc() {
        return dictDesc;
    }

    public void setDictDesc(String dictDesc) {
        this.dictDesc = dictDesc;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getCreatePerson() {
        return createPerson;
    }

    public void setCreatePerson(String createPerson) {
        this.createPerson = createPerson;
    }

    public Date getModifyDate() {
        return modifyDate;
    }

    public void setModifyDate(Date modifyDate) {
        this.modifyDate = modifyDate;
    }

    public String getModifyPerson() {
        return modifyPerson;
    }

    public void setModifyPerson(String modifyPerson) {
        this.modifyPerson = modifyPerson;
    }

    public int getIsUsed() {
        return isUsed;
    }

    public void setIsUsed(int isUsed) {
        this.isUsed = isUsed;
    }

    @Override
    public String toString() {
        return "AuditDictQueryVo{" +
                "dictId=" + dictId +
                ", dictTypeId=" + dictTypeId +
                ", dictValue=" + dictValue +
                ", dictName='" + dictName + '\'' +
                ", dictDesc='" + dictDesc + '\'' +
                ", status=" + status +
                ", createDate=" + createDate +
                ", createPerson='" + createPerson + '\'' +
                ", modifyDate=" + modifyDate +
                ", modifyPerson='" + modifyPerson + '\'' +
                ", isUsed=" + isUsed +
                '}';
    }
}
