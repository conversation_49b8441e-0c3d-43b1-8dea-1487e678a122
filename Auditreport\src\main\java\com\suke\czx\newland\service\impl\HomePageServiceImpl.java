package com.suke.czx.newland.service.impl;

import com.suke.czx.newland.dao.mgrdbmapper.RptCheckCfgMapper;
import com.suke.czx.newland.dao.mgrdbmapper.RptCheckLogMapper;
import com.suke.czx.newland.dto.AuditItemDto;
import com.suke.czx.newland.dto.RptCheckLogDto;
import com.suke.czx.newland.service.HomePageService;
import com.suke.czx.newland.vo.echartstable.AuditItemCheckLogTableVo;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class HomePageServiceImpl implements HomePageService {

    private final RptCheckCfgMapper rptCheckCfgMapper;
    private final RptCheckLogMapper rptCheckLogMapper;

    public HomePageServiceImpl(RptCheckCfgMapper rptCheckCfgMapper, RptCheckLogMapper rptCheckLogMapper) {
        this.rptCheckCfgMapper = rptCheckCfgMapper;
        this.rptCheckLogMapper = rptCheckLogMapper;
    }

    @Override
    public List<AuditItemDto> qryAuditItemCreateInfoWithDuration(int duration) {
        return rptCheckCfgMapper.qryRptCheckCfgCreateTimeWithDuration(duration);
    }

    @Override
    public List<AuditItemDto> qryAuditItemUpdateInfoWithDuration(int duration) {
        return rptCheckCfgMapper.qryRptCheckCfUpdateTimeWithDuration(duration);
    }

    @Override
    public List<AuditItemCheckLogTableVo> qryAuditItemCheckLogInfo(LocalDate time) {
        List<AuditItemCheckLogTableVo> res = new ArrayList<>();
        List<RptCheckLogDto> logRes = rptCheckLogMapper.qryRptCheckLogGroupByConclusionWithTime(time.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        Map<String, List<RptCheckLogDto>> groupByModleNameMap = logRes.stream()
                .collect(Collectors.groupingBy(RptCheckLogDto::getModleName));
        groupByModleNameMap.forEach((key,val)->{
            AuditItemCheckLogTableVo temp = new AuditItemCheckLogTableVo();
            temp.setModleName(key);
            for (RptCheckLogDto rptCheckLog : val) {
                if ("正常".equals(rptCheckLog.getConclusion())){
                    temp.setNormal(temp.getNormal() + rptCheckLog.getCountVal());
                }else if ("异常".equals(rptCheckLog.getConclusion())){
                    temp.setUnNormal(temp.getUnNormal() + rptCheckLog.getCountVal());
                }else {
                    temp.setUnNormal(temp.getUnNormal() + rptCheckLog.getCountVal());
                }
            }
            res.add(temp);
        });
        return res;
    }
}
