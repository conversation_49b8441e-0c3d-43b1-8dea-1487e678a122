package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.CheckLogAnalysisDto;
import com.suke.czx.newland.dto.RptCheckCfgDataDistributionDto;
import com.suke.czx.newland.vo.audit.*;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IAuditCfgMapper {
    int addAuditType(AuditDictTypeVo auditDictTypeVo);
    List<AuditConfigVo> queryAuditConfig(AuditQueryVo auditQueryVo);
    int selectAuditConfigCountByTypeId(@Param("typeId") int typeId);
    int addAuditConfig(AuditConfigVo auditConfigVo);
    int editAuditConfig(AuditConfigVo auditConfigVo);
    int delAuditConfig(@Param("ruleId") String ruleId);
    long selectCountByXuhao(@Param("xuhao") long xuhao);

    String callAuditProcedure(@Param("cfgId") Integer cfgId, @Param("execPerson") String execPerson);

    List<AuditDictTypeVo> queryDictType();
    List<AuditDictVo> queryDictByType(@Param("typeId") Integer typeId);
    int delAuditDictByDictId(@Param("dictId") Integer dictValue);
    int addAuditDict(AuditDictVo dict);
    int editAuditDict(AuditDictVo dict);
    int selectMaxId();

    int selectDictValMaxId();
    int updateHisToryByDate();
    int deleteHistoryByDate();
    List<AuditLogVo> queryAuditLogByAll(@Param("condition") AuditLogQryVo auditLogVo,@Param("datasourceId") String datasourceId);

    int execSqlByRuleId(@Param("ruleSql") String ruleSql);
    int bakAuditCfgByRuleId(@Param("ruleId") long ruleId);

    int saveExecRemarkByLogId(AuditLogVo logVo);

    int saveAnalysisResByLogId(AuditLogVo logVo);

//    List<AuditConfigVo> queryHisConfigByRuleId(@Param("ruleId") long ruleId);
    // TODO ����
    int importResultData(@Param("list") List<AuditLogVo> auditLogVo);

    // TODO ����
    int importAuditData(@Param("list") List<AuditConfigVo> auditConfigVo);

    // TODO ����
    List<AuditConfigVo> queryHisConfigByRuleId(@Param("ruleId") long ruleId, @Param("modifyPerson") String modifyPerson, @Param("ruleName") String ruleName);

    Map<String, Object> queryJobStatus();


    List<RptCheckCfgDataDistributionDto> queryDataDistribution();

    List<AuditDictVo> qryAllAuditDictByActived();

    AuditDictVo qryAllAuditDictByDictId(int dictId);

    List<AuditConfigVo> queryRptCheckCfgByRuleIdWithActived(@Param("dictId")int dictId);

    AuditDictVo qryAuditDictByDictValue(String dictValue);

    AuditDictVo qryAuditDictByDictValueWithoutStatus(String dictValue);

    List<AuditConfigVo> qryAuditCfgWithRuleIds(String[] ruleIds);


    List<CheckLogAnalysisDto> qryAuditDateWithDuration(@Param("duration") int duration);
}
