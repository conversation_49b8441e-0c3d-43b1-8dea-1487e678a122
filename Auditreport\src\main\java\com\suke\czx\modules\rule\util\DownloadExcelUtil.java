package com.suke.czx.modules.rule.util;

import com.alibaba.druid.util.Utils;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import com.suke.czx.newland.dto.AuditLogDownloadRecordDto;
import com.suke.czx.newland.vo.audit.AuditConfigVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;

@Slf4j
public class DownloadExcelUtil {

    public static byte[] exportAuditRuleToExcel(List<AuditConfigVo> records) {
        XSSFWorkbook workbook = new XSSFWorkbook(); // 创建Excel工作簿
        Sheet sheet = workbook.createSheet("稽核规则信息"); // 创建工作表

        // 设置表头样式
        XSSFCellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        headerStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        Row headerRow = sheet.createRow(0); // 创建表头行
        headerRow.createCell(0).setCellValue("规则编号");
        headerRow.createCell(1).setCellValue("稽核点规则名称");
        headerRow.createCell(2).setCellValue("状态（1生效、0失效）");
        headerRow.createCell(3).setCellValue("创建人");
        headerRow.createCell(4).setCellValue("创建时间");
        headerRow.createCell(5).setCellValue("修改人");
        headerRow.createCell(6).setCellValue("修改时间");
        headerRow.createCell(7).setCellValue("稽核项");
        headerRow.createCell(8).setCellValue("业务中心");
        headerRow.createCell(9).setCellValue("业务模块");
        headerRow.createCell(10).setCellValue("稽核语句");
        headerRow.createCell(11).setCellValue("阈值（<>,>=,>,<=,<,=）");
        headerRow.createCell(12).setCellValue("备注");
        headerRow.createCell(13).setCellValue("环境");
        genContextStyle(workbook);
        int rowNum = 1;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (AuditConfigVo record : records) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(record.getRuleId());
            row.createCell(1).setCellValue(record.getRuleName());
            row.createCell(2).setCellValue(record.getStatus());
            row.createCell(3).setCellValue(record.getCreatePerson());
            row.createCell(4).setCellValue(record.getCreateDate() != null ? format.format(record.getCreateDate()) : "");
            row.createCell(5).setCellValue(record.getModifyPerson());
            row.createCell(6).setCellValue(record.getModifyDate() != null ? format.format(record.getModifyDate()) : "");
            row.createCell(7).setCellValue(record.getChkTypeId());
            row.createCell(8).setCellValue(record.getCenterId());
            row.createCell(9).setCellValue(record.getModleId());
            row.createCell(10).setCellValue(record.getRuleSql());
            row.createCell(11).setCellValue(record.getLimitValue());
            row.createCell(12).setCellValue(record.getRuleDesc());
            row.createCell(13).setCellValue(record.getEnvType());
//            if (record.getModifyDate() != null) {
//                row.createCell(9).setCellValue(format.format(record.getModifyDate()));
//            }
            sheet.autoSizeColumn(rowNum);
            int width = Math.max(15 * 256, Math.min(256 * 256, sheet.getColumnWidth(rowNum) * 12 / 10));
            sheet.setColumnWidth(rowNum, width);
        }
        //设置默认行高
        sheet.setDefaultRowHeight((short) (16.5 * 20));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); // 将Excel写入字节数组输出流
        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            log.error(Utils.getStackTrace(e));
            throw new RuntimeException(e);
        }
        return outputStream.toByteArray();

    }

    public static byte[] exportAuditLogToExcel(List<AuditLogDownloadRecordDto> records) {
        XSSFWorkbook workbook = new XSSFWorkbook(); // 创建Excel工作簿
        Sheet sheet = workbook.createSheet("稽核日志信息"); // 创建工作表

        // 设置表头样式
        XSSFCellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        headerStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        Row headerRow = sheet.createRow(0); // 创建表头行
        headerRow.createCell(0).setCellValue("日志编号");
        headerRow.createCell(1).setCellValue("数据库用户名");
        headerRow.createCell(2).setCellValue("用户机器名称");
        headerRow.createCell(3).setCellValue("规则编号");
        headerRow.createCell(4).setCellValue("执行人");
        headerRow.createCell(5).setCellValue("执行日期");
        headerRow.createCell(6).setCellValue("稽核项编码");
        headerRow.createCell(7).setCellValue("稽核项");
        headerRow.createCell(8).setCellValue("业务中心编码");
        headerRow.createCell(9).setCellValue("业务中心");
        headerRow.createCell(10).setCellValue("稽核模块编码");
        headerRow.createCell(11).setCellValue("稽核模块");
        headerRow.createCell(12).setCellValue("规则名称");
        headerRow.createCell(13).setCellValue("执行SQL");
        headerRow.createCell(14).setCellValue("阈值");
        headerRow.createCell(15).setCellValue("统计值");
        headerRow.createCell(16).setCellValue("状态");
        headerRow.createCell(17).setCellValue("执行结果");
        headerRow.createCell(18).setCellValue("执行时间(单位:ms)");
        headerRow.createCell(19).setCellValue("执行备注");
        headerRow.createCell(20).setCellValue("创建时间");
        headerRow.createCell(21).setCellValue("创建人");
        headerRow.createCell(22).setCellValue("最近更新时间");
        headerRow.createCell(23).setCellValue("执行人");
        headerRow.createCell(24).setCellValue("分析结论");
        genContextStyle(workbook);
        int rowNum = 1;
        for (AuditLogDownloadRecordDto user : records) {
            Row row = sheet.createRow(rowNum++);
            row.createCell(0).setCellValue(user.getLogId());
            row.createCell(1).setCellValue(user.getDbName());
            row.createCell(2).setCellValue(user.getDbMachine());
            row.createCell(3).setCellValue(user.getRuleId());
            row.createCell(4).setCellValue(user.getModifyPerson());
            row.createCell(5).setCellValue(user.getModifyDate());
//            Cell cell = row.createCell(5);
//            cell.setCellType(CellType.STRING);
//            cell.setCellValue(user.getModifyDate());
            row.createCell(6).setCellValue(user.getChkTypeId());
            row.createCell(7).setCellValue(user.getChkTypeName());
            row.createCell(8).setCellValue(user.getCenterId());
            row.createCell(9).setCellValue(user.getCenterName());
            row.createCell(10).setCellValue(user.getModleId());
            row.createCell(11).setCellValue(user.getModleName());
            row.createCell(12).setCellValue(user.getRuleName());
            row.createCell(13).setCellValue(user.getRuleSql());
            row.createCell(14).setCellValue(user.getLimitValue());
            row.createCell(15).setCellValue(user.getSqlValue());
            row.createCell(16).setCellValue(user.getStatus());
            row.createCell(17).setCellValue(user.getConclusion());
            row.createCell(18).setCellValue(user.getExecMsec());
            row.createCell(19).setCellValue(user.getExecRemark());
            row.createCell(20).setCellValue(user.getCreateDate());
            row.createCell(21).setCellValue(user.getCreatePerson());
            row.createCell(22).setCellValue(user.getExecDate());
            row.createCell(23).setCellValue(user.getExecPerson());
            row.createCell(24).setCellValue(user.getAnalysisRes());
            sheet.autoSizeColumn(rowNum);
            int width = Math.max(15 * 256, Math.min(256 * 256, sheet.getColumnWidth(rowNum) * 12 / 10));
            sheet.setColumnWidth(rowNum, width);
        }
        //设置默认行高
        sheet.setDefaultRowHeight((short) (16.5 * 20));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); // 将Excel写入字节数组输出流
        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return outputStream.toByteArray();
    }

    public static byte[] exportUsersToExcel(List<AuditRuleConfig> ruleConfigs) throws IOException {
        XSSFWorkbook workbook = new XSSFWorkbook(); // 创建Excel工作簿
        Sheet sheet = workbook.createSheet(ruleConfigs.get(0).getFaId()); // 创建工作表

        // 设置表头样式
        XSSFCellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        headerStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);

        Row headerRow = sheet.createRow(0); // 创建表头行
        headerRow.createCell(0).setCellValue("行");
        headerRow.createCell(1).setCellValue("列");
        headerRow.createCell(2).setCellValue("子规则名称");
        headerRow.createCell(3).setCellValue("规则类型");
        headerRow.createCell(4).setCellValue("规则参数");
        headerRow.createCell(5).setCellValue("规则说明");
        headerRow.createCell(6).setCellValue("表头行号");
        headerRow.createCell(7).setCellValue("表尾行号");
        headerRow.createCell(8).setCellValue("规则名称");


        //设置Excel格式
        XSSFCellStyle style = genContextStyle(workbook);
        int rowNum = 1;
        for (AuditRuleConfig user : ruleConfigs) {
            Row row = sheet.createRow(rowNum++);
            if (user.getRuleRow() != null && user.getRuleRow() == 99999){
                row.createCell(0).setCellValue("正文");
            }else {
                row.createCell(0).setCellValue(user.getRuleRow());
            }
            if (user.getRuleColumn() != null && user.getRuleColumn() == 99999){
                row.createCell(1).setCellValue("正文");
            }else {
                row.createCell(1).setCellValue(user.getRuleColumn());
            }
            row.createCell(2).setCellValue(user.getName());
            row.createCell(3).setCellValue(user.getType());
            row.createCell(4).setCellValue(user.getParameter());
            row.createCell(5).setCellValue(user.getRemarks());
            row.createCell(6).setCellValue(user.getTitleRow());
            if (user.getTitleEndRow() != null) {
                row.createCell(7).setCellValue(user.getTitleEndRow());
            }

            row.createCell(8).setCellValue(user.getFaId());

            sheet.autoSizeColumn(rowNum);
            int width = Math.max(15 * 256, Math.min(256 * 256, sheet.getColumnWidth(rowNum) * 12 / 10));
            sheet.setColumnWidth(rowNum, width);
        }
        //设置默认行高
        sheet.setDefaultRowHeight((short) (16.5 * 20));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream(); // 将Excel写入字节数组输出流
        try {
            workbook.write(outputStream);
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return outputStream.toByteArray(); // 返回字节数组
    }


    //表格字体样式
    public static XSSFCellStyle genContextStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style1 = workbook.createCellStyle();
        //文本水平居中显示
        style1.setAlignment(HorizontalAlignment.CENTER);
        //文本竖直居中显示
        style1.setVerticalAlignment(VerticalAlignment.CENTER);
        //文本自动换行
        style1.setWrapText(true);
        //设置文本边框
        style1.setBorderBottom(BorderStyle.THIN);
        style1.setBorderLeft(BorderStyle.THIN);
        style1.setBorderRight(BorderStyle.THIN);
        style1.setBorderTop(BorderStyle.THIN);
        return style1;
    }

    ;
}
