package com.suke.czx.modules.sys.controller;

import com.suke.czx.common.annotation.SysLog;
import com.suke.czx.common.exception.RRException;
import com.suke.czx.common.utils.Constant.MenuType;
import com.suke.czx.common.utils.R;
import com.suke.czx.modules.sys.entity.SysMenuEntity;
import com.suke.czx.modules.sys.service.ShiroService;
import com.suke.czx.modules.sys.service.SysMenuService;
import com.suke.czx.modules.sys.service.SysRoleMenuService;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 系统菜单
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午9:58:15
 */
@RestController
@RequestMapping("/audit/menu")
public class SysMenuController extends AbstractController {

	@Autowired
	private SysMenuService sysMenuService;

	@Autowired
	private ShiroService shiroService;

	@Autowired
	private SysRoleMenuService sysRoleMenuService;

	/**
	 * 导航菜单
	 */
	@RequestMapping("/nav")
	public R nav(){
		List<SysMenuEntity> menuList = sysMenuService.getUserMenuList(getUserId());
		Set<String> permissions = shiroService.getUserPermissions(getUserId());
		return R.ok().put("menuList", menuList).put("permissions", permissions);
	}

	/**
	 * 根据用户id查询用户的权限列表
	 * @return
	 */
	@RequestMapping(value = "/perMenu",method = RequestMethod.GET)
	public List<SysMenuEntity> getUserMenuList() {
		return sysMenuService.queryUserList(getUserId());
	}
	
	/**
	 * 所有菜单列表
	 */
	@RequestMapping("/list")
	// @RequiresPermissions("sys:menu:list")
	public List<SysMenuEntity> list(){
		List<SysMenuEntity> menuList = sysMenuService.queryList(new HashMap<String, Object>());

		return menuList;
	}
	
	/**
	 * 选择菜单(添加、修改菜单)
	 */
	@RequestMapping("/select")
	// @RequiresPermissions("sys:menu:select")
	public R select(){
		//查询列表数据
		List<SysMenuEntity> menuList = sysMenuService.queryNotButtonList();
		
		//添加顶级菜单
		SysMenuEntity root = new SysMenuEntity();
		root.setId(0L);
		root.setName("一级菜单");
		root.setParentId(-1L);
		// root.setOpen(true);
		menuList.add(root);
		
		return R.ok().put("menuList", menuList);
	}
	
	/**
	 * 菜单信息
	 */
	@RequestMapping("/info/{menuId}")
	// @RequiresPermissions("sys:menu:info")
	public R info(@PathVariable("menuId") Long menuId){
		SysMenuEntity menu = sysMenuService.queryObject(menuId);
		return R.ok().put("menu", menu);
	}
	
	/**
	 * 保存
	 */
	@SysLog("保存菜单")
	@RequestMapping(value = "/addMenu",method = RequestMethod.POST)
	// @RequiresPermissions("sys:menu:save")
	public R save(@RequestBody SysMenuEntity menu){
		//数据校验
		verifyForm(menu);
		System.out.println("保存的菜单：" + menu);
		sysMenuService.save(menu);
		
		return R.ok();
	}
	
	/**
	 * 修改
	 */
	@SysLog("修改菜单")
	@RequestMapping(value = "/updateMenu",method = RequestMethod.PUT)
	// @RequiresPermissions("sys:menu:update")
	public R update(@RequestBody SysMenuEntity menu){
		//数据校验
		verifyForm(menu);

		// System.out.println("修改的菜单：" + menu);
		sysMenuService.update(menu);
		
		return R.ok();
	}
	
	/**
	 * 删除
	 */
	@SysLog("删除菜单")
	@RequestMapping(value = "/delMenu",method = RequestMethod.DELETE)
	// @RequiresPermissions("sys:menu:delete")
	public R delete(long menuId){
		//判断是否有子菜单或按钮
		List<SysMenuEntity> menuList = sysMenuService.queryListParentId(menuId);
		if(menuList.size() > 0){
			return R.error("请先删除子菜单!");
		}
		//判断是否存在关联的角色
		Integer queryTotal = sysRoleMenuService.queryTotal(menuId);
		if (queryTotal > 0) {
			//说明关联表中存在数据，不可以删除
			return R.error("请先删除角色菜单表中的关联数据!");
		}
		//进行删除
		sysMenuService.delete(menuId);
		return R.ok();
	}
	
	/**
	 * 验证参数是否正确
	 */
	private void verifyForm(SysMenuEntity menu){
		if(StringUtils.isBlank(menu.getName())){
			throw new RRException("菜单名称不能为空");
		}
		
		if(menu.getParentId() == null){
			throw new RRException("上级菜单不能为空");
		}
		
		//菜单
		if(Objects.equals(menu.getType(), MenuType.MENU.getValue())){
			if(StringUtils.isBlank(menu.getUrl())){
				throw new RRException("菜单URL不能为空");
			}
		}
		
		//上级菜单类型
		// String parentType = MenuType.CATALOG.getValue();
		// if(menu.getParentId() != 0){
		// 	//查询并获取了上级菜单的类型
		// 	SysMenuEntity parentMenu = sysMenuService.queryObject(menu.getParentId());
		// 	parentType = parentMenu.getType();
		// }
		//
		// //目录、菜单
		// if(Objects.equals(menu.getType(), MenuType.CATALOG.getValue()) || Objects.equals(menu.getType(), MenuType.MENU.getValue())){
		// 	if(!Objects.equals(parentType, MenuType.CATALOG.getValue())){
		// 		throw new RRException("上级菜单只能为目录类型");
		// 	}
		// 	return ;
		// }
		//
		// //按钮
		// if(Objects.equals(menu.getType(), MenuType.BUTTON.getValue())){
		// 	if(!Objects.equals(parentType, MenuType.MENU.getValue())){
		// 		throw new RRException("上级菜单只能为菜单类型");
		// 	}
		// 	return ;
		// }
	}
}
