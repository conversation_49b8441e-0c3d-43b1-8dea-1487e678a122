package com.suke.czx.config;

import com.suke.czx.thread.SchedulingRunnable;
import org.quartz.CronExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.config.CronTask;
import org.springframework.scheduling.config.ScheduledTask;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class CronTaskRegistrar extends ScheduledTaskRegistrar {

    private final Map<Runnable, ScheduledTask> scheduledTasks = new ConcurrentHashMap<>(16);

    @Autowired
    @Qualifier(value = "taskScheduler")
    private TaskScheduler taskScheduler;

    public TaskScheduler getScheduler() {
        return this.taskScheduler;
    }

    public void addCronTask(Runnable task, String cronExpression) {
        addCronTask(new CronTask(task, cronExpression));
    }

    public void addCronTask(CronTask cronTask) {
        if (Objects.nonNull(cronTask)) {
            Runnable task = cronTask.getRunnable();
            if (this.scheduledTasks.containsKey(task)) {
                removeCronTask(task);
            }
            this.scheduledTasks.put(task, scheduleCronTask(cronTask));
        }
    }

    /*
    新增，如果存在则替换，根据dict id
     */
    public void addCronTaskWithDictId(SchedulingRunnable cronTask, String cronExpression) {
        if (!CronExpression.isValidExpression(cronExpression)) {
            return;
        }
        if (Objects.nonNull(cronTask)) {
            removeCronTaskByTaskDictId(cronTask);
            this.scheduledTasks.put(cronTask, scheduleCronTask(new CronTask(cronTask, cronExpression)));
        }
    }

    /*
    存在任务则移除
     */
    private void removeCronTaskByTaskDictId(SchedulingRunnable cronTask) {
        if (Objects.nonNull(cronTask)) {
            removeCronTaskByTaskDictId(cronTask.getDictId());
        }
    }

    public void removeCronTaskByTaskDictId(int dictId) {
        for (Runnable runnable : scheduledTasks.keySet()) {
            if (runnable instanceof SchedulingRunnable) {
                if (((SchedulingRunnable) runnable).getDictId() == dictId) {
                    removeCronTask(runnable);
                    break;
                }
            }
        }
    }

    public void removeCronTask(Runnable task) {
        ScheduledTask scheduledTask = this.scheduledTasks.remove(task);
        if (Objects.nonNull(scheduledTask)) {
            scheduledTask.cancel();
        }
    }

    @Override
    public void destroy() {
        for (ScheduledTask task : this.scheduledTasks.values()) {
            task.cancel();
        }
        this.scheduledTasks.clear();
    }
}
