package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetTogetherDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class TogetherHandler extends ExcelHandleRuler {


    private final String dynamic_expression_regex = "【(([y|Y]{4}[m|M]{2}-\\d)|([y|Y]{4}[m|M]{2})|([y|Y]{4}[m|M]{2}[d|D]{2}-\\d)|([y|Y]{4}[m|M]{2}[d|D]{2}))】";

    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetTogetherDto condition = (SheetTogetherDto) param;

        String matchStr = condition.getMatchStr();
        int togetherColumn = condition.getColumn();
        Sheet sheet = condition.getSheet();
        boolean res = true;
        matchStr = handDynamicParam(matchStr);

        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);

            Cell oneRowTargetCell = oneRow.getCell(togetherColumn);
            setCellStr(oneRowTargetCell);

            String oneRowTargetStr = oneRowTargetCell.getStringCellValue();

            if (!matchStr.equals(oneRowTargetStr)) {
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }
        }
        return res ? success() : fail();
    }

    private String handDynamicParam(String content) {
        Pattern pattern = Pattern.compile(dynamic_expression_regex);

        Matcher matcherForFormatStr = pattern.matcher(content);
        if (!matcherForFormatStr.find()) {
            return content;
        }
        String formatStr = matcherForFormatStr.group(1);
        if (formatStr.contains("-")) {
            String[] formatStrArr = formatStr.split("-");
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStrArr[0]);
            if (formatStrArr[0].length() == 6) {
                return matcherForFormatStr.replaceAll(LocalDate
                        .now()
                        .minusMonths(Long.parseLong(formatStrArr[1]))
                        .format(formatter));
            } else if (formatStrArr[0].length() == 8) {
                return matcherForFormatStr.replaceAll(LocalDate
                        .now()
                        .minusDays(Long.parseLong(formatStrArr[1]))
                        .format(formatter));
            }
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatStr);
            String dateStr = LocalDate.now().format(formatter);
            return matcherForFormatStr.replaceAll(dateStr);
        }
        return content;
    }
}
