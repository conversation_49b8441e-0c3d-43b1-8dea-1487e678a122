package com.suke.czx.newland.vo.audit;

import java.util.List;

public class AuditExecInfoVo {

    private List<AuditExecVo> cfgIds;
    private String execPerson;

    private String runDatasourceId;

    public String getRunDatasourceId() {
        return runDatasourceId;
    }

    public void setRunDatasourceId(String runDatasourceId) {
        this.runDatasourceId = runDatasourceId;
    }

    public List<AuditExecVo> getCfgIds() {
        return cfgIds;
    }

    public void setCfgIds(List<AuditExecVo> cfgIds) {
        this.cfgIds = cfgIds;
    }

    public String getExecPerson() {
        return execPerson;
    }

    public void setExecPerson(String execPerson) {
        this.execPerson = execPerson;
    }

    @Override
    public String toString() {
        return "AuditExecInfoVo{" +
                "cfgIds=" + cfgIds +
                ", execPerson='" + execPerson + '\'' +
                '}';
    }
}
