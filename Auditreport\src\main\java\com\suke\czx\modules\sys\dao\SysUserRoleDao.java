package com.suke.czx.modules.sys.dao;

import com.suke.czx.modules.sys.entity.SysUserRoleEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与角色对应关系
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:34:46
 */
@Mapper
public interface SysUserRoleDao extends BaseDao<SysUserRoleEntity> {
	
	/**
	 * 根据用户ID，获取角色ID列表
	 */
	List<Long> queryRoleIdList(String userId);

	/**
	 * 保存用户的角色信息
	 * @param userId
	 * @param item
	 */
    void saveUserRole(@Param("userId") String userId,@Param("item") Long item);
}
