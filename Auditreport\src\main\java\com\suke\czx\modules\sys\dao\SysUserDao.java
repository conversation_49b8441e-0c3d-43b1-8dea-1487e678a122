package com.suke.czx.modules.sys.dao;

import com.suke.czx.modules.sys.entity.SysUserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 系统用户
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年9月18日 上午9:34:11
 */
@Mapper
public interface SysUserDao extends BaseDao<SysUserEntity> {
	
	/**
	 * 查询用户的所有权限
	 * @param userId  用户ID
	 */
	List<String> queryAllPerms(String userId);
	
	/**
	 * 查询用户的所有菜单ID
	 */
	List<Long> queryAllMenuId(String userId);
	
	/**
	 * 根据用户名，查询系统用户
	 */
	SysUserEntity queryByUserName(String username);
	
	/**
	 * 修改密码
	 */
	int updatePassword(Map<String, Object> map);

	List<SysUserEntity> qryUserListByCondition(@Param("param") SysUserEntity param,@Param("roleIds") String[] roleIds);

	void delAutoUserInfoByUserIds(String[] userIds);

	void delAutoUserRoleByUserIds(String[] userIds);

	Set<String> qryUserRoleByUserId(String userId);
}
