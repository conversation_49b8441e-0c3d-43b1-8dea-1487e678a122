<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suke.czx.newland.dao.mgrdbmapper.IAuditCfgMapper">

    <insert id="addAuditConfig" parameterType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        insert into RPT_CHECK_CFG(RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE)
        values(#{ruleId},#{ruleName,jdbcType = VARCHAR},#{status},#{create<PERSON>erson,jdbcType = VARCHAR},sysdate,null,null,#{chkTypeId,jdbcType = VARCHAR},#{centerId,jdbcType = VARCHAR},#{modleId,jdbcType = VARCHAR},#{ruleSql,jdbcType = VARCHAR},#{limitValue,jdbcType = VARCHAR},#{ruleDesc,jdbcType = VARCHAR},#{envType})
    </insert>

    <insert id="addAuditDict">
        insert into PUBLIC_RPT_DICT(DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON)
        values(#{dictId},#{dictTypeId},#{dictValue},#{dictName,jdbcType = VARCHAR},#{dictDesc,jdbcType = VARCHAR},#{status},sysdate,#{createPerson,jdbcType = VARCHAR},sysdate,#{createPerson,jdbcType = VARCHAR})
    </insert>


    <delete id="delAuditDictByDictId" parameterType="java.lang.Integer">
        delete from PUBLIC_RPT_DICT where DICT_ID = #{dictId}
    </delete>
    <delete id="delAuditConfig">
        delete from RPT_CHECK_CFG where RULE_ID = #{ruleId}
    </delete>
    <delete id="deleteHistoryByDate">
        <![CDATA[
            delete from RPT_CHECK_LOG logs where logs.EXEC_DATE < add_months(sysdate, -9)
        ]]>
    </delete>

    <select id="selectCountByXuhao" resultType="java.lang.Long">
         select nvl(max(RULE_ID),-1) from RPT_CHECK_CFG
    </select>
    <insert id="addAuditType" parameterType="com.suke.czx.newland.vo.audit.AuditDictTypeVo">
        insert into PUBLIC_DICT_TYPE(DICT_TYPE_ID,DICT_TYPE_NAME,DICT_TYPE_DESC,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON)
        values(#{dictTypeId},#{dictTypeName,jdbcType=VARCHAR},#{dictTypeDesc,jdbcType=VARCHAR},sysdate,#{createPerson,jdbcType=VARCHAR},sysdate,#{modifyPerson,jdbcType=VARCHAR})
    </insert>
    <insert id="bakAuditCfgByRuleId">
        insert into RPT_CHECK_CFG_HIS (RULE_ID, RULE_NAME, STATUS, CREATE_PERSON, CREATE_DATE, MODIFY_PERSON,
                                                    MODIFY_DATE, CHK_TYPE_ID, CENTER_ID, MODLE_ID, RULE_SQL, LIMIT_VALUE, RULE_DESC,
                                                    ENV_TYPE)
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,sysdate,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE
        from RPT_CHECK_CFG org
        where org.rule_id = #{ruleId}
    </insert>
    <select id="queryDictType" resultType="com.suke.czx.newland.vo.audit.AuditDictTypeVo">
        select DICT_TYPE_ID,DICT_TYPE_NAME,DICT_TYPE_DESC,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON
        from PUBLIC_DICT_TYPE order by DICT_TYPE_ID desc
    </select>
    <select id="queryDictByType" resultType="com.suke.czx.newland.vo.audit.AuditDictVo" parameterType="java.lang.Integer">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_TYPE_ID = #{typeId} order by DICT_ID desc
    </select>
    <select id="selectMaxId" resultType="java.lang.Integer">
        select nvl(max(DICT_ID),-1) from PUBLIC_RPT_DICT
    </select>

    <select id="selectDictValMaxId" resultType="java.lang.Integer">
        select nvl(max(DICT_VALUE),-1) from PUBLIC_RPT_DICT
    </select>

    <select id="queryAuditConfig" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where 1=1
        <if test="key != '' and key != null">
            and (RULE_SQL like concat(concat('%',#{key, jdbcType=VARCHAR}),'%') or RULE_NAME like concat(concat('%',#{key, jdbcType=VARCHAR}),'%'))
        </if>
        <if test="value != '' and value != null">
            and CHK_TYPE_ID = #{value,jdbcType = VARCHAR}
        </if>
        <if test="center != '' and center != null">
            and CENTER_ID = #{center,jdbcType = VARCHAR}
        </if>
        <if test="modle != '' and modle != null">
            and MODLE_ID = #{modle,jdbcType = VARCHAR}
        </if>
        <if test="modifyDate != null and endTime != null">
            and to_char(MODIFY_DATE,'yyyyMMddhh24miss') between to_char(#{modifyDate,jdbcType = TIMESTAMP},'yyyyMMddhh24miss')  and  to_char(#{endTime,jdbcType = TIMESTAMP},'yyyyMMddhh24miss')
        </if>
        <if test="status != -1">
            and status = #{status}
        </if>
        <if test="runDatasourceId != null and runDatasourceId != ''">
            and ENV_TYPE = #{runDatasourceId}
        </if>
        order by RULE_ID desc
    </select>

    <select id="queryAuditLogByAll" resultType="com.suke.czx.newland.vo.audit.AuditLogVo" parameterType="com.suke.czx.newland.vo.audit.AuditLogVo">
        select log.LOG_ID        logId,
        log.DB_NAME       dbName,
        log.DB_MACHINE    dbMachine,
        log.RULE_ID       ruleId,
        log.MODIFY_PERSON modifyPerson,
        log.MODIFY_DATE   modifyDate,
        log.CHK_TYPE_ID   chkTypeId,
        log.CHK_TYPE_NAME chkTypeName,
        log.CENTER_ID     centerId,
        log.CENTER_NAME   centerName,
        log.MODLE_ID      modleId,
        log.MODLE_NAME    modleName,
        log.RULE_NAME     ruleName,
        log.RULE_SQL      ruleSql,
        log.LIMIT_VALUE   limitValue,
        log.SQL_VALUE     sqlValue,
        log.STATUS        status,
        log.CONCLUSION    conclusion,
        log.EXEC_MSEC     execMsec,
        log.EXEC_REMARK   execRemark,
        log.CREATE_DATE   createDate,
        log.CREATE_PERSON createPerson,
        log.EXEC_DATE     execDate,
        log.EXEC_PERSON   execPerson,
        log.ANALYSIS_RES  analysisRes
        from RPT_CHECK_LOG log left join RPT_CHECK_CFG cfg on log.RULE_ID = cfg.RULE_ID where 1=1
        <if test="condition.ruleName != null and condition.ruleName != ''">
            and log.RULE_NAME like concat(concat('%',#{condition.ruleName, jdbcType=VARCHAR}),'%')
        </if>
        <if test="condition.centerId != null and condition.centerId != ''">
            and log.CENTER_ID = #{condition.centerId}
        </if>
        <if test="condition.modleId != null and condition.modleId != ''">
            and log.MODLE_ID = #{condition.modleId}
        </if>
        <if test="condition.chkTypeId != null and condition.chkTypeId != ''">
            and log.CHK_TYPE_ID = #{condition.chkTypeId}
        </if>
        <if test="condition.createDate != null and condition.endTime != null" >
            and to_char(log.EXEC_DATE,'yyyyMMddhh24miss') between to_char(#{condition.createDate,jdbcType = TIMESTAMP},'yyyyMMddhh24miss')  and  to_char(#{condition.endTime,jdbcType = TIMESTAMP},'yyyyMMddhh24miss')
        </if>
        <if test="condition.conclusion != null and condition.conclusion != ''">
            and log.CONCLUSION = #{condition.conclusion,jdbcType = VARCHAR}
        </if>
        <if test="condition.analysisRes != null and condition.analysisRes != ''">
            and log.ANALYSIS_RES = #{condition.analysisRes}
        </if>
        <if test="datasourceId != null and datasourceId != ''">
            and cfg.ENV_TYPE = #{datasourceId}
        </if>
        order by LOG_ID desc
    </select>

    <select id="selectAuditConfigCountByTypeId" resultType="java.lang.Integer">
        select count(1) from RPT_CHECK_CFG where CHK_TYPE_ID = #{typeId} or CENTER_ID =  #{typeId} or MODLE_ID =  #{typeId}
    </select>

    <select id="callAuditProcedure" statementType="CALLABLE" resultType="java.lang.String">
        {call p_report_audit_config_new(#{cfgId, jdbcType=INTEGER},#{execPerson,jdbcType=VARCHAR})}
    </select>
    <select id="execSqlByRuleId" parameterType="java.lang.String" resultType="java.lang.Integer">
        ${ruleSql}
    </select>

<!--    <select id="queryHisConfigByRuleId" resultType="com.newland.vo.audit.AuditConfigVo">-->
<!--        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG_HIS where rule_id = #{ruleId} order by modify_date desc nulls last-->
<!--    </select>-->

    <update id="editAuditDict">
        update PUBLIC_RPT_DICT set DICT_VALUE = #{dictValue}, DICT_TYPE_ID = #{dictTypeId},DICT_NAME = #{dictName,jdbcType = VARCHAR},DICT_DESC = #{dictDesc,jdbcType = VARCHAR},STATUS = #{status},MODIFY_DATE = sysdate,MODIFY_PERSON = #{modifyPerson,jdbcType = VARCHAR},CRON_EXPRESSION = #{cronExpression,jdbcType = VARCHAR} where DICT_ID = #{dictId}
    </update>
    <update id="editAuditConfig">
        update RPT_CHECK_CFG set RULE_NAME = #{ruleName,jdbcType=VARCHAR},MODIFY_PERSON = #{createPerson},MODIFY_DATE = sysdate,CHK_TYPE_ID = #{chkTypeId,jdbcType = VARCHAR},CENTER_ID = #{centerId,jdbcType = VARCHAR},MODLE_ID = #{modleId,jdbcType = VARCHAR},RULE_SQL = #{ruleSql,jdbcType = VARCHAR},LIMIT_VALUE = #{limitValue,jdbcType = VARCHAR},RULE_DESC = #{ruleDesc,jdbcType = VARCHAR},STATUS = #{status},ENV_TYPE=#{envType} where RULE_ID = #{ruleId}
    </update>
    <update id="updateHisToryByDate">
        update RPT_CHECK_LOG set STATUS = 2 where to_char(nvl(EXEC_DATE,sysdate),'yyyymmdd') = to_char(sysdate, 'yyyymmdd')
    </update>
    <update id="saveExecRemarkByLogId" parameterType="com.suke.czx.newland.vo.audit.AuditLogVo">
        update RPT_CHECK_LOG set exec_remark = #{execRemark,jdbcType=VARCHAR} where log_id = #{logId}
    </update>
    <update id="saveAnalysisResByLogId" parameterType="com.suke.czx.newland.vo.audit.AuditLogVo">
        update RPT_CHECK_LOG set ANALYSIS_RES= #{analysisRes,jdbcType=VARCHAR} where log_id = #{logId}
    </update>
    <insert id="importResultData" parameterType="java.util.List" useGeneratedKeys="false">
        insert into RPT_CHECK_LOG(
        LOG_ID,
        DB_NAME,
        DB_MACHINE,
        RULE_ID,
        MODIFY_PERSON,
        MODIFY_DATE,
        CHK_TYPE_ID,
        CHK_TYPE_NAME,
        CENTER_ID,
        CENTER_NAME,
        MODLE_ID,
        MODLE_NAME,
        RULE_NAME,
        RULE_SQL,
        LIMIT_VALUE,
        SQL_VALUE,
        STATUS,
        CONCLUSION,
        EXEC_MSEC,
        EXEC_REMARK,
        CREATE_DATE,
        CREATE_PERSON,
        EXEC_DATE,
        EXEC_PERSON,
        ANALYSIS_RES
        )
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.logId,jdbcType = VARCHAR},
            #{item.dbName,jdbcType = VARCHAR},
            #{item.dbMachine,jdbcType = VARCHAR},
            #{item.ruleId,jdbcType = VARCHAR},
            #{item.modifyPerson,jdbcType = VARCHAR},
            #{item.modifyDate,jdbcType = VARCHAR},
            #{item.chkTypeId,jdbcType = VARCHAR},
            #{item.chkTypeName,jdbcType = VARCHAR},
            #{item.centerId,jdbcType = VARCHAR},
            #{item.centerName,jdbcType = VARCHAR},
            #{item.modleId,jdbcType = VARCHAR},
            #{item.modleName,jdbcType = VARCHAR},
            #{item.ruleName,jdbcType = VARCHAR},
            #{item.ruleSql,jdbcType = VARCHAR},
            #{item.limitValue,jdbcType = VARCHAR},
            #{item.sqlValue,jdbcType = VARCHAR},
            #{item.status,jdbcType = VARCHAR},
            #{item.conclusion,jdbcType = VARCHAR},
            #{item.execMsec,jdbcType = VARCHAR},
            #{item.execRemark,jdbcType = VARCHAR},
            #{item.createDate,jdbcType = VARCHAR},
            #{item.createPerson,jdbcType = VARCHAR},
            #{item.execDate,jdbcType = VARCHAR},
            #{item.execPerson,jdbcType = VARCHAR},
            #{item.analysisRes,jdbcType = VARCHAR}
            from dual
        </foreach>
    </insert>

    <insert id="importAuditData" parameterType="java.util.List" useGeneratedKeys="false">
        insert into RPT_CHECK_CFG(
        RULE_ID,
        RULE_NAME,
        STATUS,
        CREATE_PERSON,
        CREATE_DATE,
        MODIFY_PERSON,
        MODIFY_DATE,
        CHK_TYPE_ID,
        CENTER_ID,
        MODLE_ID,
        RULE_SQL,
        LIMIT_VALUE,
        RULE_DESC,
        ENV_TYPE)
        <foreach collection="list" item="item" index="index" separator="union all">
            select
            #{item.ruleId},
            #{item.ruleName,jdbcType = VARCHAR},
            #{item.status,jdbcType = VARCHAR},
            #{item.createPerson,jdbcType = VARCHAR},
            #{item.createDate,jdbcType = VARCHAR},
            #{item.modifyPerson,jdbcType = VARCHAR},
            #{item.modifyDate,jdbcType = VARCHAR},
            #{item.chkTypeId,jdbcType = VARCHAR},
            #{item.centerId,jdbcType = VARCHAR},
            #{item.modleId,jdbcType = VARCHAR},
            #{item.ruleSql,jdbcType = VARCHAR},
            #{item.limitValue,jdbcType = VARCHAR},
            #{item.ruleDesc,jdbcType = VARCHAR},
            #{item.envType,jdbcType = VARCHAR}
            from dual
        </foreach>
    </insert>


    <select id="queryHisConfigByRuleId" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">

        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE
        from (
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG_HIS where rule_id = #{ruleId}
        <if test="modifyPerson != '' and modifyPerson != null">
            and MODIFY_PERSON like concat(concat('%',#{modifyPerson, jdbcType=VARCHAR}),'%')
        </if>
        <if test="ruleName != '' and ruleName != null">
            and RULE_NAME like concat(concat('%',#{ruleName, jdbcType=VARCHAR}),'%')
        </if>
        order by modify_date desc nulls last
        ) where rownum = 1
    </select>

    <select id="queryJobStatus" resultType="java.util.Map">
        select to_char(NEXT_DATE,'yyyyMMdd') NEXT_DATE,
               FAILURES
        from dba_jobs
        where JOB = '469'
    </select>


    <!--首页度量查询语句-->
    <select id="queryDataDistribution" resultType="java.util.Map">
        select t.MODLE_ID MODLEID,t1.DICT_NAME NAME,count(*) VALUE FROM RPT_CHECK_CFG t,PUBLIC_RPT_DICT t1
        WHERE t1.dict_type_id=12 AND t1.DICT_VALUE =t.MODLE_ID GROUP BY t1.DICT_NAME ,t.MODLE_ID
    </select>

    <select id="qryAllAuditDictByActived" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where STATUS='1' order by DICT_ID desc
    </select>
    <select id="qryAllAuditDictByDictId" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_ID = #{dictId} and  STATUS='1' order by DICT_ID desc
    </select>

    <select id="queryRptCheckCfgByRuleIdWithActived" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where MODLE_ID = #{dictId} and STATUS = '1'
    </select>
    <select id="qryAuditDictByDictValue" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_VALUE = #{dictValue} and  STATUS='1' order by DICT_ID desc
    </select>

    <select id="qryAuditDictByDictValueWithoutStatus" resultType="com.suke.czx.newland.vo.audit.AuditDictVo">
        select DICT_ID,DICT_TYPE_ID,DICT_VALUE,DICT_NAME,DICT_DESC,STATUS,CREATE_DATE,CREATE_PERSON,MODIFY_DATE,MODIFY_PERSON,CRON_EXPRESSION
        from PUBLIC_RPT_DICT where DICT_VALUE = #{dictValue} order by DICT_ID desc
    </select>
    <select id="qryAuditCfgWithRuleIds" resultType="com.suke.czx.newland.vo.audit.AuditConfigVo">
        select RULE_ID,RULE_NAME,STATUS,CREATE_PERSON,CREATE_DATE,MODIFY_PERSON,MODIFY_DATE,CHK_TYPE_ID,CENTER_ID,MODLE_ID,RULE_SQL,LIMIT_VALUE,RULE_DESC,ENV_TYPE from RPT_CHECK_CFG where 1=1
        and RULE_ID in
            <foreach collection="array" item="ruleId" open="(" close=")" separator=",">
                #{ruleId}
            </foreach>
    </select>
    <select id="qryAuditDateWithDuration" resultType="com.suke.czx.newland.dto.CheckLogAnalysisDto">
        select t.MODLE_NAME as modleName ,count(*) as countVal ,t.ANALYSIS_RES as analysisRes from
            RPT_CHECK_lOG t WHERE
            1=1
            and EXEC_DATE > sysdate - #{duration}
        GROUP BY t.MODLE_NAME,t.ANALYSIS_RES
    </select>
</mapper>
