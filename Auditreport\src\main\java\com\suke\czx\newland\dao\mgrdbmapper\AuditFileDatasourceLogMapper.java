package com.suke.czx.newland.dao.mgrdbmapper;

import com.suke.czx.newland.dto.AuditFileDatasourceLogQryDto;
import com.suke.czx.newland.dto.AuditFileDatasourceLogResDto;
import com.suke.czx.newland.po.AuditFileDatasourceLogPo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AuditFileDatasourceLogMapper {

    int addBatchAuditFileDatasourceLog(List<AuditFileDatasourceLogPo> records);

    List<AuditFileDatasourceLogResDto> qryAuditFileDatasourceLogByCondition(AuditFileDatasourceLogQryDto condition);

    int delBatchAuditFileDatasourceLog(List<String> logIds);


}
