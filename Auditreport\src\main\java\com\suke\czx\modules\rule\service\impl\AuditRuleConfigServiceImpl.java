package com.suke.czx.modules.rule.service.impl;

import com.suke.czx.modules.rule.dao.AuditRuleConfigDao;
import com.suke.czx.modules.rule.entity.AuditRuleConfig;
import com.suke.czx.modules.rule.service.AuditRuleConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class AuditRuleConfigServiceImpl implements AuditRuleConfigService {

    @Autowired
    private AuditRuleConfigDao auditRuleConfigDao;


    /**
     * 根据规则表的父id查询规则子表
     * @return
     */
    @Override
    public List<AuditRuleConfig> queryRuleConfigList(String faId,String type) {
        List<AuditRuleConfig> auditRuleConfigList = auditRuleConfigDao.queryRuleConfigList(faId,type);
        return auditRuleConfigList;
    }

    /**
     * 更新规则信息（子表）
     * @param auditRuleConfig
     * @return
     */
    @Override
    public Integer updateRuleConfig(AuditRuleConfig auditRuleConfig) {
        return auditRuleConfigDao.update(auditRuleConfig);
    }

    /**
     *
     * @param id
     * @return
     */
    @Override
    public Integer delRuleConfigByid(Long id) {
        return auditRuleConfigDao.delete(id);
    }

    @Override
    public void delRuleConfigByIds(String[] ids) {
        auditRuleConfigDao.delRuleConfigByIds(ids);
    }

    /**
     * 根据id获取规则的详细信息
     * @param id
     * @return
     */
    @Override
    public AuditRuleConfig queryRuleConfigInfoById(Long id) {
        return auditRuleConfigDao.queryObject(id);
    }

    /**
     * 保存单条规则信息，在页面点击添加规则按钮（向规则子表中）
     * @param auditRuleConfig
     * @return
     */
    @Override
    public void addRuleConfig(AuditRuleConfig auditRuleConfig) {
        auditRuleConfigDao.saveRuleConfig(auditRuleConfig);
    }

    /**
     * 通过id查询子规则表中
     * @param faId
     * @return
     */
    @Override
    public List<AuditRuleConfig> getAuditRuleById(String faId) {
        return auditRuleConfigDao.getAuditRuleById(faId);
    }
}
