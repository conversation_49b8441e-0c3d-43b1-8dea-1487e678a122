package com.suke.czx.modules.audit.rules;

import com.suke.czx.modules.audit.rules.dto.SheetBaseDto;
import com.suke.czx.modules.audit.rules.dto.SheetNotLikeDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.Map;

public class NotLikeHandler extends ExcelHandleRuler {
    @Override
    public Map<String, Object> sheetHandle(SheetBaseDto param) {
        SheetNotLikeDto condition = (SheetNotLikeDto) param;
        Sheet sheet = condition.getSheet();
        int notLikeColumn = condition.getColumn();
        String rule = condition.getRule();
        String[] ruleArr = rule.split(",");
        String notLikeKeyword = ruleArr[0];
        String position = ruleArr[1];

        boolean res = true;
        for (int i = condition.getStartRow(); i <= condition.getEndRow(); i++) {
            Row oneRow = sheet.getRow(i);
            Cell oneRowTargetCell = oneRow.getCell(notLikeColumn);
            setCellStr(oneRowTargetCell);
            if (!isCellStrMatchRule(oneRowTargetCell.getStringCellValue(), position, notLikeKeyword)) {
                setCellRed(oneRowTargetCell, condition.getCellStyle());
                res = false;
            }
        }

        return res ? success() : fail();
    }

    private boolean isCellStrMatchRule(String cellStr, String position, String keyword) {
        if (cellStr == null || cellStr.isEmpty()) {
            return false;
        }
        if ("start".equals(position)) {
            return !cellStr.startsWith(keyword);
        } else if ("mid".equals(position)) {
            return cellStr.startsWith(keyword) || cellStr.endsWith(keyword) || !cellStr.contains(keyword);
        } else if ("end".equals(position)) {
            return !cellStr.endsWith(keyword);
        }
        return false;
    }
}
