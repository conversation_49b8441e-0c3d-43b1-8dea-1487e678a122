package com.suke.czx.newland.controller;

import com.suke.czx.newland.common.Result;
import com.suke.czx.newland.dto.DatasourceInfoDto;
import com.suke.czx.newland.service.DatasourceService;
import com.suke.czx.newland.util.CryptoUtil;
import com.suke.czx.newland.util.DataSourceUtil;
import com.suke.czx.newland.vo.DatasourceInfoAddVo;
import com.suke.czx.newland.vo.DatasourceInfoQryVo;
import com.suke.czx.newland.vo.DatasourceInfoUpdateVo;
import com.suke.czx.newland.vo.DatasourceTestConnectVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/datasource")
public class DatasourceController {


    private final DatasourceService datasourceService;

    public DatasourceController(DatasourceService datasourceService) {
        this.datasourceService = datasourceService;
    }

    @PostMapping("/addDatasource")
    public Result addDatasource(@RequestBody DatasourceInfoAddVo datasourceInfo) {
        try {
            DatasourceInfoDto param = new DatasourceInfoDto();
            param.setDatasourceName(datasourceInfo.getDatasourceName());
            param.setDatasourceType(datasourceInfo.getDatasourceType());
            param.setUrl(datasourceInfo.getUrl());
            param.setDatasourceUsername(datasourceInfo.getDatasourceUsername());
            param.setDatasourcePassword(datasourceInfo.getDatasourcePassword());
            datasourceService.addDatasource(param);
        }catch (Exception e){
            return new Result().setCode(10001).setMsg(e.getMessage());
        }
        return new Result().setCode(10000);
    }

    @PostMapping("/qryDatasourceWithPageInfo")
    public Result qryDatasourceWithPageInfo(@RequestBody DatasourceInfoQryVo qryCondition, @RequestParam int pageIndex, @RequestParam int pageSize) {
        return new Result().setCode(10000).setData(datasourceService.qryDatasourceWithPageInfo(qryCondition, pageIndex, pageSize));
    }

    @PostMapping("/updateDatasourceByDatasourceId")
    public Result updateDatasourceByDatasourceId(@RequestBody DatasourceInfoUpdateVo updateCondition) {
        DatasourceInfoDto param = new DatasourceInfoDto();
        param.setDatasourceId(updateCondition.getDatasourceId());
        param.setDatasourceName(updateCondition.getDatasourceName());
        param.setDatasourceType(updateCondition.getDatasourceType());
        param.setUrl(updateCondition.getUrl());
        param.setDatasourceUsername(updateCondition.getDatasourceUsername());
        param.setDatasourcePassword(updateCondition.getDatasourcePassword());
        datasourceService.updateDatasource(param);

        return new Result().setCode(10000);
    }

    @PostMapping("/deleteDatasourceByDatasourceId")
    public Result deleteDatasourceByDatasourceId(@RequestParam String datasourceId) {
        datasourceService.deleteDatasourceByDatasourceId(datasourceId);
        return new Result().setCode(10000);
    }

    @PostMapping("/testDatasourceWithDatasourceId")
    public Result testDatasource(@RequestParam String datasourceId) {
        try {
            datasourceService.testDatasourceConnection(datasourceId);
        } catch (Exception e) {
            return new Result().setCode(10001).setData(e.getMessage());
        }
        return new Result().setCode(10000);
    }

    @PostMapping("/testDatasourceWithInfo")
    public Result testDatasourceWithInfo(@RequestBody DatasourceTestConnectVo datasourceInfo) {
        try {
            datasourceService.testConnection(DataSourceUtil.getDriver(datasourceInfo.getDatasourceType()), datasourceInfo.getUrl(), datasourceInfo.getDatasourceUsername(), CryptoUtil.decrypt(datasourceInfo.getDatasourcePassword()));
        } catch (Exception e) {
            return new Result().setCode(10001).setData(e.getMessage());
        }
        return new Result().setCode(10000);
    }

    @PostMapping("/qryAllDatasourceActivated")
    public Result qryAllDatasourceActivated() {
        return new Result().setCode(10000).setData(datasourceService.qryAllDatasourceActived());
    }
}
